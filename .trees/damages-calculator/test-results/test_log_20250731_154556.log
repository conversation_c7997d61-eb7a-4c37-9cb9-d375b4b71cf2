[0;34m[2025-07-31 15:45:56][0m Starting Comprehensive API Testing...
[0;34m[2025-07-31 15:45:56][0m API Base URL: https://api.pilawyer.ai
[0;34m[2025-07-31 15:45:56][0m Frontend URL: https://pilawyer.ai
[0;34m[2025-07-31 15:45:56][0m Checking environment configuration...
[1;33m⚠️ SUPABASE_URL not set - some tests may fail[0m
[1;33m⚠️ SUPABASE_ANON_KEY not set - authentication tests may fail[0m
[1;33m⚠️ STRIPE_PUBLISHABLE_KEY not set - payment tests may fail[0m
[0;32m✅ Environment check completed[0m
[0;34m[2025-07-31 15:45:56][0m === Phase 1: Basic API Health Checks ===
[0;34m[2025-07-31 15:45:56][0m Running test: Frontend Health Check
[0;31m❌ FAILED: Frontend Health Check - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: API Health Check
[0;31m❌ FAILED: API Health Check - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Backend Health Check
[0;31m❌ FAILED: Backend Health Check - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: CORS Preflight Check
[0;31m❌ FAILED: CORS Preflight Check - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m === Phase 2: Authentication Testing ===
[0;34m[2025-07-31 15:45:56][0m Running test: Unauthenticated Access Rejection
[0;31m❌ FAILED: Unauthenticated Access Rejection - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Admin Endpoint Protection
[0;31m❌ FAILED: Admin Endpoint Protection - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Invalid Token Rejection
[0;31m❌ FAILED: Invalid Token Rejection - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Malformed Auth Header Rejection
[0;31m❌ FAILED: Malformed Auth Header Rejection - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m === Phase 3: Subscription Management API Testing ===
[0;34m[2025-07-31 15:45:56][0m Running test: US Pricing Retrieval
[0;31m❌ FAILED: US Pricing Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Belgium Pricing Retrieval
[0;31m❌ FAILED: Belgium Pricing Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: UK Pricing Retrieval
[0;31m❌ FAILED: UK Pricing Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Canada Pricing Retrieval
[0;31m❌ FAILED: Canada Pricing Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Invalid Country Code
[0;31m❌ FAILED: Invalid Country Code - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Invalid Currency Code
[0;31m❌ FAILED: Invalid Currency Code - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Subscription Plans Retrieval
[0;31m❌ FAILED: Subscription Plans Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Subscription Addons Retrieval
[0;31m❌ FAILED: Subscription Addons Retrieval - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m === Phase 4: Payment Methods API Testing ===
[0;34m[2025-07-31 15:45:56][0m Running test: US Payment Methods
[0;31m❌ FAILED: US Payment Methods - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Belgium Payment Methods
[0;31m❌ FAILED: Belgium Payment Methods - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: UK Payment Methods
[0;31m❌ FAILED: UK Payment Methods - Command execution failed[0m
[0;34m[2025-07-31 15:45:56][0m Running test: Canada Payment Methods
[0;31m❌ FAILED: Canada Payment Methods - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: SEPA Validation
[0;31m❌ FAILED: SEPA Validation - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Card Validation
[0;31m❌ FAILED: Card Validation - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Invalid Payment Method Type
[0;31m❌ FAILED: Invalid Payment Method Type - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m === Phase 5: Compliance API Testing ===
[0;34m[2025-07-31 15:45:57][0m Running test: Data Residency Detection
[0;31m❌ FAILED: Data Residency Detection - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Available Regions
[0;31m❌ FAILED: Available Regions - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: US Legal Disclaimers
[0;31m❌ FAILED: US Legal Disclaimers - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: EU Legal Disclaimers
[0;31m❌ FAILED: EU Legal Disclaimers - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Consent Types
[0;31m❌ FAILED: Consent Types - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m === Phase 6: Error Handling Validation ===
[0;34m[2025-07-31 15:45:57][0m Running test: Malformed JSON Handling
[0;31m❌ FAILED: Malformed JSON Handling - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Missing Required Fields
[0;31m❌ FAILED: Missing Required Fields - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Invalid Content Type
[0;31m❌ FAILED: Invalid Content Type - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Method Not Allowed
[0;31m❌ FAILED: Method Not Allowed - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m Running test: Non-existent Endpoint
[0;31m❌ FAILED: Non-existent Endpoint - Command execution failed[0m
[0;34m[2025-07-31 15:45:57][0m === Phase 7: Rate Limiting Testing ===
[0;34m[2025-07-31 15:45:57][0m Testing rate limiting (this may take a moment)...
