# Database Schema Fixes and Clarifications

## Overview

This document outlines the database schema issues discovered and fixed during the Next.js 15 compatibility updates, specifically related to security logging and device management functions.

## Security Events Tables

### Issue: Multiple Security Events Tables

We discovered there are **two different security events tables** in the database:

1. **`security.events`** - Primary table for application security logging
2. **`security.security_events`** - Enhanced middleware logging table

### Table: `security.events` (Primary)

**Correct Schema** (verified via Supabase MCP):
```sql
CREATE TABLE security.events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    tenant_id UUID,
    ip_address TEXT,
    user_agent TEXT,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

**Key Points:**
- ✅ Uses `created_at` as timestamp column (NOT `timestamp`)
- ✅ Stores additional data in `details` JSONB column
- ✅ Used by application security logging (forensics.ts, auth-security-logger.ts)

### Table: `security.security_events` (Enhanced Middleware)

**Schema** (from migration 20250309):
```sql
CREATE TABLE security.security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    request_url TEXT NOT NULL,
    request_method TEXT NOT NULL,
    request_headers JSONB DEFAULT '{}',
    event_details JSONB DEFAULT '{}',
    user_id UUID REFERENCES auth.users(id),
    tenant_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

**Key Points:**
- ✅ More detailed schema for middleware logging
- ✅ Includes request-specific fields (url, method, headers)
- ✅ Used by enhanced middleware security logging

## Device Management Functions

### Issue: Incorrect Function Parameters

**Problem**: Code was calling device registration functions with wrong parameters.

### Function: `register_device`

**Correct Signature** (verified via Supabase MCP):
```sql
public.register_device(
    p_fingerprint TEXT,
    p_user_agent TEXT,
    p_ip_address TEXT DEFAULT NULL
) RETURNS UUID
```

**Fixed Usage**:
```typescript
const { data, error } = await supabase.rpc('register_device', {
    p_fingerprint: deviceFp,
    p_user_agent: navigator.userAgent,
    p_ip_address: null // Server-side capture
});
```

### Function: `name_device`

**Correct Signature** (verified via Supabase MCP):
```sql
public.name_device(
    p_fingerprint TEXT,
    p_device_name TEXT
) RETURNS UUID
```

**Usage** (fallback for device naming):
```typescript
const { data, error } = await supabase.rpc('name_device', {
    p_fingerprint: deviceFp,
    p_device_name: deviceName
});
```

## Fixes Applied

### 1. Security Logging Schema Fix

**Files Updated:**
- `frontend/src/lib/security/forensics.ts`
- `frontend/src/app/api/security/log-event/route.ts`
- `frontend/src/lib/security/auth-security-logger.ts`

**Changes:**
- ✅ Changed `timestamp` → `created_at` in database inserts
- ✅ Fixed table reference from `security_events` → `security.events`
- ✅ Restructured payload to match schema (moved extra fields to `details` JSONB)
- ✅ Updated details object field names for consistency

### 2. Device Registration Function Fix

**Files Updated:**
- `frontend/src/lib/security/context.tsx`
- `supabase/DEVICE_MANAGEMENT_GUIDE.md`

**Changes:**
- ✅ Fixed function parameters from `(p_fingerprint, p_device_name)` → `(p_fingerprint, p_user_agent, p_ip_address)`
- ✅ Removed incorrect `.schema('security')` call
- ✅ Added proper user agent detection with SSR fallback
- ✅ Updated documentation to reflect actual database functions

### 3. Async/Await Compatibility

**Files Updated:**
- `frontend/src/lib/auth/getUnifiedSession.ts`
- `frontend/src/app/api/security/log-event/route.ts`

**Changes:**
- ✅ Added `await` to `createServerClientForUser()` calls
- ✅ Added `await` to `createClient()` calls in API routes
- ✅ Fixed Next.js 15 async cookies compatibility

## Verification Methods

### Database Schema Verification
```typescript
// Using Supabase MCP server to verify actual schema
const { data } = await supabase.rpc('query', {
    query: `SELECT column_name, data_type FROM information_schema.columns 
            WHERE table_schema = 'security' AND table_name = 'events'`
});
```

### Function Signature Verification
```typescript
// Using Supabase MCP server to verify function definitions
const { data } = await supabase.rpc('query', {
    query: `SELECT pg_get_functiondef(oid) FROM pg_proc 
            WHERE proname = 'register_device'`
});
```

## Expected Results

### ✅ Resolved Errors:
- **PGRST204**: "Could not find the 'timestamp' column of 'events'"
- **Device Registration**: "Error naming device" in SecurityProvider
- **Async/Await**: "Cannot read properties of undefined (reading 'getSession')"

### ✅ Improved Functionality:
- Security logging properly inserts into correct tables
- Device registration works on first attempt (no fallback needed)
- Session management stable across server components

## Migration Notes

If you need to apply these schema fixes to a new environment:

1. **Ensure both security events tables exist** (check migrations)
2. **Verify function signatures** using MCP queries
3. **Update application code** to use correct column names and parameters
4. **Test security logging** to ensure events are properly recorded

## Related Documentation

- `supabase/DEVICE_MANAGEMENT_GUIDE.md` - Updated device function documentation
- `supabase/SECURITY_DOCUMENTATION.md` - General security documentation
- `docs/security/DATABASE_SCHEMA.md` - Detailed schema documentation
