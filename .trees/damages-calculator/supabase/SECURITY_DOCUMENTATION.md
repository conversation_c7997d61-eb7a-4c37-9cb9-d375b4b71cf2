# Security Documentation

This document provides a comprehensive overview of the security features, implementation details, and best practices for the PI Lawyer AI platform.

## Table of Contents

1. [Authentication System](#authentication-system)
2. [Device Management Security](#device-management-security)
3. [Database Security](#database-security)
4. [Audit Logging](#audit-logging)
5. [Security Event Monitoring](#security-event-monitoring)
6. [Multi-tenant Data Isolation](#multi-tenant-data-isolation)
7. [Session Management](#session-management)
8. [Security Administration](#security-administration)
9. [Troubleshooting Guide](#troubleshooting-guide)

## Authentication System

### JWT-based Authentication

The application uses Supabase Auth with JWT tokens and the PKCE (Proof Key for Code Exchange) flow for secure authentication:

- **PKCE Flow**: Provides better protection than implicit flow, especially for SPA applications
- **JWT Customization**: Custom claims including `role` and `tenant_id` at the root level of the JWT
- **Token Expiration**: Different expiration times based on device trust status
- **Auto-refresh**: Tokens automatically refresh to maintain session continuity

### JWT Claims

The JWT contains the following important claims:

- `role`: User's role (e.g., 'attorney', 'admin', 'staff')
- `tenant_id`: The tenant identifier for multi-tenant data isolation
- Standard claims: `exp` (expiration), `iat` (issued at), `sub` (subject)

These claims are used directly by Row-Level Security policies to enforce data access control.

### Implementation Details

Client configuration in `frontend/src/lib/supabase/provider.tsx`:

```javascript
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    detectSessionInUrl: true,
    flowType: 'pkce',
    autoRefreshToken: true,
    persistSession: true,
    storageKey: 'supabase.auth.token',
  },
  global: {
    headers: {
      'X-Client-Info': 'pi-lawyer-ai-frontend',
    },
  },
  db: {
    schema: 'public'
  }
})
```

## Device Management Security

### Device Fingerprinting

The system implements browser fingerprinting to identify and track devices:

- Generates a unique device fingerprint based on browser characteristics
- Associates fingerprints with user accounts
- Maintains device trust status

### Device Trust Levels

Devices can have the following trust statuses:

- **Untrusted**: Default status for new devices
- **Trusted**: Explicitly trusted by the user, allows longer sessions
- **Blocked**: Explicitly blocked by the user, prevents any login attempts

### Device Registration

When a user logs in from a new device:

1. The device fingerprint is generated
2. The device is registered in the `security.device_fingerprints` table
3. The user is notified of the new device login
4. The device starts as untrusted with shorter session timeouts

### Device Management UI

Users can manage their devices through the Settings page:

- View all devices associated with their account
- Trust or block specific devices
- See last activity timestamp and device details

## Database Security

### Row-Level Security (RLS)

The database uses PostgreSQL Row-Level Security to enforce data access controls:

- **Tenant Isolation**: Data is isolated by tenant_id
- **User-Specific Policies**: Users can only access their own data
- **Role-Based Policies**: Different roles have different access levels

### Security Schema

A dedicated `security` schema contains security-related functions and tables:

- `security.device_fingerprints`: Stores device information
- `security.events`: Logs security events
- `security.revoked_sessions`: Tracks revoked JWT tokens

### Public Schema Aliases

For improved accessibility, public schema aliases are provided for security functions:

```sql
CREATE OR REPLACE FUNCTION public.register_device(
  p_fingerprint TEXT,
  p_user_agent TEXT
) RETURNS BOOLEAN AS $$
  SELECT security.register_device(p_fingerprint, p_user_agent);
$$ LANGUAGE SQL SECURITY DEFINER;
```

## Audit Logging

### pgAudit Configuration

The system uses PostgreSQL Audit Extension (pgAudit) with the following settings:

```sql
ALTER SYSTEM SET pgaudit.log = 'write, ddl';
ALTER SYSTEM SET pgaudit.log_catalog = 'on';
ALTER SYSTEM SET pgaudit.log_client = 'on';
ALTER SYSTEM SET pgaudit.log_parameter = 'on';
ALTER SYSTEM SET pgaudit.log_statement_once = 'on';
ALTER SYSTEM SET pgaudit.role = 'rds_pgaudit';
```

### Custom Audit Tables

Custom audit tables capture and structure audit data:

- `audit.log`: Stores raw pgAudit output
- Database triggers process pgAudit logs

### User Activity Auditing

The system tracks the following user activities:

- Authentication events (login, logout, password reset)
- Profile changes
- Security setting modifications
- Data access and modification events

### Retention Policy

Audit logs are retained according to the following policy:

- Standard audit logs: 90 days
- Security event logs: 180 days

## Security Event Monitoring

### Suspicious Activity Detection

The system automatically detects the following suspicious activities:

- **Failed Login Attempts**: Alerts after 5 failed logins in 15 minutes
- **Account Takeover**: Detects logins from unusual locations
- **Unusual Access Patterns**: Monitors for abnormal access frequency

### Security Event Logging

All security events are logged in `security.events` with the following details:

- Event type
- User ID
- IP address
- User agent
- **created_at** (timestamp column)
- Detailed event information (stored as JSONB)

**Important**: The table uses `created_at` as the timestamp column, not `timestamp`. All logging code should use `created_at` for consistency with the database schema.

### Automated Responses

The system implements automated responses to security threats:

- **Session Termination**: Automatically terminates sessions for suspicious activity
- **Account Locking**: Temporarily locks accounts after multiple failed attempts
- **Notification**: Alerts users of suspicious activity

## Multi-tenant Data Isolation

### Tenant Architecture

The system implements multi-tenancy via:

- Dedicated schema for tenant data (`tenants` schema)
- JWT claims containing tenant_id
- RLS policies enforcing tenant separation

### Tenant Isolation

Data is isolated between tenants through:

- Schema-level separation
- Row-level security policies
- JWT claim validation

### Cross-tenant Access Control

Administrative access across tenants is controlled by:

- Role-based permissions
- Explicit cross-tenant access grants
- Audit logging of all cross-tenant access

## Session Management

### Session Timeouts

The application implements the following session timeouts:

- Untrusted devices: 1 hour
- Trusted devices: 7 days
- Admin roles: 4 hours regardless of device trust

### Session Revocation

Sessions can be revoked in the following ways:

- Explicit logout
- Password change (revokes all sessions)
- Role change (revokes all sessions)
- Admin revocation
- Device blocking (revokes all sessions from that device)

### Client-side Session Checks

The frontend implements regular session checks:

```javascript
// Check session timeout
const checkSessionTimeout = async () => {
  try {
    const { data, error } = await supabase.rpc(
      'is_session_timed_out',
      {},
      { schema: 'public' }
    )

    if (data === true) {
      await supabase.auth.signOut()
      router.push('/login?reason=timeout')
    }
  } catch (error) {
    console.error('Error checking session timeout:', error)
  }
}

// Check every minute
const interval = setInterval(checkSessionTimeout, 60000)
```

## Security Administration

### Security Dashboard

Administrators have access to a security dashboard that shows:

- Recent security events
- Failed login attempts
- Suspicious activity alerts
- Device trust status across users

### Security Functions

The following security administration functions are available:

- `security.revoke_user_sessions(user_id)`: Revokes all sessions for a user
- `security.block_device(fingerprint)`: Blocks a device across all users
- `security.reset_user_devices(user_id)`: Resets all device associations for a user

## Troubleshooting Guide

### Common Issues and Solutions

**Issue**: Authentication middleware redirects to login

**Solution**: Check the following:
1. Verify JWT token expiration using browser developer tools
2. Ensure cookies are being properly set and maintained
3. Check for JWT claim issues in the Supabase configuration

**Issue**: Device trust status not being recognized

**Solution**:
1. Verify device fingerprint generation in browser console
2. Check device registration in database
3. Clear browser cookies and localStorage, then re-login

**Issue**: Security schema function access issues

**Solution**:
1. Verify search_path includes the security schema
2. Check that public aliases for security functions exist
3. Run the following to fix search path issues:
   ```sql
   ALTER DATABASE postgres SET search_path TO public, extensions, security;
   ```

**Issue**: pgAudit logs not appearing

**Solution**:
1. Verify pgAudit extension is enabled
2. Check configuration parameters are properly set
3. Ensure the database has been restarted after configuration changes

### Security Debugging Tools

The following tools are available for security debugging:

- `window.debugSupabaseSession()`: Browser console function to debug JWT and session state
- `security.test_device_functions()`: Database function to test device management functions
- Security log viewer in the admin dashboard
