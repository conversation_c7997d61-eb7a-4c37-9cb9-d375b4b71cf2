# Device Management Implementation Guide

This document provides comprehensive details about the device management security features implemented in the PI Lawyer AI platform.

## Table of Contents

1. [Overview](#overview)
2. [Database Schema](#database-schema)
3. [Security Functions](#security-functions)
4. [Frontend Implementation](#frontend-implementation)
5. [Integration with Authentication](#integration-with-authentication)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Overview

The device management system provides the following security features:

- Device fingerprinting and tracking
- Device trust status management
- Session length controls based on device trust
- Device blocking capabilities
- Comprehensive audit logging of device activities

### Security Goals

- Prevent unauthorized access from unknown devices
- Allow trusted devices to have longer session times
- Enable users to manage their own devices
- Track and log device usage for security auditing
- Identify and block suspicious devices

## Database Schema

### Device Fingerprints Table

The core of the device management system is the `security.device_fingerprints` table:

```sql
CREATE TABLE security.device_fingerprints (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  fingerprint TEXT NOT NULL,
  device_name TEXT,
  user_agent TEXT,
  ip_address TEXT,
  first_seen TIMESTAMPTZ NOT NULL DEFAULT now(),
  last_seen TIMESTAMPTZ NOT NULL DEFAULT now(),
  is_trusted BOOLEAN NOT NULL DEFAULT false,
  is_blocked BOOLEAN NOT NULL DEFAULT false,
  UNIQUE(user_id, fingerprint)
);

-- Add RLS policies to ensure users can only see their own devices
ALTER TABLE security.device_fingerprints ENABLE ROW LEVEL SECURITY;

CREATE POLICY view_own_devices ON security.device_fingerprints
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY update_own_devices ON security.device_fingerprints
  FOR UPDATE USING (user_id = auth.uid());
```

### Schema Migrations

The device management system is deployed through several migrations:

1. `20250301_security_phase1.sql`: Creates initial security schema and tables
2. `20250301_add_device_management_functions.sql`: Adds core device management functions
3. `20250301_add_public_device_functions.sql`: Creates public schema aliases for security functions
4. `20250301_add_get_user_devices.sql`: Adds function to retrieve user devices

## Security Functions

### Core Functions

The device management system provides the following core functions:

#### Register Device

```sql
CREATE OR REPLACE FUNCTION security.register_device(
  p_fingerprint TEXT,
  p_user_agent TEXT DEFAULT current_setting('request.headers', true)::json->>'user-agent',
  p_ip_address TEXT DEFAULT current_setting('request.headers', true)::json->>'x-forwarded-for'
) RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_device_name TEXT;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- If no authenticated user, return false
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;

  -- Generate a device name from user agent
  v_device_name := COALESCE(
    p_user_agent,
    'Unknown Device'
  );

  -- Insert or update the device fingerprint
  INSERT INTO security.device_fingerprints (
    user_id,
    fingerprint,
    device_name,
    user_agent,
    ip_address
  ) VALUES (
    v_user_id,
    p_fingerprint,
    v_device_name,
    p_user_agent,
    p_ip_address
  )
  ON CONFLICT (user_id, fingerprint) DO UPDATE SET
    last_seen = now(),
    user_agent = COALESCE(p_user_agent, security.device_fingerprints.user_agent),
    ip_address = COALESCE(p_ip_address, security.device_fingerprints.ip_address);

  -- Log the device registration event
  PERFORM security.log_event(
    'device.registered',
    jsonb_build_object(
      'fingerprint', p_fingerprint,
      'user_agent', p_user_agent,
      'ip_address', p_ip_address
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Trust Device

```sql
CREATE OR REPLACE FUNCTION security.trust_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_rows_affected INTEGER;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- If no authenticated user, return false
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;

  -- Update the device trust status
  UPDATE security.device_fingerprints
  SET
    is_trusted = true,
    is_blocked = false,
    last_seen = now()
  WHERE
    user_id = v_user_id AND
    fingerprint = p_fingerprint
  RETURNING 1 INTO v_rows_affected;

  -- If no rows were affected, the device doesn't exist for this user
  IF v_rows_affected IS NULL OR v_rows_affected = 0 THEN
    RETURN false;
  END IF;

  -- Log the trust event
  PERFORM security.log_event(
    'device.trusted',
    jsonb_build_object(
      'fingerprint', p_fingerprint
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Block Device

```sql
CREATE OR REPLACE FUNCTION security.block_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_rows_affected INTEGER;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- If no authenticated user, return false
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;

  -- Update the device trust status
  UPDATE security.device_fingerprints
  SET
    is_blocked = true,
    is_trusted = false,
    last_seen = now()
  WHERE
    user_id = v_user_id AND
    fingerprint = p_fingerprint
  RETURNING 1 INTO v_rows_affected;

  -- If no rows were affected, the device doesn't exist for this user
  IF v_rows_affected IS NULL OR v_rows_affected = 0 THEN
    RETURN false;
  END IF;

  -- Log the block event
  PERFORM security.log_event(
    'device.blocked',
    jsonb_build_object(
      'fingerprint', p_fingerprint
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Check Device Trust Status

```sql
CREATE OR REPLACE FUNCTION security.is_trusted_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_is_trusted BOOLEAN;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- If no authenticated user, return false
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;

  -- Check if the device is trusted
  SELECT is_trusted INTO v_is_trusted
  FROM security.device_fingerprints
  WHERE
    user_id = v_user_id AND
    fingerprint = p_fingerprint AND
    is_blocked = false;

  -- Return false if not found or not trusted
  RETURN COALESCE(v_is_trusted, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Get User Devices

```sql
CREATE OR REPLACE FUNCTION security.get_user_devices()
RETURNS SETOF security.device_fingerprints AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- If no authenticated user, return empty set
  IF v_user_id IS NULL THEN
    RETURN;
  END IF;

  -- Return the user's devices
  RETURN QUERY
  SELECT *
  FROM security.device_fingerprints
  WHERE user_id = v_user_id
  ORDER BY last_seen DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Public Schema Aliases

For improved accessibility, public schema aliases are provided:

**Note**: There are two functions for device management:
- `register_device`: Registers a new device with user agent and IP address
- `name_device`: Names/renames an existing device with a custom name

```sql
-- Create public schema aliases for security functions
CREATE OR REPLACE FUNCTION public.register_device(
  p_fingerprint TEXT,
  p_user_agent TEXT,
  p_ip_address TEXT DEFAULT NULL
) RETURNS UUID AS $$
  -- This is a wrapper that calls the security schema function
  -- with proper user and tenant context
$$ LANGUAGE SQL SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.name_device(
  p_fingerprint TEXT,
  p_device_name TEXT
) RETURNS UUID AS $$
  SELECT security.name_device(p_fingerprint, p_device_name);
$$ LANGUAGE SQL SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.trust_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
  Select security.trust_device(p_fingerprint);
$$ LANGUAGE SQL SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.block_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
  SELECT security.block_device(p_fingerprint);
$$ LANGUAGE SQL SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_trusted_device(
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
  SELECT security.is_trusted_device(p_fingerprint);
$$ LANGUAGE SQL SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_user_devices()
RETURNS SETOF security.device_fingerprints AS $$
  SELECT * FROM security.get_user_devices();
$$ LANGUAGE SQL SECURITY DEFINER;
```

## Frontend Implementation

### Device Fingerprinting

The frontend generates device fingerprints using a combination of browser signals:

```typescript
export const generateDeviceFingerprint = (): string => {
  const components = [
    navigator.userAgent,
    navigator.language,
    new Date().getTimezoneOffset(),
    screen.width + 'x' + screen.height + 'x' + screen.colorDepth,
    navigator.platform,
  ];

  // Hash the components to create a fingerprint
  let fingerprint = '';

  try {
    const str = components.join('###');
    let hash = 0;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    fingerprint = Math.abs(hash).toString(36);
  } catch (e) {
    console.error('Error generating fingerprint:', e);
    fingerprint = 'fallback-' + Math.random().toString(36).substring(2);
  }

  return fingerprint;
};
```

### Security Context Provider

The frontend includes a security context provider to manage device security:

```typescript
export function SecurityProvider({ children }: { children: React.ReactNode }) {
  const { supabase } = useSupabase()
  const router = useRouter()
  const [deviceFingerprint, setDeviceFingerprint] = useState<string | null>(null)
  const [isDeviceTrusted, setIsDeviceTrusted] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Generate and register device fingerprint on mount
  useEffect(() => {
    const initializeDevice = async () => {
      try {
        const fingerprint = await registerDevice()

        // Check if device is trusted
        const { data, error } = await supabase.rpc(
          'is_trusted_device',
          { p_fingerprint: fingerprint }
        )

        if (error) {
          console.error('Error checking device trust status:', error)
          setIsDeviceTrusted(false)
        } else {
          setIsDeviceTrusted(data)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error initializing device:', error)
        setIsLoading(false)
      }
    }

    initializeDevice()
  }, [supabase])

  // Register device
  const registerDevice = async () => {
    const fingerprint = generateDeviceFingerprint()
    setDeviceFingerprint(fingerprint)

    // Store in session storage
    sessionStorage.setItem('device_fingerprint', fingerprint)

    // Get device name
    const deviceName = typeof navigator !== 'undefined'
      ? `${navigator.platform} - ${navigator.userAgent.split('(')[0]}`
      : 'Unknown Device'

    // Register with backend
    try {
      const { data, error } = await supabase.rpc(
        'register_device',
        {
          p_fingerprint: fingerprint,
          p_user_agent: navigator.userAgent,
          p_ip_address: null // Will be captured server-side
        }
      )

      if (error) {
        console.error('Error registering device:', error)
      }

      return fingerprint
    } catch (error) {
      console.error('Error registering device:', error)
      return fingerprint
    }
  }

  // Trust this device
  const trustDevice = async () => {
    if (!deviceFingerprint) return false

    try {
      const { data, error } = await supabase.rpc(
        'trust_device',
        { p_fingerprint: deviceFingerprint }
      )

      if (error) {
        console.error('Error trusting device:', error)
        return false
      }

      setIsDeviceTrusted(true)
      return true
    } catch (error) {
      console.error('Error trusting device:', error)
      return false
    }
  }

  // Block a device
  const blockDevice = async (fingerprint?: string) => {
    const fingerprintToBlock = fingerprint || deviceFingerprint

    if (!fingerprintToBlock) return false

    try {
      const { data, error } = await supabase.rpc(
        'block_device',
        { p_fingerprint: fingerprintToBlock }
      )

      if (error) {
        console.error('Error blocking device:', error)
        return false
      }

      // If blocking current device, sign out
      if (fingerprintToBlock === deviceFingerprint) {
        await supabase.auth.signOut()
        router.push('/login?reason=blocked')
      }

      return true
    } catch (error) {
      console.error('Error blocking device:', error)
      return false
    }
  }

  return (
    <SecurityContext.Provider
      value={{
        isDeviceTrusted,
        trustDevice,
        blockDevice,
        registerDevice,
        deviceFingerprint,
        isLoading
      }}
    >
      {children}
    </SecurityContext.Provider>
  )
}
```

### Device Manager Component

The UI component for managing devices:

```typescript
export function DeviceManager() {
  const { supabase } = useSupabase()
  const { deviceFingerprint, blockDevice, trustDevice } = useSecurity()
  const [devices, setDevices] = useState<Device[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch user's devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const { data, error } = await supabase.rpc('get_user_devices')

        if (error) {
          console.error('Error fetching devices:', error)
          setIsLoading(false)
          return
        }

        setDevices(data || [])
      } catch (error) {
        console.error('Error fetching devices:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDevices()
  }, [supabase])

  // Handle blocking a device
  const handleBlockDevice = async (fingerprint: string) => {
    try {
      const success = await blockDevice(fingerprint)

      if (success) {
        // Update local state to reflect the change
        setDevices(devices.map(device =>
          device.fingerprint === fingerprint
            ? { ...device, is_blocked: true, is_trusted: false }
            : device
        ))
      }
    } catch (error) {
      console.error('Error blocking device:', error)
    }
  }

  // Handle trusting a device
  const handleTrustDevice = async (fingerprint: string) => {
    try {
      const success = await trustDevice(fingerprint)

      if (success) {
        // Update local state to reflect the change
        setDevices(devices.map(device =>
          device.fingerprint === fingerprint
            ? { ...device, is_trusted: true, is_blocked: false }
            : device
        ))
      }
    } catch (error) {
      console.error('Error trusting device:', error)
    }
  }

  // Device UI rendering logic...
}
```

## Integration with Authentication

### Session Timeout

The system implements different session timeouts based on device trust status:

```sql
CREATE OR REPLACE FUNCTION security.is_session_timed_out() RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_fingerprint TEXT;
  v_is_trusted BOOLEAN;
  v_last_activity TIMESTAMPTZ;
  v_timeout INTERVAL;
  v_role TEXT;
BEGIN
  -- Get the current user ID from the JWT
  v_user_id := auth.uid();

  -- If no authenticated user, consider the session timed out
  IF v_user_id IS NULL THEN
    RETURN true;
  END IF;

  -- Get user role
  SELECT COALESCE(r.rolname, 'authenticated') INTO v_role
  FROM pg_roles r
  JOIN pg_auth_members m ON m.roleid = r.oid
  JOIN pg_roles u ON m.member = u.oid
  WHERE u.rolname = v_user_id::text;

  -- Get the current fingerprint from request headers
  -- This assumes the client sends the fingerprint in a custom header
  BEGIN
    v_fingerprint := current_setting('request.headers', true)::json->>'x-device-fingerprint';
  EXCEPTION WHEN OTHERS THEN
    v_fingerprint := NULL;
  END;

  -- Check if device is trusted
  IF v_fingerprint IS NOT NULL THEN
    SELECT is_trusted INTO v_is_trusted
    FROM security.device_fingerprints
    WHERE user_id = v_user_id AND fingerprint = v_fingerprint AND is_blocked = false;
  ELSE
    v_is_trusted := false;
  END IF;

  -- Get last activity time from the session
  SELECT issued_at INTO v_last_activity
  FROM auth.sessions
  WHERE user_id = v_user_id
  ORDER BY created_at DESC
  LIMIT 1;

  -- If no session found, consider timed out
  IF v_last_activity IS NULL THEN
    RETURN true;
  END IF;

  -- Set timeout based on device trust and role
  IF v_role = 'admin' THEN
    -- Admin sessions timeout after 4 hours regardless of device trust
    v_timeout := INTERVAL '4 hours';
  ELSIF v_is_trusted THEN
    -- Trusted devices get longer sessions
    v_timeout := INTERVAL '7 days';
  ELSE
    -- Untrusted devices get short sessions
    v_timeout := INTERVAL '1 hour';
  END IF;

  -- Check if the session has timed out
  RETURN (now() - v_last_activity) > v_timeout;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Session Revocation

When a device is blocked, all sessions from that device need to be revoked:

```sql
CREATE OR REPLACE FUNCTION security.revoke_sessions_for_device(
  p_user_id UUID,
  p_fingerprint TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  -- Insert revoked session record
  INSERT INTO security.revoked_sessions (
    user_id,
    device_fingerprint,
    reason
  ) VALUES (
    p_user_id,
    p_fingerprint,
    'device_blocked'
  );

  -- Log the event
  PERFORM security.log_event(
    'session.revoked_for_device',
    jsonb_build_object(
      'user_id', p_user_id,
      'fingerprint', p_fingerprint
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Troubleshooting

### Common Issues

1. **Device Fingerprint Not Generated**
   - Check browser compatibility with the fingerprinting method
   - Verify JavaScript is enabled
   - Check browser console for errors

2. **Device Trust Status Not Saving**
   - Verify RLS policies on the device_fingerprints table
   - Check database permissions
   - Ensure the security schema is in the search_path

3. **Functions Not Found**
   - Check if the security schema exists
   - Verify public aliases for security functions
   - Ensure the search_path includes the security schema

### Debugging Queries

Use these queries for troubleshooting:

```sql
-- Check device registration for a user
SELECT * FROM security.device_fingerprints
WHERE user_id = '[USER_ID]'
ORDER BY last_seen DESC;

-- Test trust function directly
SELECT security.trust_device('[FINGERPRINT]');

-- Verify search path
SHOW search_path;

-- Check schema existence
SELECT schema_name FROM information_schema.schemata
WHERE schema_name = 'security';

-- Test session timeout function
SELECT security.is_session_timed_out();
```

## Best Practices

1. **Security Updates**
   - Regularly update fingerprinting methods to improve accuracy
   - Monitor for fingerprint collisions and improve the algorithm

2. **User Education**
   - Inform users about device trust features
   - Encourage users to review their devices regularly
   - Prompt for device trust decisions at login

3. **Monitoring**
   - Implement alerts for suspicious device activity
   - Monitor for multiple devices per user
   - Track geographic distribution of devices

4. **Performance**
   - Index the device_fingerprints table appropriately
   - Cache device trust status where possible
   - Optimize frequent queries

5. **Future Enhancements**
   - Implement two-factor authentication for untrusted devices
   - Add location-based security checks
   - Implement progressive security based on risk assessment
