const { createClient } = require('@supabase/supabase-js')

// Test script to check if function_exists RPC exists
async function testFunctionExists() {
  const supabaseUrl = 'https://anwefmklplkjxkmzpnva.supabase.co'
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc3OTYyNDYsImV4cCI6MjA1MzM3MjI0Nn0._mrkLcgDRn-ejKwHGEC49L2C4SVvNdBcLfJbaFwpTkU'
  
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('🔍 Testing Supabase connection and function_exists RPC...')
  
  try {
    // Test 1: Basic connection test
    console.log('\n1. Testing basic connection...')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('auth.users')
      .select('id')
      .limit(1)
    
    if (connectionError) {
      console.log('❌ Connection failed:', connectionError.message)
    } else {
      console.log('✅ Connection successful')
    }
    
    // Test 2: Check if function_exists RPC exists
    console.log('\n2. Testing function_exists RPC...')
    const { data: functionResult, error: functionError } = await supabase
      .rpc('function_exists', { p_function_name: 'register_device' })
    
    if (functionError) {
      console.log('❌ function_exists RPC failed:', functionError.message)
      console.log('   Code:', functionError.code)
      console.log('   Details:', functionError.details)
      console.log('   Hint:', functionError.hint)
    } else {
      console.log('✅ function_exists RPC worked!')
      console.log('   Result:', functionResult)
    }
    
    // Test 3: Check if register_device exists (what the original call was testing)
    console.log('\n3. Testing register_device function directly...')
    const { data: registerResult, error: registerError } = await supabase
      .rpc('register_device', { p_fingerprint: 'test-fingerprint' })
    
    if (registerError) {
      console.log('❌ register_device RPC failed:', registerError.message)
      console.log('   Code:', registerError.code)
    } else {
      console.log('✅ register_device RPC exists and works')
      console.log('   Result:', registerResult)
    }
    
  } catch (error) {
    console.log('❌ Unexpected error:', error.message)
  }
}

testFunctionExists()