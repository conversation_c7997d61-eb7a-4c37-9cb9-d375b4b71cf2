#!/usr/bin/env python3
"""
Test script for Celery worker.

This script tests the Celery worker by submitting a simple task
and checking its status.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv(".env.jobs")

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import Celery app and tasks
from jobs.worker import app
from jobs.tasks.embedding_tasks import generate_document_embedding

def test_celery_task():
    """Test a Celery task by submitting it and checking its status."""
    print("Submitting test task to Celery...")
    
    # Submit a test task
    task = generate_document_embedding.apply_async(
        args=["test-doc-123", "test-tenant", "test-user", None, None, None, 0]
    )
    
    job_id = task.id
    print(f"Task submitted with ID: {job_id}")
    
    # Wait for the task to complete
    print("Waiting for task to complete...")
    max_wait = 10  # seconds
    start_time = time.time()
    
    while not task.ready() and time.time() - start_time < max_wait:
        print(f"Task status: {task.status}")
        time.sleep(1)
    
    # Check the result
    if task.ready():
        if task.successful():
            print("Task completed successfully!")
            print(f"Result: {task.get()}")
        else:
            print(f"Task failed: {task.result}")
    else:
        print("Task did not complete within the timeout period.")
    
    return task.status

if __name__ == "__main__":
    status = test_celery_task()
    print(f"Final task status: {status}")
