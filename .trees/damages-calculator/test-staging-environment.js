#!/usr/bin/env node

/**
 * Test script to validate staging environment fixes
 */

const { createClient } = require('@supabase/supabase-js');

// Staging database configuration
const SUPABASE_URL = 'https://btwaueeckvylrlrnbvgt.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testSchemaAccess() {
  console.log('\n🔍 Testing Schema Access...');
  
  // Test tenants schema access
  try {
    const { data, error } = await supabase
      .schema('tenants')
      .from('subscription_plans')
      .select('id, name')
      .limit(1);
      
    if (error) {
      console.log('❌ Tenants schema access failed:', error.message);
      return false;
    } else {
      console.log('✅ Tenants schema access successful');
      return true;
    }
  } catch (err) {
    console.log('❌ Tenants schema access error:', err.message);
    return false;
  }
}

async function testSecurityFunctions() {
  console.log('\n🔒 Testing Security Functions...');
  
  const results = {
    is_session_timed_out: false,
    register_device: false,
    trust_device: false,
    block_device: false
  };

  // Test is_session_timed_out
  try {
    const { data, error } = await supabase.rpc('is_session_timed_out', {});
    if (error && !error.message.includes('Could not find the function')) {
      console.log('✅ is_session_timed_out function exists');
      results.is_session_timed_out = true;
    } else if (error) {
      console.log('❌ is_session_timed_out function not found');
    } else {
      console.log('✅ is_session_timed_out function exists and returned:', data);
      results.is_session_timed_out = true;
    }
  } catch (err) {
    console.log('❌ is_session_timed_out error:', err.message);
  }

  // Test register_device
  try {
    const { data, error } = await supabase.rpc('register_device', {
      p_fingerprint: 'test-staging-fingerprint',
      p_user_agent: 'test-agent',
      p_ip_address: null
    });
    
    if (error && error.message.includes('must be authenticated')) {
      console.log('✅ register_device function exists (requires auth)');
      results.register_device = true;
    } else if (error && !error.message.includes('Could not find the function')) {
      console.log('✅ register_device function exists');
      results.register_device = true;
    } else if (error) {
      console.log('❌ register_device function not found');
    } else {
      console.log('✅ register_device function exists and returned:', data);
      results.register_device = true;
    }
  } catch (err) {
    console.log('❌ register_device error:', err.message);
  }

  // Test trust_device
  try {
    const { data, error } = await supabase.rpc('trust_device', {
      p_fingerprint: 'test-staging-fingerprint'
    });
    
    if (error && error.message.includes('must be authenticated')) {
      console.log('✅ trust_device function exists (requires auth)');
      results.trust_device = true;
    } else if (error && !error.message.includes('Could not find the function')) {
      console.log('✅ trust_device function exists');
      results.trust_device = true;
    } else if (error) {
      console.log('❌ trust_device function not found');
    } else {
      console.log('✅ trust_device function exists and returned:', data);
      results.trust_device = true;
    }
  } catch (err) {
    console.log('❌ trust_device error:', err.message);
  }

  // Test block_device
  try {
    const { data, error } = await supabase.rpc('block_device', {
      p_fingerprint: 'test-staging-fingerprint'
    });
    
    if (error && error.message.includes('must be authenticated')) {
      console.log('✅ block_device function exists (requires auth)');
      results.block_device = true;
    } else if (error && !error.message.includes('Could not find the function')) {
      console.log('✅ block_device function exists');
      results.block_device = true;
    } else if (error) {
      console.log('❌ block_device function not found');
    } else {
      console.log('✅ block_device function exists and returned:', data);
      results.block_device = true;
    }
  } catch (err) {
    console.log('❌ block_device error:', err.message);
  }

  return results;
}

async function testDatabaseTables() {
  console.log('\n📊 Testing Database Tables...');
  
  const tables = [
    { schema: 'tenants', table: 'subscription_plans' },
    { schema: 'tenants', table: 'clients' },
    { schema: 'tenants', table: 'users' },
    { schema: 'security', table: 'device_fingerprints' },
    { schema: 'security', table: 'events' }
  ];

  const results = {};

  for (const { schema, table } of tables) {
    try {
      const { data, error } = await supabase
        .schema(schema)
        .from(table)
        .select('*')
        .limit(1);
        
      if (error) {
        console.log(`❌ ${schema}.${table} access failed:`, error.message);
        results[`${schema}.${table}`] = false;
      } else {
        console.log(`✅ ${schema}.${table} access successful`);
        results[`${schema}.${table}`] = true;
      }
    } catch (err) {
      console.log(`❌ ${schema}.${table} error:`, err.message);
      results[`${schema}.${table}`] = false;
    }
  }

  return results;
}

async function generateReport(schemaAccess, securityFunctions, tableAccess) {
  console.log('\n📋 STAGING ENVIRONMENT TEST REPORT');
  console.log('=====================================');
  
  console.log('\n🔍 Schema Access:');
  console.log(`  Tenants Schema: ${schemaAccess ? '✅ Working' : '❌ Failed'}`);
  
  console.log('\n🔒 Security Functions:');
  Object.entries(securityFunctions).forEach(([func, working]) => {
    console.log(`  ${func}: ${working ? '✅ Working' : '❌ Failed'}`);
  });
  
  console.log('\n📊 Database Tables:');
  Object.entries(tableAccess).forEach(([table, working]) => {
    console.log(`  ${table}: ${working ? '✅ Working' : '❌ Failed'}`);
  });
  
  // Calculate overall health
  const totalTests = 1 + Object.keys(securityFunctions).length + Object.keys(tableAccess).length;
  const passedTests = (schemaAccess ? 1 : 0) + 
                     Object.values(securityFunctions).filter(Boolean).length + 
                     Object.values(tableAccess).filter(Boolean).length;
  
  const healthPercentage = Math.round((passedTests / totalTests) * 100);
  
  console.log('\n🎯 Overall Health:');
  console.log(`  ${passedTests}/${totalTests} tests passed (${healthPercentage}%)`);
  
  if (healthPercentage >= 80) {
    console.log('  🎉 Staging environment is healthy!');
  } else if (healthPercentage >= 60) {
    console.log('  ⚠️  Staging environment has some issues');
  } else {
    console.log('  ❌ Staging environment needs attention');
  }
  
  return healthPercentage;
}

async function main() {
  console.log('🚀 Testing Staging Environment Fixes...');
  console.log('Database URL:', SUPABASE_URL);
  
  try {
    // Run all tests
    const schemaAccess = await testSchemaAccess();
    const securityFunctions = await testSecurityFunctions();
    const tableAccess = await testDatabaseTables();
    
    // Generate report
    const healthPercentage = await generateReport(schemaAccess, securityFunctions, tableAccess);
    
    console.log('\n🏁 Testing Complete!');
    
    if (healthPercentage >= 80) {
      process.exit(0);
    } else {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run the tests
main().catch(console.error);
