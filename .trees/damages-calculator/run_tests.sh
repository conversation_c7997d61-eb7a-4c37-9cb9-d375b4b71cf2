#!/bin/bash

# Set color variables
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Print heading
echo -e "\n${YELLOW}=======================================${NC}"
echo -e "${YELLOW}     DOCUMENT PROCESSING TEST SUITE     ${NC}"
echo -e "${YELLOW}=======================================${NC}\n"

# Check if running all tests or specific tests
if [ "$1" == "frontend" ]; then
  RUN_FRONTEND=true
  RUN_BACKEND=false
elif [ "$1" == "backend" ]; then
  RUN_FRONTEND=false
  RUN_BACKEND=true
else
  RUN_FRONTEND=true
  RUN_BACKEND=true
fi

# Function to run frontend tests
run_frontend_tests() {
  echo -e "${YELLOW}Running Frontend Tests...${NC}\n"

  # Navigate to frontend directory
  cd frontend

  # Install dependencies if node_modules doesn't exist or if --install flag is provided
  if [ ! -d "node_modules" ] || [ "$2" == "--install" ]; then
    echo -e "${YELLOW}Installing frontend dependencies...${NC}"
    npm install
  fi

  if [ "$2" == "--specific" ] && [ -n "$3" ]; then
    # Run specific test file
    echo -e "${YELLOW}Running specific test: $3${NC}"
    npx jest "$3" --verbose
  else
    # Run all frontend tests
    npx jest --verbose
  fi

  FRONTEND_STATUS=$?

  # Go back to the root directory
  cd ..

  return $FRONTEND_STATUS
}

# Function to run backend tests
run_backend_tests() {
  echo -e "${YELLOW}Running Backend Tests...${NC}\n"

  # Navigate to backend directory
  cd pi_lawyer

  # Install dependencies if needed
  if [ ! -d "venv" ] || [ "$2" == "--install" ]; then
    echo -e "${YELLOW}Setting up Python virtual environment...${NC}"
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    pip install pytest pytest-asyncio
  else
    # Activate virtual environment
    source venv/bin/activate
  fi

  if [ "$2" == "--specific" ] && [ -n "$3" ]; then
    # Run specific test file
    echo -e "${YELLOW}Running specific test: $3${NC}"
    python -m pytest "$3" -v
  else
    # Run all backend tests
    python -m pytest tests/ -v
  fi

  BACKEND_STATUS=$?

  # Deactivate virtual environment
  deactivate

  # Go back to the root directory
  cd ..

  return $BACKEND_STATUS
}

# Run tests based on flags
FRONTEND_STATUS=0
BACKEND_STATUS=0

if [ "$RUN_FRONTEND" = true ]; then
  run_frontend_tests "$@"
  FRONTEND_STATUS=$?
fi

if [ "$RUN_BACKEND" = true ]; then
  run_backend_tests "$@"
  BACKEND_STATUS=$?
fi

# Print summary
echo -e "\n${YELLOW}=======================================${NC}"
echo -e "${YELLOW}           TEST SUMMARY                ${NC}"
echo -e "${YELLOW}=======================================${NC}\n"

if [ "$RUN_FRONTEND" = true ]; then
  if [ $FRONTEND_STATUS -eq 0 ]; then
    echo -e "${GREEN}✓ Frontend Tests: PASSED${NC}"
  else
    echo -e "${RED}✗ Frontend Tests: FAILED${NC}"
  fi
fi

if [ "$RUN_BACKEND" = true ]; then
  if [ $BACKEND_STATUS -eq 0 ]; then
    echo -e "${GREEN}✓ Backend Tests: PASSED${NC}"
  else
    echo -e "${RED}✗ Backend Tests: FAILED${NC}"
  fi
fi

echo -e "\n${YELLOW}=======================================${NC}\n"

# Determine exit code
if [ "$RUN_FRONTEND" = true ] && [ "$RUN_BACKEND" = true ]; then
  # If both were run, exit with failure if either failed
  if [ $FRONTEND_STATUS -ne 0 ] || [ $BACKEND_STATUS -ne 0 ]; then
    exit 1
  fi
elif [ "$RUN_FRONTEND" = true ]; then
  # If only frontend was run, use its status
  exit $FRONTEND_STATUS
elif [ "$RUN_BACKEND" = true ]; then
  # If only backend was run, use its status
  exit $BACKEND_STATUS
fi

# If we got here, all tests passed
exit 0
