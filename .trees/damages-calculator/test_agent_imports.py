#!/usr/bin/env python3
"""
Test script to verify Phase 2 agent imports work correctly.
This script tests each agent individually to identify import issues.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

def test_intake_agent():
    """Test intake agent import and initialization."""
    try:
        from backend.agents.interactive.intake.agent import IntakeAgent
        agent = IntakeAgent()
        graph = agent.create_graph()
        print("✅ IntakeAgent: Import and graph creation successful")
        return True, agent, graph
    except Exception as e:
        print(f"❌ IntakeAgent: Failed - {e}")
        return False, None, None

def test_research_agent():
    """Test research agent import and initialization."""
    try:
        from backend.agents.interactive.research.agent import ResearchAgent
        agent = ResearchAgent()
        graph = agent.create_graph()
        print("✅ ResearchAgent: Import and graph creation successful")
        return True, agent, graph
    except Exception as e:
        print(f"❌ ResearchAgent: Failed - {e}")
        return False, None, None

def test_document_agent():
    """Test document agent import and initialization."""
    try:
        from backend.agents.insights.document.agent import DocumentAgent
        agent = DocumentAgent()
        graph = agent.create_graph()
        print("✅ DocumentAgent: Import and graph creation successful")
        return True, agent, graph
    except Exception as e:
        print(f"❌ DocumentAgent: Failed - {e}")
        return False, None, None

def test_deadline_agent():
    """Test deadline agent import and initialization."""
    try:
        from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
        agent = DeadlineInsightsAgent()
        graph = agent.create_graph()
        print("✅ DeadlineInsightsAgent: Import and graph creation successful")
        return True, agent, graph
    except Exception as e:
        print(f"❌ DeadlineInsightsAgent: Failed - {e}")
        return False, None, None

def test_supervisor_agent():
    """Test supervisor agent import and initialization."""
    try:
        from backend.agents.insights.supervisor.agent import SupervisorAgent
        agent = SupervisorAgent()
        graph = agent.create_graph()
        print("✅ SupervisorAgent: Import and graph creation successful")
        return True, agent, graph
    except Exception as e:
        print(f"❌ SupervisorAgent: Failed - {e}")
        return False, None, None

def main():
    """Run all agent tests."""
    print("🧪 Testing Phase 2 Agent Imports...")
    print("=" * 50)
    
    results = {}
    
    # Test each agent
    results['intake'] = test_intake_agent()
    results['research'] = test_research_agent()
    results['document'] = test_document_agent()
    results['deadline'] = test_deadline_agent()
    results['supervisor'] = test_supervisor_agent()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    successful_agents = []
    failed_agents = []
    
    for agent_name, (success, agent, graph) in results.items():
        if success:
            successful_agents.append(agent_name)
        else:
            failed_agents.append(agent_name)
    
    print(f"✅ Successful: {len(successful_agents)} agents")
    if successful_agents:
        print(f"   - {', '.join(successful_agents)}")
    
    print(f"❌ Failed: {len(failed_agents)} agents")
    if failed_agents:
        print(f"   - {', '.join(failed_agents)}")
    
    # Return results for potential use in runtime
    return results

if __name__ == "__main__":
    main()
