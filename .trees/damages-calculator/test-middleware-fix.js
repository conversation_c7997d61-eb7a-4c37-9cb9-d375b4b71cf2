#!/usr/bin/env node

/**
 * Middleware Loop Fix Testing Utility
 * 
 * This script tests the middleware loop fix implementation by:
 * 1. Simulating various request patterns
 * 2. Monitoring performance metrics
 * 3. Verifying loop prevention works
 * 4. Testing the async security event queue
 */

const https = require('https');
const http = require('http');

// Configuration
const CONFIG = {
  baseUrl: process.env.BASE_URL || 'http://localhost:3000',
  testDuration: 30000, // 30 seconds
  concurrentRequests: 5,
  requestInterval: 100, // ms
  monitoringInterval: 5000, // 5 seconds
};

// Test results
const results = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  blockedRequests: 0,
  averageResponseTime: 0,
  responseTimes: [],
  errors: [],
  startTime: null,
  endTime: null
};

/**
 * Make HTTP request with timeout
 */
function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const url = new URL(path, CONFIG.baseUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Middleware-Test-Client/1.0',
        'X-Test-Request': 'true',
        ...options.headers
      },
      timeout: 10000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          responseTime: responseTime,
          path: path
        });
      });
    });

    req.on('error', (error) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      reject({
        error: error.message,
        responseTime: responseTime,
        path: path
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        error: 'Request timeout',
        responseTime: 10000,
        path: path
      });
    });

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

/**
 * Test basic request handling
 */
async function testBasicRequests() {
  console.log('🧪 Testing basic request handling...');
  
  const testPaths = [
    '/login',
    '/dashboard',
    '/api/health',
    '/admin',
    '/client-portal'
  ];

  for (const path of testPaths) {
    try {
      const response = await makeRequest(path);
      results.totalRequests++;
      results.responseTimes.push(response.responseTime);
      
      if (response.statusCode < 500) {
        results.successfulRequests++;
        console.log(`✅ ${path}: ${response.statusCode} (${response.responseTime}ms)`);
      } else {
        results.failedRequests++;
        console.log(`❌ ${path}: ${response.statusCode} (${response.responseTime}ms)`);
      }
    } catch (error) {
      results.totalRequests++;
      results.failedRequests++;
      results.errors.push({ path, error: error.error });
      console.log(`❌ ${path}: ERROR - ${error.error} (${error.responseTime}ms)`);
    }
  }
}

/**
 * Test internal request detection
 */
async function testInternalRequestDetection() {
  console.log('🧪 Testing internal request detection...');
  
  const internalPaths = [
    '/api/security/log-event',
    '/api/security/log-events-batch',
    '/api/monitoring/middleware'
  ];

  for (const path of internalPaths) {
    try {
      const response = await makeRequest(path, {
        headers: {
          'X-Internal-Request': 'true',
          'X-Security-Internal': 'true'
        }
      });
      
      results.totalRequests++;
      results.responseTimes.push(response.responseTime);
      
      // These should be processed quickly (skipped by middleware)
      if (response.responseTime < 100) {
        console.log(`✅ ${path}: Fast internal request (${response.responseTime}ms)`);
        results.successfulRequests++;
      } else {
        console.log(`⚠️ ${path}: Slow internal request (${response.responseTime}ms)`);
        results.successfulRequests++;
      }
    } catch (error) {
      results.totalRequests++;
      results.failedRequests++;
      console.log(`❌ ${path}: ERROR - ${error.error}`);
    }
  }
}

/**
 * Test concurrent requests for loop detection
 */
async function testConcurrentRequests() {
  console.log('🧪 Testing concurrent requests for loop detection...');
  
  const concurrentPromises = [];
  
  for (let i = 0; i < CONFIG.concurrentRequests; i++) {
    const promise = (async () => {
      for (let j = 0; j < 10; j++) {
        try {
          const response = await makeRequest('/dashboard', {
            headers: {
              'X-Test-Concurrent': `batch-${i}-request-${j}`
            }
          });
          
          results.totalRequests++;
          results.responseTimes.push(response.responseTime);
          
          if (response.statusCode === 429) {
            results.blockedRequests++;
            console.log(`🛑 Request blocked (depth protection): ${response.responseTime}ms`);
          } else if (response.statusCode < 500) {
            results.successfulRequests++;
          } else {
            results.failedRequests++;
          }
          
          // Small delay to simulate real usage
          await new Promise(resolve => setTimeout(resolve, CONFIG.requestInterval));
        } catch (error) {
          results.totalRequests++;
          results.failedRequests++;
          results.errors.push({ path: '/dashboard', error: error.error });
        }
      }
    })();
    
    concurrentPromises.push(promise);
  }
  
  await Promise.all(concurrentPromises);
}

/**
 * Monitor performance metrics
 */
async function monitorPerformance() {
  try {
    const response = await makeRequest('/api/monitoring/middleware?format=detailed');
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.data);
      console.log('\n📊 Performance Metrics:');
      console.log(`   Status: ${data.status}`);
      console.log(`   Total Requests: ${data.metrics?.totalRequests || 0}`);
      console.log(`   Average Duration: ${Math.round(data.metrics?.averageDuration || 0)}ms`);
      console.log(`   Error Rate: ${Math.round((data.metrics?.errorRate || 0) * 100)}%`);
      console.log(`   Skip Rate: ${Math.round((data.metrics?.skipRate || 0) * 100)}%`);
      console.log(`   Requests/sec: ${Math.round((data.metrics?.requestsPerSecond || 0) * 100) / 100}`);
      
      if (data.issues && data.issues.length > 0) {
        console.log('   Issues:', data.issues);
      }
      
      if (data.queueMetrics) {
        console.log(`   Queue Size: ${data.queueMetrics.queueSize || 0}`);
        console.log(`   Queue Processing: ${data.queueMetrics.processing ? 'Yes' : 'No'}`);
      }
    }
  } catch (error) {
    console.log('⚠️ Could not fetch performance metrics:', error.error);
  }
}

/**
 * Test security event queue
 */
async function testSecurityEventQueue() {
  console.log('🧪 Testing security event queue...');
  
  // Make requests that should trigger security events
  const securityTestPaths = [
    '/admin?test=<script>alert(1)</script>', // XSS attempt
    '/dashboard/../../../etc/passwd', // Path traversal
    '/login' // Normal request for comparison
  ];

  for (const path of securityTestPaths) {
    try {
      await makeRequest(path, {
        headers: {
          'User-Agent': 'sqlmap/1.0 (test)'
        }
      });
      results.totalRequests++;
    } catch (error) {
      results.totalRequests++;
      // Expected for malicious requests
    }
  }
  
  // Wait a bit for events to be queued
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Check queue metrics
  await monitorPerformance();
}

/**
 * Calculate and display final results
 */
function displayResults() {
  const duration = (results.endTime - results.startTime) / 1000;
  const avgResponseTime = results.responseTimes.length > 0 
    ? results.responseTimes.reduce((sum, time) => sum + time, 0) / results.responseTimes.length 
    : 0;
  
  const successRate = results.totalRequests > 0 
    ? (results.successfulRequests / results.totalRequests) * 100 
    : 0;
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 MIDDLEWARE LOOP FIX TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`Test Duration: ${duration.toFixed(1)}s`);
  console.log(`Total Requests: ${results.totalRequests}`);
  console.log(`Successful Requests: ${results.successfulRequests}`);
  console.log(`Failed Requests: ${results.failedRequests}`);
  console.log(`Blocked Requests: ${results.blockedRequests}`);
  console.log(`Success Rate: ${successRate.toFixed(1)}%`);
  console.log(`Average Response Time: ${Math.round(avgResponseTime)}ms`);
  
  if (results.responseTimes.length > 0) {
    const sortedTimes = [...results.responseTimes].sort((a, b) => a - b);
    const p95Index = Math.floor(sortedTimes.length * 0.95);
    console.log(`P95 Response Time: ${Math.round(sortedTimes[p95Index] || 0)}ms`);
  }
  
  console.log(`Requests per Second: ${(results.totalRequests / duration).toFixed(1)}`);
  
  if (results.errors.length > 0) {
    console.log('\n❌ Errors:');
    results.errors.slice(0, 5).forEach(error => {
      console.log(`   ${error.path}: ${error.error}`);
    });
    if (results.errors.length > 5) {
      console.log(`   ... and ${results.errors.length - 5} more errors`);
    }
  }
  
  // Assessment
  console.log('\n🎯 ASSESSMENT:');
  
  if (results.blockedRequests > 0) {
    console.log('✅ Loop prevention is working (requests blocked due to depth)');
  }
  
  if (avgResponseTime < 500) {
    console.log('✅ Response times are acceptable (< 500ms average)');
  } else {
    console.log('⚠️ Response times are high (> 500ms average)');
  }
  
  if (successRate > 80) {
    console.log('✅ High success rate indicates stable operation');
  } else {
    console.log('⚠️ Low success rate may indicate issues');
  }
  
  if (results.errors.length === 0) {
    console.log('✅ No unexpected errors occurred');
  } else {
    console.log(`⚠️ ${results.errors.length} errors occurred during testing`);
  }
  
  console.log('\n✨ Middleware loop fix implementation appears to be working!');
}

/**
 * Main test execution
 */
async function runTests() {
  console.log('🚀 Starting Middleware Loop Fix Tests...');
  console.log(`Target: ${CONFIG.baseUrl}`);
  console.log(`Duration: ${CONFIG.testDuration / 1000}s`);
  console.log(`Concurrent Requests: ${CONFIG.concurrentRequests}\n`);
  
  results.startTime = Date.now();
  
  try {
    // Test basic functionality
    await testBasicRequests();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test internal request detection
    await testInternalRequestDetection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test security event queue
    await testSecurityEventQueue();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test concurrent requests for loop detection
    await testConcurrentRequests();
    
    // Final performance check
    await new Promise(resolve => setTimeout(resolve, 2000));
    await monitorPerformance();
    
  } catch (error) {
    console.error('❌ Test execution error:', error);
  }
  
  results.endTime = Date.now();
  displayResults();
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Middleware Loop Fix Testing Utility');
  console.log('');
  console.log('Usage: node test-middleware-fix.js [options]');
  console.log('');
  console.log('Environment Variables:');
  console.log('  BASE_URL - Target URL (default: http://localhost:3000)');
  console.log('  TEST_DURATION - Test duration in ms (default: 30000)');
  console.log('');
  console.log('Examples:');
  console.log('  BASE_URL=https://myapp.com node test-middleware-fix.js');
  console.log('  TEST_DURATION=60000 node test-middleware-fix.js');
  process.exit(0);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});