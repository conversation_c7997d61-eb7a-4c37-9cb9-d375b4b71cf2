#!/usr/bin/env node

/**
 * Test script to verify that the tenants schema is properly exposed
 * in the pi-lawyer-ai-staging Supabase project
 */

const { createClient } = require('@supabase/supabase-js');

// Staging database configuration
const SUPABASE_URL = 'https://btwaueeckvylrlrnbvgt.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk';

async function testSchemaAccess() {
  console.log('🧪 Testing schema access for pi-lawyer-ai-staging...\n');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  // Test 1: Access public schema (should work)
  console.log('1️⃣ Testing public schema access...');
  try {
    const { data, error } = await supabase
      .from('subscription_plans') // This is our public view
      .select('name, code')
      .limit(1);
    
    if (error) {
      console.log('❌ Public schema error:', error.message);
    } else {
      console.log('✅ Public schema works:', data?.length || 0, 'records found');
    }
  } catch (err) {
    console.log('❌ Public schema exception:', err.message);
  }
  
  // Test 2: Access tenants schema (this is what we're trying to fix)
  console.log('\n2️⃣ Testing tenants schema access...');
  try {
    const { data, error } = await supabase
      .schema('tenants')
      .from('subscription_plans')
      .select('name, code')
      .limit(1);
    
    if (error) {
      console.log('❌ Tenants schema error:', error.message);
      if (error.message.includes('schema must be one of')) {
        console.log('🔧 SOLUTION: Add "tenants" to exposed schemas in Supabase Dashboard → Settings → API');
      }
    } else {
      console.log('✅ Tenants schema works:', data?.length || 0, 'records found');
      console.log('🎉 SUCCESS: tenants schema is properly exposed!');
    }
  } catch (err) {
    console.log('❌ Tenants schema exception:', err.message);
  }
  
  // Test 3: List available schemas (diagnostic)
  console.log('\n3️⃣ Diagnostic: Checking available schemas...');
  try {
    const { data, error } = await supabase
      .from('information_schema.schemata')
      .select('schema_name')
      .not('schema_name', 'like', 'pg_%')
      .not('schema_name', 'in', '(information_schema)')
      .order('schema_name');
    
    if (error) {
      console.log('❌ Schema list error:', error.message);
    } else {
      console.log('📋 Available schemas:', data?.map(s => s.schema_name).join(', '));
    }
  } catch (err) {
    console.log('❌ Schema list exception:', err.message);
  }
}

// Run the test
testSchemaAccess().catch(console.error);
