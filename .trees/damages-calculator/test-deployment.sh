#!/bin/bash

# PI Lawyer AI - Deployment Testing Script
# Tests both frontend and backend deployments

echo "🧪 PI Lawyer AI - Deployment Testing"
echo "===================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# URLs
FRONTEND_URL="https://pi-lawyer-ai.vercel.app"
BACKEND_URL="https://pi-lawyer-ai-backend.fly.dev"

echo ""
echo "🎯 Testing Frontend (Vercel)..."
echo "URL: $FRONTEND_URL"

# Test frontend
frontend_status=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL")
if [ "$frontend_status" = "200" ]; then
    echo -e "${GREEN}✅ Frontend: ONLINE (HTTP $frontend_status)${NC}"
else
    echo -e "${RED}❌ Frontend: OFFLINE (HTTP $frontend_status)${NC}"
fi

echo ""
echo "🎯 Testing Backend (Fly.io)..."
echo "URL: $BACKEND_URL"

# Test backend health
backend_status=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/health")
if [ "$backend_status" = "200" ]; then
    echo -e "${GREEN}✅ Backend Health: ONLINE (HTTP $backend_status)${NC}"
    
    # Get health details
    echo ""
    echo "📊 Backend Health Details:"
    curl -s "$BACKEND_URL/health" | jq . 2>/dev/null || curl -s "$BACKEND_URL/health"
else
    echo -e "${RED}❌ Backend Health: OFFLINE (HTTP $backend_status)${NC}"
fi

echo ""
echo "🔧 Testing Backend API Documentation..."
docs_status=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/docs")
if [ "$docs_status" = "200" ]; then
    echo -e "${GREEN}✅ API Docs: ONLINE (HTTP $docs_status)${NC}"
    echo "   📖 View at: $BACKEND_URL/docs"
else
    echo -e "${RED}❌ API Docs: OFFLINE (HTTP $docs_status)${NC}"
fi

echo ""
echo "🔧 Testing Backend OpenAPI Schema..."
openapi_status=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/openapi.json")
if [ "$openapi_status" = "200" ]; then
    echo -e "${GREEN}✅ OpenAPI Schema: ONLINE (HTTP $openapi_status)${NC}"
else
    echo -e "${RED}❌ OpenAPI Schema: OFFLINE (HTTP $openapi_status)${NC}"
fi

echo ""
echo "📋 Testing Summary:"
echo "==================="
echo "Frontend (Vercel):     $([ "$frontend_status" = "200" ] && echo -e "${GREEN}✅ ONLINE${NC}" || echo -e "${RED}❌ OFFLINE${NC}")"
echo "Backend Health:        $([ "$backend_status" = "200" ] && echo -e "${GREEN}✅ ONLINE${NC}" || echo -e "${RED}❌ OFFLINE${NC}")"
echo "API Documentation:     $([ "$docs_status" = "200" ] && echo -e "${GREEN}✅ ONLINE${NC}" || echo -e "${RED}❌ OFFLINE${NC}")"
echo "OpenAPI Schema:        $([ "$openapi_status" = "200" ] && echo -e "${GREEN}✅ ONLINE${NC}" || echo -e "${RED}❌ OFFLINE${NC}")"

echo ""
echo "🌐 Quick Access URLs:"
echo "Frontend:     $FRONTEND_URL"
echo "Backend API:  $BACKEND_URL/docs"
echo "Health Check: $BACKEND_URL/health"

echo ""
echo "🧪 Manual Testing Checklist:"
echo "1. Open frontend in browser and test UI"
echo "2. Open backend API docs and test endpoints"
echo "3. Test authentication flow"
echo "4. Test practice area selection"
echo "5. Test AI chat functionality"
echo "6. Test form submissions"
echo "7. Test file uploads"
echo "8. Test responsive design"

echo ""
echo "✅ Deployment testing complete!"
