#!/usr/bin/env python3
"""
Test agent registration with CopilotKit SDK.
This script tests the core functionality of registering Phase 2 agents with CopilotKit.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

def test_copilotkit_registration():
    """Test registering agents with CopilotKit SDK."""
    print("🧪 Testing CopilotKit Agent Registration...")
    print("=" * 50)
    
    # Import CopilotKit components
    try:
        from copilotkit import CopilotKitSDK as CopilotKitRemoteEndpoint
        from copilotkit import LangGraphAgent
        print("✅ CopilotKit imports successful")
    except Exception as e:
        print(f"❌ CopilotKit imports failed: {e}")
        return False
    
    # Test agent imports and graph creation
    agents = []
    
    # Test IntakeAgent
    print("\n1. Testing IntakeAgent Registration:")
    try:
        from backend.agents.interactive.intake.agent import IntakeAgent
        intake_agent = IntakeAgent()
        intake_graph = intake_agent.create_graph()
        
        agents.append(LangGraphAgent(
            name="intake_agent",
            description="Multi-practice intake assistant for Personal Injury, Family Law, and Criminal Defense cases",
            graph=intake_graph,
        ))
        print("✅ IntakeAgent registered successfully")
    except Exception as e:
        print(f"❌ IntakeAgent registration failed: {e}")
    
    # Test DocumentAgent
    print("\n2. Testing DocumentAgent Registration:")
    try:
        from backend.agents.insights.document.agent import DocumentAgent
        document_agent = DocumentAgent()
        document_graph = document_agent.create_graph()
        
        agents.append(LangGraphAgent(
            name="document_agent",
            description="Legal document generation assistant for creating professional legal documents",
            graph=document_graph,
        ))
        print("✅ DocumentAgent registered successfully")
    except Exception as e:
        print(f"❌ DocumentAgent registration failed: {e}")
    
    # Test DeadlineInsightsAgent
    print("\n3. Testing DeadlineInsightsAgent Registration:")
    try:
        from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
        deadline_agent = DeadlineInsightsAgent()
        deadline_graph = deadline_agent.create_graph()
        
        agents.append(LangGraphAgent(
            name="deadline_agent",
            description="Legal deadline extraction and tracking assistant for identifying important deadlines",
            graph=deadline_graph,
        ))
        print("✅ DeadlineInsightsAgent registered successfully")
    except Exception as e:
        print(f"❌ DeadlineInsightsAgent registration failed: {e}")
    
    # Create CopilotKit SDK
    print("\n4. Testing CopilotKit SDK Creation:")
    try:
        if agents:
            sdk = CopilotKitRemoteEndpoint(agents=agents)
            print(f"✅ CopilotKit SDK created with {len(agents)} agents")
            
            # List registered agents
            print("\n📋 Registered Agents:")
            for agent in sdk.agents:
                print(f"   - {agent.name}: {agent.description}")
            
            return True, sdk
        else:
            print("❌ No agents available for SDK creation")
            return False, None
            
    except Exception as e:
        print(f"❌ CopilotKit SDK creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_agent_invocation():
    """Test basic agent invocation."""
    print("\n" + "=" * 50)
    print("🔧 Testing Agent Invocation...")
    
    try:
        from backend.agents.interactive.intake.agent import IntakeAgent
        
        # Create agent
        agent = IntakeAgent()
        
        # Create test state
        test_state = {
            "messages": [{"role": "user", "content": "I need help with a personal injury case"}],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
            "thread_id": "test-thread",
            "status": "initializing"
        }
        
        # Test agent invocation (without actually running it)
        print("✅ Agent invocation test setup successful")
        print(f"   Agent: {agent.agent_name}")
        print(f"   Test state keys: {list(test_state.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent invocation test failed: {e}")
        return False

def main():
    """Run all registration tests."""
    print("🚀 Phase 2 Agent Registration Test Suite")
    print("=" * 60)
    
    # Test CopilotKit registration
    registration_success, sdk = test_copilotkit_registration()
    
    # Test agent invocation
    invocation_success = test_agent_invocation()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    if registration_success:
        print("✅ Agent Registration: PASSED")
        print(f"   - Successfully registered {len(sdk.agents)} agents with CopilotKit")
    else:
        print("❌ Agent Registration: FAILED")
    
    if invocation_success:
        print("✅ Agent Invocation: PASSED")
    else:
        print("❌ Agent Invocation: FAILED")
    
    overall_success = registration_success and invocation_success
    
    if overall_success:
        print("\n🎉 All tests PASSED! Phase 2 agents are ready for integration.")
    else:
        print("\n⚠️ Some tests FAILED. Review issues before proceeding.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
