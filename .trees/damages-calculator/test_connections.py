"""
Connection tester for Neo4j and <PERSON>con<PERSON>.
Run this script to verify connections to both services are working correctly.
"""
import os
import sys
from dotenv import load_dotenv
import logging
from neo4j import GraphDatabase
import pinecone

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_neo4j_connection():
    """Test connection to Neo4j database."""
    logger.info("Testing Neo4j connection...")
    
    neo4j_uri = os.getenv("NEO4J_URI")
    neo4j_user = os.getenv("NEO4J_USER")
    neo4j_password = os.getenv("NEO4J_PASSWORD")
    
    if not all([neo4j_uri, neo4j_user, neo4j_password]):
        logger.error("Missing Neo4j credentials in .env file")
        return False
    
    try:
        # Create a driver instance
        driver = GraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        # Verify connectivity
        with driver.session() as session:
            result = session.run("RETURN 'Neo4j connection successful' AS message")
            message = result.single()["message"]
            logger.info(f"✅ {message}")
            
            # Get Neo4j version
            version_result = session.run("CALL dbms.components() YIELD name, versions RETURN name, versions[0] as version")
            version_record = version_result.single()
            logger.info(f"Connected to {version_record['name']} version {version_record['version']}")
            
        driver.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Neo4j connection failed: {str(e)}")
        return False

def test_pinecone_connection():
    """Test connection to Pinecone."""
    logger.info("Testing Pinecone connection...")
    
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    pinecone_environment = os.getenv("PINECONE_ENVIRONMENT", "us-east-1-gcp")
    pinecone_index_name = os.getenv("PINECONE_INDEX_NAME")
    
    if not pinecone_api_key:
        logger.error("Missing PINECONE_API_KEY in .env file")
        return False
    
    try:
        # Check installed pinecone version
        import pkg_resources
        pc_version = pkg_resources.get_distribution("pinecone-client").version
        logger.info(f"Detected Pinecone SDK version: {pc_version}")
        
        # Handle both new (>=2.2.0) and old (<2.2.0) Pinecone client versions
        is_new_version = hasattr(pinecone, 'Pinecone')
        
        if is_new_version:
            # New version (>=2.2.0)
            logger.info("Using newer Pinecone client format (>=2.2.0)")
            pc = pinecone.Pinecone(api_key=pinecone_api_key)
            indexes = pc.list_indexes()
            
            if indexes:
                logger.info(f"✅ Pinecone connection successful")
                logger.info(f"Available indexes: {', '.join(indexes)}")
                
                if pinecone_index_name and pinecone_index_name in indexes:
                    index = pc.Index(pinecone_index_name)
                    stats = index.describe_index_stats()
                    logger.info(f"Index '{pinecone_index_name}' contains {stats['total_vector_count']} vectors")
                    
                    # List namespaces if any
                    namespaces = stats.get('namespaces', {})
                    if namespaces:
                        logger.info(f"Namespaces: {', '.join(namespaces.keys())}")
                        for ns, ns_stats in namespaces.items():
                            logger.info(f"  - {ns}: {ns_stats['vector_count']} vectors")
                    else:
                        logger.info("No namespaces found in the index")
            else:
                logger.warning("No indexes found in Pinecone account")
        else:
            # Old version (<2.2.0)
            logger.info("Using legacy Pinecone client format (<2.2.0)")
            pinecone.init(api_key=pinecone_api_key, environment=pinecone_environment)
            indexes = pinecone.list_indexes()
            
            if indexes:
                logger.info(f"✅ Pinecone connection successful")
                logger.info(f"Available indexes: {', '.join(indexes)}")
                
                if pinecone_index_name and pinecone_index_name in indexes:
                    index = pinecone.Index(pinecone_index_name)
                    stats = index.describe_index_stats()
                    logger.info(f"Index '{pinecone_index_name}' contains {stats['total_vector_count']} vectors")
                    
                    # List namespaces if any
                    namespaces = stats.get('namespaces', {})
                    if namespaces:
                        logger.info(f"Namespaces: {', '.join(namespaces.keys())}")
                        for ns, ns_stats in namespaces.items():
                            logger.info(f"  - {ns}: {ns_stats['vector_count']} vectors")
                    else:
                        logger.info("No namespaces found in the index")
            else:
                logger.warning("No indexes found in Pinecone account")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Pinecone connection failed: {str(e)}")
        return False
    
def check_voyage_api_key():
    """Check if Voyage API key is set."""
    voyage_api_key = os.getenv("VOYAGE_API_KEY")
    if voyage_api_key:
        logger.info(f"✅ VOYAGE_API_KEY is set: {voyage_api_key[:5]}...{voyage_api_key[-4:]}")
        return True
    else:
        logger.error("❌ VOYAGE_API_KEY is not set in .env file")
        return False

def check_embedding_model():
    """Check if embedding model is set to voyage-3-large."""
    embedding_model = os.getenv("EMBEDDING_MODEL")
    if embedding_model == "voyage-3-large":
        logger.info(f"✅ EMBEDDING_MODEL is correctly set to voyage-3-large")
        return True
    else:
        logger.warning(f"⚠️ EMBEDDING_MODEL is set to '{embedding_model}', should be 'voyage-3-large'")
        return False

if __name__ == "__main__":
    logger.info("Starting connection tests...")
    
    # Test Neo4j
    neo4j_ok = test_neo4j_connection()
    
    # Test Pinecone
    pinecone_ok = test_pinecone_connection()
    
    # Check Voyage API key
    voyage_ok = check_voyage_api_key()
    
    # Check embedding model
    embedding_ok = check_embedding_model()
    
    # Summary
    print("\n" + "="*50)
    print("CONNECTION TEST SUMMARY")
    print("="*50)
    print(f"Neo4j Connection:     {'✅ PASSED' if neo4j_ok else '❌ FAILED'}")
    print(f"Pinecone Connection:  {'✅ PASSED' if pinecone_ok else '❌ FAILED'}")
    print(f"Voyage API Key:       {'✅ SET' if voyage_ok else '❌ MISSING'}")
    print(f"Embedding Model:      {'✅ CORRECT' if embedding_ok else '⚠️ NEEDS UPDATE'}")
    print("="*50)
    
    if not embedding_ok:
        print("\nTo update your embedding model, run:")
        print("sed -i '' 's/EMBEDDING_MODEL=text-embedding-3-large/EMBEDDING_MODEL=voyage-3-large/g' .env")
    
    if not (neo4j_ok and pinecone_ok):
        sys.exit(1)
