# Strategic Database Migration Plan

## 🎯 PRIORITY-BASED MIGRATION APPROACH

Given the scope (196 missing tables), we need a strategic approach focusing on critical functionality first.

### **Phase 1: Critical Application Schemas (HIGH PRIORITY)**

#### 1.1 Tenants Schema (67 missing tables)
**Impact**: Core application functionality
**Tables needed for basic app operation**:
- `users` - User management
- `user_roles` - Role-based access
- `firms` - Law firm data
- `matters` - Case management
- `clients` - Client management
- `tasks` - Task management
- `subscription_plans` ✅ (already migrated)
- `tenant_subscriptions` - Active subscriptions
- `tenant_quotas` - Usage limits

#### 1.2 Public Schema (64 missing tables)
**Impact**: Legal data and core features
**Critical tables**:
- `cases` - Legal case database
- `documents` - Document management
- `people` - Legal entities
- `jurisdictions` - Legal jurisdictions
- `legal_templates` - Document templates
- `system_settings` - App configuration

### **Phase 2: Security & Compliance (MEDIUM PRIORITY)**

#### 2.1 Security Schema (27 missing tables)
**Impact**: Security, audit, compliance
**Tables**:
- Security events, audit logs, compliance tracking

#### 2.2 Error Reporting Schema (4 missing tables)
**Impact**: Monitoring and debugging

### **Phase 3: Enhanced Features (LOW PRIORITY)**

#### 3.1 MFA Enhancements (9 missing tables)
#### 3.2 Payment Methods (4 missing tables)
#### 3.3 Data Retention (7 missing tables)
#### 3.4 Other schemas

## 🚀 RECOMMENDED MIGRATION STRATEGY

### **Option A: Supabase CLI Migration (RECOMMENDED)**
Use Supabase CLI to dump and restore schemas:

```bash
# 1. Dump production schema
supabase db dump --project-ref anwefmklplkjxkmzpnva --schema tenants > tenants_schema.sql
supabase db dump --project-ref anwefmklplkjxkmzpnva --schema public > public_schema.sql

# 2. Apply to staging
supabase db reset --project-ref btwaueeckvylrlrnbvgt
psql -h db.btwaueeckvylrlrnbvgt.supabase.co -U postgres -f tenants_schema.sql
psql -h db.btwaueeckvylrlrnbvgt.supabase.co -U postgres -f public_schema.sql
```

### **Option B: Manual Schema Recreation (CURRENT)**
Recreate critical tables manually using Management API

### **Option C: Supabase Project Duplication**
Create new staging project from production backup

## 🎯 IMMEDIATE ACTION PLAN

### **Step 1: Install Supabase CLI**
```bash
npm install -g supabase
supabase login
```

### **Step 2: Link Projects**
```bash
supabase link --project-ref anwefmklplkjxkmzpnva  # Production
supabase link --project-ref btwaueeckvylrlrnbvgt  # Staging
```

### **Step 3: Schema Migration**
```bash
# Dump critical schemas
supabase db dump --project-ref anwefmklplkjxkmzpnva --schema tenants --data-only=false > tenants_complete.sql
supabase db dump --project-ref anwefmklplkjxkmzpnva --schema public --data-only=false > public_complete.sql

# Apply to staging (after backup)
supabase db reset --project-ref btwaueeckvylrlrnbvgt
supabase db push --project-ref btwaueeckvylrlrnbvgt
```

### **Step 4: Data Migration (Selective)**
```bash
# Migrate reference data only (no user data)
supabase db dump --project-ref anwefmklplkjxkmzpnva --schema tenants --data-only=true --table subscription_plans > data_migration.sql
# Apply reference data
```

## ⚠️ MIGRATION CONSIDERATIONS

### **Data Safety**
- ✅ Backup staging before migration
- ✅ Use READ-ONLY operations on production
- ✅ Migrate schema first, then selective data
- ❌ Do NOT migrate user data (GDPR/privacy)

### **Reference Data to Migrate**
- ✅ Subscription plans, pricing
- ✅ Legal templates, jurisdictions
- ✅ System settings, configurations
- ✅ Practice areas, document categories
- ❌ User accounts, client data
- ❌ Cases, documents, personal data

### **Testing Strategy**
1. **Schema Validation**: Verify all tables created
2. **Permission Testing**: Ensure RLS policies work
3. **API Testing**: Test all endpoints
4. **Integration Testing**: Full application flow
5. **Performance Testing**: Query performance

## 🎯 SUCCESS CRITERIA

### **Phase 1 Complete When**:
- ✅ All tenants schema tables exist
- ✅ All public schema tables exist  
- ✅ Basic app functionality works
- ✅ User authentication works
- ✅ Subscription system works

### **Migration Complete When**:
- ✅ All schemas match production
- ✅ All functions and views migrated
- ✅ All permissions configured
- ✅ Full application functionality
- ✅ Performance matches production

## 🚨 ROLLBACK PLAN

If migration fails:
1. **Restore staging from backup**
2. **Revert environment variables**
3. **Continue with production for testing**
4. **Retry migration with lessons learned**

## 📊 ESTIMATED TIMELINE

- **Phase 1 (Critical)**: 2-4 hours
- **Phase 2 (Security)**: 1-2 hours  
- **Phase 3 (Enhanced)**: 1-2 hours
- **Testing & Validation**: 2-3 hours
- **Total**: 6-11 hours

## 🎯 NEXT IMMEDIATE STEPS

1. **Install Supabase CLI** ⏳
2. **Backup staging database** ⏳
3. **Dump production schemas** ⏳
4. **Apply to staging** ⏳
5. **Test critical functionality** ⏳
