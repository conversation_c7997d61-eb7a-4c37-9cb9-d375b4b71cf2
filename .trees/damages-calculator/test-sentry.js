#!/usr/bin/env node

/**
 * Test script to verify Sentry integration is working correctly
 */

require('dotenv').config();

// Import Sentry
const Sentry = require('@sentry/node');

// Initialize Sentry
Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV || 'development',
  release: process.env.NEXT_PUBLIC_APP_VERSION || 'test-1.0.0',
  tracesSampleRate: 1.0,
  debug: true,
});

console.log('🔧 Testing Sentry Integration for PI Lawyer AI');
console.log('================================================');

// Test 1: Basic configuration
console.log('\n1. Testing Sentry Configuration:');
console.log(`   DSN: ${process.env.NEXT_PUBLIC_SENTRY_DSN ? '✅ Configured' : '❌ Missing'}`);
console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`   Release: ${process.env.NEXT_PUBLIC_APP_VERSION || 'test-1.0.0'}`);

// Test 2: Capture a test message
console.log('\n2. Testing Message Capture:');
try {
  Sentry.captureMessage('Test message from PI Lawyer AI - Sentry integration working!', 'info');
  console.log('   ✅ Test message sent to Sentry');
} catch (error) {
  console.log('   ❌ Failed to send test message:', error.message);
}

// Test 3: Capture a test error
console.log('\n3. Testing Error Capture:');
try {
  const testError = new Error('Test error from PI Lawyer AI - Error reporting working!');
  testError.stack = `Error: Test error from PI Lawyer AI - Error reporting working!
    at testSentryIntegration (/test-sentry.js:45:21)
    at Object.<anonymous> (/test-sentry.js:60:1)`;
  
  Sentry.captureException(testError);
  console.log('   ✅ Test error sent to Sentry');
} catch (error) {
  console.log('   ❌ Failed to send test error:', error.message);
}

// Test 4: Test with context
console.log('\n4. Testing Context and Tags:');
try {
  Sentry.withScope((scope) => {
    scope.setTag('component', 'test-script');
    scope.setTag('service', 'pi-lawyer-ai');
    scope.setContext('test_info', {
      test_type: 'integration_test',
      timestamp: new Date().toISOString(),
      user_agent: 'Node.js Test Script',
      feature: 'error_reporting'
    });
    scope.setUser({
      id: 'test-user-123',
      email: '<EMAIL>',
      username: 'test-user'
    });
    
    Sentry.captureMessage('Test message with context and tags', 'info');
  });
  console.log('   ✅ Test message with context sent to Sentry');
} catch (error) {
  console.log('   ❌ Failed to send test message with context:', error.message);
}

// Test 5: Test performance monitoring
console.log('\n5. Testing Performance Monitoring:');
try {
  const transaction = Sentry.startTransaction({
    op: 'test',
    name: 'PI Lawyer AI - Test Transaction'
  });
  
  // Simulate some work
  const span = transaction.startChild({
    op: 'test.operation',
    description: 'Test operation for PI Lawyer AI'
  });
  
  setTimeout(() => {
    span.finish();
    transaction.finish();
    console.log('   ✅ Performance transaction sent to Sentry');
  }, 100);
} catch (error) {
  console.log('   ❌ Failed to send performance data:', error.message);
}

// Test 6: Test breadcrumbs
console.log('\n6. Testing Breadcrumbs:');
try {
  Sentry.addBreadcrumb({
    message: 'User navigated to dashboard',
    category: 'navigation',
    level: 'info',
    data: {
      from: '/login',
      to: '/dashboard'
    }
  });
  
  Sentry.addBreadcrumb({
    message: 'API call made',
    category: 'http',
    level: 'info',
    data: {
      url: '/api/legal-analysis',
      method: 'POST',
      status_code: 200
    }
  });
  
  Sentry.captureMessage('Test message with breadcrumbs', 'info');
  console.log('   ✅ Breadcrumbs added and sent to Sentry');
} catch (error) {
  console.log('   ❌ Failed to add breadcrumbs:', error.message);
}

console.log('\n🎉 Sentry Integration Test Complete!');
console.log('=====================================');
console.log('Check your Sentry dashboard at: https://sentry.io/');
console.log('Look for the test messages and errors in your PI Lawyer AI project.');

// Flush and exit
Sentry.flush(2000).then(() => {
  console.log('\n✅ All events flushed to Sentry successfully');
  process.exit(0);
}).catch((error) => {
  console.log('\n❌ Error flushing events to Sentry:', error.message);
  process.exit(1);
});
