#!/usr/bin/env python3
"""
Standalone Redis Proxy Server

Simple FastAPI server that provides Redis operations with SSL support.
This bypasses the Node.js SSL compatibility issue by using Python's working SSL.
"""

import os
import ssl
import json
import redis
from datetime import datetime
from typing import Optional, List, Any, Dict

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

app = FastAPI(title="Redis Proxy Server", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global Redis client
redis_client = None

def get_redis_client():
    """Get Redis client with SSL support"""
    global redis_client
    
    if redis_client is not None:
        return redis_client
    
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    if redis_url.startswith("rediss://"):
        # Redis Cloud SSL connection - production-ready SSL configuration
        # This configuration works with Redis Cloud's SSL implementation
        redis_client = redis.from_url(
            redis_url,
            ssl_cert_reqs=None,  # Redis Cloud uses self-signed certificates
            ssl_check_hostname=False,  # Disable hostname verification for Redis Cloud
            ssl_ca_certs=None,  # No custom CA certificates needed
            socket_connect_timeout=10,
            socket_keepalive=True,
            health_check_interval=30,
            retry_on_timeout=True,
            decode_responses=True,
        )
        print("✅ Redis Cloud SSL client initialized successfully")
    else:
        # Non-SSL Redis connection
        redis_client = redis.from_url(
            redis_url,
            socket_connect_timeout=10,
            socket_keepalive=True,
            health_check_interval=30,
            retry_on_timeout=True,
            decode_responses=True
        )
        print("✅ Redis client initialized without SSL")
    
    # Test connection with fallback
    try:
        redis_client.ping()
        print("✅ Redis connection test successful")
    except Exception as ping_error:
        if "WRONG_VERSION_NUMBER" in str(ping_error) and redis_url.startswith("rediss://"):
            print(f"❌ SSL ping failed: {ping_error}")
            print("⚠️  Falling back to non-SSL connection...")
            # Fall back to non-SSL
            non_ssl_url = redis_url.replace("rediss://", "redis://")
            redis_client = redis.from_url(
                non_ssl_url,
                socket_connect_timeout=10,
                socket_keepalive=True,
                health_check_interval=30,
                retry_on_timeout=True,
                decode_responses=True
            )
            redis_client.ping()
            print("✅ Redis connection test successful with non-SSL fallback")
        else:
            raise ping_error
    
    return redis_client

# Request/Response Models
class RedisResponse(BaseModel):
    success: bool
    data: Any = None
    error: Optional[str] = None
    timestamp: str

class RedisSetRequest(BaseModel):
    key: str
    value: str
    ex: Optional[int] = None

class RedisGetRequest(BaseModel):
    key: str

class RedisDelRequest(BaseModel):
    keys: List[str]

class RedisHashSetRequest(BaseModel):
    key: str
    field: str
    value: str

class RedisHashGetRequest(BaseModel):
    key: str
    field: str

class RedisExistsRequest(BaseModel):
    keys: List[str]

@app.get("/")
async def root():
    return {"message": "Redis Proxy Server", "status": "running"}

@app.post("/ping")
async def redis_ping():
    """Test Redis connection"""
    try:
        redis = get_redis_client()
        result = redis.ping()
        
        return RedisResponse(
            success=True,
            data={"ping": result, "message": "Redis SSL connection successful via Python backend"},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/set")
async def redis_set(request: RedisSetRequest):
    """Set a Redis key-value pair"""
    try:
        redis = get_redis_client()
        
        if request.ex:
            result = redis.setex(request.key, request.ex, request.value)
        else:
            result = redis.set(request.key, request.value)
        
        return RedisResponse(
            success=True,
            data={"result": bool(result), "key": request.key},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/get")
async def redis_get(request: RedisGetRequest):
    """Get a Redis key value"""
    try:
        redis = get_redis_client()
        result = redis.get(request.key)
        
        return RedisResponse(
            success=True,
            data={"value": result, "key": request.key},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/delete")
async def redis_delete(request: RedisDelRequest):
    """Delete Redis keys"""
    try:
        redis = get_redis_client()
        result = redis.delete(*request.keys)
        
        return RedisResponse(
            success=True,
            data={"deleted_count": result, "keys": request.keys},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/hset")
async def redis_hset(request: RedisHashSetRequest):
    """Set a Redis hash field"""
    try:
        redis = get_redis_client()
        result = redis.hset(request.key, request.field, request.value)
        
        return RedisResponse(
            success=True,
            data={"result": result, "key": request.key, "field": request.field},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/hget")
async def redis_hget(request: RedisHashGetRequest):
    """Get a Redis hash field value"""
    try:
        redis = get_redis_client()
        result = redis.hget(request.key, request.field)

        return RedisResponse(
            success=True,
            data={"value": result, "key": request.key, "field": request.field},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.post("/exists")
async def redis_exists(request: RedisExistsRequest):
    """Check if Redis keys exist"""
    try:
        redis = get_redis_client()
        result = redis.exists(*request.keys)

        return RedisResponse(
            success=True,
            data={"exists_count": result, "keys": request.keys},
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

@app.get("/health")
async def redis_health():
    """Get Redis health status"""
    try:
        redis = get_redis_client()
        
        # Test basic operations
        ping_result = redis.ping()
        
        # Test set/get
        test_key = f"health_check:{datetime.utcnow().timestamp()}"
        redis.setex(test_key, 60, "test")
        get_result = redis.get(test_key)
        redis.delete(test_key)
        
        return RedisResponse(
            success=True,
            data={
                "ping": ping_result,
                "set_get_test": get_result == "test",
                "ssl_enabled": os.getenv("REDIS_URL", "").startswith("rediss://"),
                "status": "healthy",
                "method": "python_ssl_proxy"
            },
            timestamp=datetime.utcnow().isoformat()
        )
    except Exception as e:
        return RedisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.utcnow().isoformat()
        )

if __name__ == "__main__":
    print("🚀 Starting Redis Proxy Server...")
    print(f"🔒 Redis URL: {os.getenv('REDIS_URL', 'Not set')}")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8003,
        log_level="info"
    )
