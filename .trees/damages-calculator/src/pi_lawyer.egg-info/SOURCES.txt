README.md
pyproject.toml
setup.py
src/pi_lawyer/__init__.py
src/pi_lawyer/config.py
src/pi_lawyer/document_worker.py
src/pi_lawyer.egg-info/PKG-INFO
src/pi_lawyer.egg-info/SOURCES.txt
src/pi_lawyer.egg-info/dependency_links.txt
src/pi_lawyer.egg-info/requires.txt
src/pi_lawyer.egg-info/top_level.txt
src/pi_lawyer/agents/__init__.py
src/pi_lawyer/agents/copilotkit_runtime.py
src/pi_lawyer/agents/deadline_agent.py
src/pi_lawyer/agents/document_agent.py
src/pi_lawyer/agents/intake_agent.py
src/pi_lawyer/agents/research_agent.py
src/pi_lawyer/agents/research_copilot_handler.py
src/pi_lawyer/agents/research/__init__.py
src/pi_lawyer/agents/research/graph.py
src/pi_lawyer/agents/research/state.py
src/pi_lawyer/api/__init__.py
src/pi_lawyer/api/authored_document_embedding.py
src/pi_lawyer/api/copilotkit_route.py
src/pi_lawyer/api/document_route.py
src/pi_lawyer/api/document_route_variables_update.py
src/pi_lawyer/api/health_api.py
src/pi_lawyer/api/intake_route.py
src/pi_lawyer/api/main.py
src/pi_lawyer/api/research_route.py
src/pi_lawyer/api/runtime.py
src/pi_lawyer/api/runtime_old.py
src/pi_lawyer/api/task_route.py
src/pi_lawyer/auth/__init__.py
src/pi_lawyer/auth/auth_helpers.py
src/pi_lawyer/auth/jwt_auth.py
src/pi_lawyer/auth/middleware.py
src/pi_lawyer/auth/rbac.py
src/pi_lawyer/auth/tenant.py
src/pi_lawyer/db/__init__.py
src/pi_lawyer/db/database.py
src/pi_lawyer/db/mock_auth.py
src/pi_lawyer/db/pinecone_client.py
src/pi_lawyer/db/supabase_client.py
src/pi_lawyer/documents/__init__.py
src/pi_lawyer/documents/embeddings.py
src/pi_lawyer/documents/manager.py
src/pi_lawyer/documents/processor.py
src/pi_lawyer/documents/storage.py
src/pi_lawyer/services/__init__.py
src/pi_lawyer/services/authored_document_embedding_service.py
src/pi_lawyer/services/authored_document_worker.py
src/pi_lawyer/services/circuit_breaker.py
src/pi_lawyer/services/deadline_extraction_service.py
src/pi_lawyer/services/document_analysis_service.py
src/pi_lawyer/services/document_classifier_service.py
src/pi_lawyer/services/document_embedding_utils.py
src/pi_lawyer/services/document_parser_service.py
src/pi_lawyer/services/document_processing_queue.py
src/pi_lawyer/services/document_processing_transaction.py
src/pi_lawyer/services/document_summarization.py
src/pi_lawyer/services/document_worker.py
src/pi_lawyer/services/error_classification.py
src/pi_lawyer/services/health_service.py
src/pi_lawyer/services/metrics_collector.py
src/pi_lawyer/services/redis_lock_service.py
src/pi_lawyer/services/redis_queue_service.py
src/pi_lawyer/services/task_embedding_service.py
src/pi_lawyer/services/tenant_document_embedding_service.py
src/pi_lawyer/services/services/__init__.py
src/pi_lawyer/services/services/authored_document_embedding_service.py
src/pi_lawyer/services/services/authored_document_worker.py
src/pi_lawyer/services/services/circuit_breaker.py
src/pi_lawyer/services/services/deadline_extraction_service.py
src/pi_lawyer/services/services/document_analysis_service.py
src/pi_lawyer/services/services/document_classifier_service.py
src/pi_lawyer/services/services/document_embedding_utils.py
src/pi_lawyer/services/services/document_parser_service.py
src/pi_lawyer/services/services/document_processing_queue.py
src/pi_lawyer/services/services/document_processing_transaction.py
src/pi_lawyer/services/services/document_summarization.py
src/pi_lawyer/services/services/document_worker.py
src/pi_lawyer/services/services/error_classification.py
src/pi_lawyer/services/services/health_service.py
src/pi_lawyer/services/services/metrics_collector.py
src/pi_lawyer/services/services/redis_lock_service.py
src/pi_lawyer/services/services/redis_queue_service.py
src/pi_lawyer/services/services/task_embedding_service.py
src/pi_lawyer/services/services/tenant_document_embedding_service.py
src/pi_lawyer/utils/__init__.py
src/pi_lawyer/utils/check_gcs.py
src/pi_lawyer/utils/demonstrate_paths.py
src/pi_lawyer/utils/gemini_client.py
src/pi_lawyer/utils/logging.py
src/pi_lawyer/utils/query_classifier.py
src/pi_lawyer/utils/storage_utils.py
src/pi_lawyer/utils/structured_logging.py
src/pi_lawyer/utils/template_utils.py
src/pi_lawyer/utils/test_gcs_structure.py
src/pi_lawyer/utils/voyage_embeddings.py
src/pi_lawyer/utils/voyage_reranker.py
tests/test_ai_intake.py
tests/test_check_gcs.py
tests/test_circuit_breaker.py
tests/test_client_intake.py
tests/test_database.py
tests/test_demonstrate_paths.py
tests/test_document_worker.py
tests/test_error_classification.py
tests/test_gemini_client.py
tests/test_logging.py
tests/test_query_classifier.py
tests/test_rbac.py
tests/test_research_access.py
tests/test_storage_utils.py
tests/test_structured_logging.py
tests/test_supabase_connection.py
tests/test_template_utils.py
tests/test_test_gcs_structure.py
tests/test_voyage_reranker.py