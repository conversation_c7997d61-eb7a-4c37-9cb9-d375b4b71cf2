'use client';

import React from 'react';

interface LocationDisplayProps {
  location: any;
}

export function LocationDisplay({ location }: LocationDisplayProps) {
  if (!location) {
    return <span>Unknown</span>;
  }

  if (typeof location === 'string') {
    return <span>{location}</span>;
  }

  // Handle object location
  const city = location.city;
  const country = location.country;

  if (city && country) {
    return <span>{`${city}, ${country}`}</span>;
  }

  if (city) {
    return <span>{city}</span>;
  }

  if (country) {
    return <span>{country}</span>;
  }

  // Fallback to JSON string
  try {
    return <span>{JSON.stringify(location)}</span>;
  } catch (e) {
    return <span>Unknown</span>;
  }
}
