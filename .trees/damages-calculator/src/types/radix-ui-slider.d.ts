/**
 * Type definitions for @radix-ui/react-slider
 */

declare module '@radix-ui/react-slider' {
  import * as React from 'react';

  type PrimitiveDivProps = React.ComponentPropsWithoutRef<'div'>;

  interface SliderProps extends PrimitiveDivProps {
    defaultValue?: number[];
    value?: number[];
    onValueChange?: (value: number[]) => void;
    onValueCommit?: (value: number[]) => void;
    name?: string;
    disabled?: boolean;
    orientation?: 'horizontal' | 'vertical';
    dir?: 'ltr' | 'rtl';
    inverted?: boolean;
    min?: number;
    max?: number;
    step?: number;
    minStepsBetweenThumbs?: number;
  }

  interface SliderTrackProps extends PrimitiveDivProps {}
  interface SliderRangeProps extends PrimitiveDivProps {}

  interface SliderThumbProps extends Omit<PrimitiveDivProps, 'asChild'> {
    asChild?: boolean;
  }

  const Root: React.ForwardRefExoticComponent<SliderProps & React.RefAttributes<HTMLDivElement>>;
  const Track: React.ForwardRefExoticComponent<SliderTrackProps & React.RefAttributes<HTMLDivElement>>;
  const Range: React.ForwardRefExoticComponent<SliderRangeProps & React.RefAttributes<HTMLDivElement>>;
  const Thumb: React.ForwardRefExoticComponent<SliderThumbProps & React.RefAttributes<HTMLDivElement>>;

  export {
    Root,
    Track,
    Range,
    Thumb
  };
}
