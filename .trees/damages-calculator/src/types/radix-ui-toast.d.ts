/**
 * Type definitions for @radix-ui/react-toast
 */

declare module '@radix-ui/react-toast' {
  import * as React from 'react';

  type PrimitiveDivProps = React.ComponentPropsWithoutRef<'div'>;
  type PrimitiveButtonProps = React.ComponentPropsWithoutRef<'button'>;

  interface ToastProviderProps {
    children?: React.ReactNode;
    duration?: number;
    label?: string;
    swipeDirection?: 'right' | 'left' | 'up' | 'down';
    swipeThreshold?: number;
  }

  interface ToastViewportProps extends PrimitiveDivProps {}

  interface ToastProps extends PrimitiveDivProps {
    open?: boolean;
    defaultOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
    forceMount?: boolean;
    type?: 'foreground' | 'background';
    duration?: number;
  }

  interface ToastTitleProps extends PrimitiveDivProps {}
  interface ToastDescriptionProps extends PrimitiveDivProps {}

  interface ToastActionProps extends PrimitiveButtonProps {
    altText: string;
  }

  interface ToastCloseProps extends PrimitiveButtonProps {}

  const Provider: React.FC<ToastProviderProps>;
  const Viewport: React.ForwardRefExoticComponent<ToastViewportProps & React.RefAttributes<HTMLDivElement>>;
  const Root: React.ForwardRefExoticComponent<ToastProps & React.RefAttributes<HTMLDivElement>>;
  const Title: React.ForwardRefExoticComponent<ToastTitleProps & React.RefAttributes<HTMLDivElement>>;
  const Description: React.ForwardRefExoticComponent<ToastDescriptionProps & React.RefAttributes<HTMLDivElement>>;
  const Action: React.ForwardRefExoticComponent<ToastActionProps & React.RefAttributes<HTMLButtonElement>>;
  const Close: React.ForwardRefExoticComponent<ToastCloseProps & React.RefAttributes<HTMLButtonElement>>;

  export {
    Provider,
    Viewport,
    Root,
    Title,
    Description,
    Action,
    Close
  };
}
