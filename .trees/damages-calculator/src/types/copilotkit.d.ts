import React from 'react';

declare module '@copilotkit/react-core' {
  export interface CopilotKitProps {
    /**
     * URL for the CopilotKit runtime
     */
    runtimeUrl: string;
    
    /**
     * Agent to use for this CopilotKit instance
     */
    agent?: string;
    
    /**
     * Context to pass to the agent
     */
    context?: Record<string, any>;
    
    /**
     * Children to render
     */
    children?: React.ReactNode;
  }

  export const CopilotKit: React.FC<CopilotKitProps>;
}

declare module '@copilotkit/react-ui' {
  export interface CopilotChatProps {
    /**
     * CSS class name
     */
    className?: string;
    
    /**
     * Children to render
     */
    children?: React.ReactNode;
    
    /**
     * Display name for the chat
     */
    displayName?: string;
    
    /**
     * Placeholder text for the input
     */
    placeholder?: string;
    
    /**
     * Suggestions to show
     */
    suggestions?: string[];
    
    /**
     * Context to pass to the agent
     */
    context?: Record<string, unknown>;
    
    /**
     * Initial message to show
     */
    initialMessage?: string;
    
    /**
     * Error handler
     */
    onError?: (error: Error) => void;
    
    /**
     * Agent to use
     */
    agent?: unknown;
    
    /**
     * Title for the chat
     */
    title?: string;
    
    /**
     * Whether to show avatars
     */
    showAvatars?: boolean;
    
    /**
     * Chat message handler
     */
    onChatMessage?: (message: any) => Promise<void>;
    
    /**
     * Chat started handler
     */
    onChatStarted?: () => void;
    
    /**
     * Chat completed handler
     */
    onChatCompleted?: () => void;
    
    /**
     * Chat error handler
     */
    onChatError?: (error: any) => void;
    
    /**
     * Chat request options
     */
    chatRequestOptions?: Record<string, any>;
  }

  export const CopilotChat: React.FC<CopilotChatProps>;
}
