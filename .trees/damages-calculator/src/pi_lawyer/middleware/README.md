# PI Lawyer AI Middleware

This directory contains middleware components for the PI Lawyer AI application.

## JWT Verification Middleware

The JWT verification middleware (`jwt_middleware.py`) authenticates and authorizes requests to the FastAPI server by verifying JWT tokens issued by Supabase.

### Features

- Extracts JWT tokens from request headers
- Verifies token signatures using the JWT secret
- Extracts tenant and user information from the token
- Adds user context to the request state for use in route handlers
- Implements tenant isolation to ensure users can only access their authorized data
- Supports development mode with fallback authentication

### Usage

To use the JWT middleware in a FastAPI application:

```python
from fastapi import FastAPI
from pi_lawyer.middleware.jwt_middleware import verify_jwt_middleware

app = FastAPI()
app.middleware("http")(verify_jwt_middleware)
```

### Public Endpoints

The following endpoints are accessible without authentication:

- `/health` - Health check endpoint
- `/docs` - API documentation
- `/redoc` - API documentation (ReDoc)
- `/openapi.json` - OpenAPI schema
- `/config/check` - Configuration check endpoint

### Development Mode

In development mode (`APP_ENV=development`), the middleware allows requests without valid JWT tokens and sets default user context values:

- `user_id`: "00000000-0000-0000-0000-000000000000"
- `tenant_id`: "00000000-0000-0000-0000-000000000000"
- `role`: "user"

### Configuration

The middleware uses the following environment variables:

- `SUPABASE_JWT_SECRET` - The JWT secret used to verify token signatures
- `JWT_ALGORITHM` - The algorithm used to verify token signatures (default: "HS256")
- `APP_ENV` - The application environment (set to "development" for development mode)

### User Context

The middleware adds the following user context to the request state:

- `request.state.user_id` - The user ID from the JWT token
- `request.state.tenant_id` - The tenant ID from the JWT token
- `request.state.role` - The user role from the JWT token
- `request.state.email` - The user email from the JWT token
- `request.state.user` - A `UserContext` object containing all user information

### Testing

Unit tests for the JWT middleware are available in `tests/middleware/test_jwt_middleware.py`.
Integration tests are available in `tests/api/test_jwt_integration.py`.
