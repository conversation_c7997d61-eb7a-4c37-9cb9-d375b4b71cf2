"""
Input validation and sanitization middleware for the PI Lawyer AI system.

This middleware provides comprehensive input validation, sanitization, and security
checks for all incoming requests to prevent injection attacks and ensure data integrity.
"""

import json
import logging
import re
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set
from urllib.parse import unquote

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

# Configuration constants
class ValidationConfig:
    """Configuration for input validation middleware."""
    
    # Request size limits
    MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_URL_LENGTH = 2048
    MAX_HEADER_SIZE = 8192
    MAX_QUERY_PARAM_LENGTH = 1024
    MAX_JSON_DEPTH = 10
    
    # Content type limits
    ALLOWED_CONTENT_TYPES = {
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data',
        'text/plain',
        'application/octet-stream'
    }
    
    # File upload limits
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_EXTENSIONS = {
        '.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
        '.mp3', '.wav', '.m4a', '.ogg',
        '.mp4', '.avi', '.mov', '.wmv'
    }
    
    # Security patterns
    MALICIOUS_PATTERNS = [
        # SQL Injection patterns
        r'(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)',
        r'(\b(or|and)\s+\d+\s*=\s*\d+)',
        r'(\b(or|and)\s+[\'"].*[\'"])',
        r'(--\s|#\s|/\*|\*/)',  # More specific SQL comment patterns
        
        # XSS patterns
        r'<script[^>]*>.*?</script>',
        r'<iframe[^>]*>.*?</iframe>',
        r'javascript:',
        r'vbscript:',
        r'on\w+\s*=',
        r'<\s*img[^>]+src\s*=\s*[\'"]?javascript:',
        
        # Command injection patterns
        r'(\||&|;|`|\$\(|\${)',
        r'(nc|netcat|wget|curl|ping|nslookup|dig)\s+',
        
        # Path traversal patterns
        r'\.\./',
        r'\.\.\\',
        r'%2e%2e%2f',
        r'%2e%2e%5c',
        
        # LDAP injection patterns
        r'(\*|\(|\)|&|\||!)',
        
        # NoSQL injection patterns
        r'(\$where|\$ne|\$gt|\$lt|\$regex)',
        
        # Template injection patterns
        r'(\{\{|\}\}|\{%|%\})',
        
        # Server-side includes
        r'<!--\s*#\s*(exec|include|echo|config)',
    ]
    
    # Compiled regex patterns for performance
    COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) 
                        for pattern in MALICIOUS_PATTERNS]


class ValidationResult:
    """Result of input validation."""
    
    def __init__(self, is_valid: bool = True, violations: List[str] = None, 
                 risk_level: str = 'low', sanitized_data: Any = None):
        self.is_valid = is_valid
        self.violations = violations or []
        self.risk_level = risk_level  # 'low', 'medium', 'high'
        self.sanitized_data = sanitized_data


class InputValidator:
    """Comprehensive input validator and sanitizer."""
    
    def __init__(self):
        self.config = ValidationConfig()
    
    def validate_request_size(self, request: Request) -> ValidationResult:
        """Validate request size limits."""
        violations = []
        risk_level = 'low'
        
        # Check content length
        content_length = request.headers.get('content-length')
        if content_length:
            try:
                size = int(content_length)
                if size > self.config.MAX_REQUEST_SIZE:
                    violations.append(f'Request size {size} exceeds maximum {self.config.MAX_REQUEST_SIZE}')
                    risk_level = 'high'
            except ValueError:
                violations.append('Invalid content-length header')
                risk_level = 'medium'
        
        # Check URL length
        if len(str(request.url)) > self.config.MAX_URL_LENGTH:
            violations.append(f'URL length exceeds maximum {self.config.MAX_URL_LENGTH}')
            risk_level = 'medium'
        
        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level
        )
    
    def validate_headers(self, request: Request) -> ValidationResult:
        """Validate request headers."""
        violations = []
        risk_level = 'low'
        
        for name, value in request.headers.items():
            # Check header size
            if len(value) > self.config.MAX_HEADER_SIZE:
                violations.append(f'Header {name} exceeds maximum size')
                risk_level = 'medium'
            
            # Check for malicious patterns in headers
            if self._contains_malicious_patterns(value):
                violations.append(f'Malicious pattern detected in header {name}')
                risk_level = 'high'
        
        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level
        )
    
    def validate_url_and_params(self, request: Request) -> ValidationResult:
        """Validate URL path and query parameters."""
        violations = []
        risk_level = 'low'
        
        # Decode and check URL path
        try:
            decoded_path = unquote(str(request.url.path))
            if self._contains_malicious_patterns(decoded_path):
                violations.append('Malicious pattern detected in URL path')
                risk_level = 'high'
        except Exception as e:
            violations.append(f'URL decoding error: {str(e)}')
            risk_level = 'medium'
        
        # Check query parameters
        for param_name, param_value in request.query_params.items():
            if len(param_value) > self.config.MAX_QUERY_PARAM_LENGTH:
                violations.append(f'Query parameter {param_name} exceeds maximum length')
                risk_level = 'medium'
            
            if self._contains_malicious_patterns(param_value):
                violations.append(f'Malicious pattern detected in query parameter {param_name}')
                risk_level = 'high'
        
        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level
        )
    
    def _contains_malicious_patterns(self, content: str) -> bool:
        """Check if content contains malicious patterns."""
        if not isinstance(content, str):
            return False

        for pattern in self.config.COMPILED_PATTERNS:
            if pattern.search(content):
                return True

        return False

    async def validate_json_body(self, request: Request) -> ValidationResult:
        """Validate JSON request body."""
        violations = []
        risk_level = 'low'
        sanitized_data = None

        try:
            # Check content type
            content_type = request.headers.get('content-type', '').lower()
            if not any(allowed in content_type for allowed in self.config.ALLOWED_CONTENT_TYPES):
                violations.append(f'Unsupported content type: {content_type}')
                risk_level = 'medium'
                return ValidationResult(False, violations, risk_level)

            # Only process JSON content
            if 'application/json' not in content_type:
                return ValidationResult(True)

            # Read and parse JSON body
            body = await request.body()
            if not body:
                return ValidationResult(True)

            try:
                json_data = json.loads(body.decode('utf-8'))
                sanitized_data = self._sanitize_json_data(json_data)

                # Validate JSON structure
                validation_result = self._validate_json_structure(json_data)
                violations.extend(validation_result.violations)
                if validation_result.risk_level == 'high':
                    risk_level = 'high'
                elif validation_result.risk_level == 'medium' and risk_level == 'low':
                    risk_level = 'medium'

            except json.JSONDecodeError as e:
                violations.append(f'Invalid JSON format: {str(e)}')
                risk_level = 'medium'
            except UnicodeDecodeError as e:
                violations.append(f'Invalid UTF-8 encoding: {str(e)}')
                risk_level = 'medium'

        except Exception as e:
            violations.append(f'Error processing request body: {str(e)}')
            risk_level = 'high'

        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level,
            sanitized_data=sanitized_data
        )

    def _validate_json_structure(self, data: Any, depth: int = 0) -> ValidationResult:
        """Recursively validate JSON structure."""
        violations = []
        risk_level = 'low'

        # Check nesting depth
        if depth > self.config.MAX_JSON_DEPTH:
            violations.append(f'JSON nesting depth exceeds maximum {self.config.MAX_JSON_DEPTH}')
            return ValidationResult(False, violations, 'high')

        if isinstance(data, dict):
            for key, value in data.items():
                # Validate key
                if not isinstance(key, str):
                    violations.append('Non-string keys not allowed')
                    risk_level = 'medium'
                elif self._contains_malicious_patterns(key):
                    violations.append(f'Malicious pattern detected in key: {key}')
                    risk_level = 'high'

                # Recursively validate value
                result = self._validate_json_structure(value, depth + 1)
                violations.extend(result.violations)
                if result.risk_level == 'high':
                    risk_level = 'high'
                elif result.risk_level == 'medium' and risk_level == 'low':
                    risk_level = 'medium'

        elif isinstance(data, list):
            for item in data:
                result = self._validate_json_structure(item, depth + 1)
                violations.extend(result.violations)
                if result.risk_level == 'high':
                    risk_level = 'high'
                elif result.risk_level == 'medium' and risk_level == 'low':
                    risk_level = 'medium'

        elif isinstance(data, str):
            if self._contains_malicious_patterns(data):
                violations.append('Malicious pattern detected in string value')
                risk_level = 'high'

        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level
        )

    def _sanitize_json_data(self, data: Any) -> Any:
        """Sanitize JSON data by removing/escaping dangerous content."""
        if isinstance(data, dict):
            return {key: self._sanitize_json_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_json_data(item) for item in data]
        elif isinstance(data, str):
            return self._sanitize_string(data)
        else:
            return data

    def _sanitize_string(self, text: str) -> str:
        """Sanitize string content."""
        if not isinstance(text, str):
            return text

        # Remove null bytes
        text = text.replace('\x00', '')

        # Escape HTML entities
        text = text.replace('<', '&lt;').replace('>', '&gt;')

        # Remove control characters except common whitespace
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')

        return text

    def validate_content_type(self, request: Request) -> ValidationResult:
        """Validate request content type."""
        violations = []
        risk_level = 'low'

        content_type = request.headers.get('content-type', '').lower()
        if content_type and not any(allowed in content_type for allowed in self.config.ALLOWED_CONTENT_TYPES):
            violations.append(f'Unsupported content type: {content_type}')
            risk_level = 'medium'

        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations,
            risk_level=risk_level
        )


# Public endpoints that don't require validation
PUBLIC_ENDPOINTS = {
    '/health',
    '/docs',
    '/redoc',
    '/openapi.json',
    '/config/check',
    '/favicon.ico'
}

# Initialize validator instance
validator = InputValidator()


def _is_public_endpoint(path: str) -> bool:
    """Check if the endpoint is public and doesn't require validation."""
    return any(path.startswith(public_path) for public_path in PUBLIC_ENDPOINTS)


def _should_skip_validation(request: Request) -> bool:
    """Determine if validation should be skipped for this request."""
    # Skip validation for public endpoints
    if _is_public_endpoint(request.url.path):
        return True

    # Skip validation for OPTIONS requests (CORS preflight)
    if request.method == "OPTIONS":
        return True

    return False


async def input_validation_middleware(
    request: Request, call_next: Callable[[Request], Awaitable[Response]]
) -> Response:
    """
    Comprehensive input validation and sanitization middleware.

    This middleware validates all incoming requests for:
    - Request size limits
    - Malicious patterns (SQL injection, XSS, etc.)
    - Content type validation
    - JSON structure validation
    - Header validation
    - URL and parameter validation

    Args:
        request: The incoming request
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler

    Raises:
        HTTPException: If validation fails with high risk level
    """
    # Skip validation for public endpoints and OPTIONS requests
    if _should_skip_validation(request):
        return await call_next(request)

    try:
        # Validate request size
        size_result = validator.validate_request_size(request)
        if not size_result.is_valid and size_result.risk_level == 'high':
            logger.warning(f"Request size validation failed: {size_result.violations}")
            return JSONResponse(
                status_code=413,
                content={"error": "Request too large", "details": size_result.violations}
            )

        # Validate headers
        header_result = validator.validate_headers(request)
        if not header_result.is_valid and header_result.risk_level == 'high':
            logger.warning(f"Header validation failed: {header_result.violations}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid headers", "details": header_result.violations}
            )

        # Validate URL and parameters
        url_result = validator.validate_url_and_params(request)
        if not url_result.is_valid and url_result.risk_level == 'high':
            logger.warning(f"URL validation failed: {url_result.violations}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid URL or parameters", "details": url_result.violations}
            )

        # Validate content type
        content_type_result = validator.validate_content_type(request)
        if not content_type_result.is_valid:
            logger.warning(f"Content type validation failed: {content_type_result.violations}")
            return JSONResponse(
                status_code=415,
                content={"error": "Unsupported media type", "details": content_type_result.violations}
            )

        # Validate JSON body if present
        json_result = await validator.validate_json_body(request)
        if not json_result.is_valid and json_result.risk_level == 'high':
            logger.warning(f"JSON validation failed: {json_result.violations}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid JSON data", "details": json_result.violations}
            )

        # Log medium-risk violations but allow request to proceed
        all_violations = (
            size_result.violations +
            header_result.violations +
            url_result.violations +
            content_type_result.violations +
            json_result.violations
        )

        if all_violations:
            logger.info(f"Input validation warnings for {request.url.path}: {all_violations}")

        # Store sanitized data in request state if available
        if json_result.sanitized_data is not None:
            request.state.sanitized_json = json_result.sanitized_data

        # Proceed with the request
        response = await call_next(request)
        return response

    except Exception as e:
        logger.error(f"Input validation middleware error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": "Internal validation error"}
        )
