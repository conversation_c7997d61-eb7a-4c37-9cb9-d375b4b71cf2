"""Security headers middleware for the PI Lawyer AI system."""
import logging
import os
from typing import Awaitable, Callable, Dict

from fastapi import Request
from fastapi.responses import Response

logger = logging.getLogger(__name__)

# Default security headers
DEFAULT_SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "1; mode=block",
    "X-Frame-Options": "DENY",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    "Cache-Control": "no-store, max-age=0",
    "Pragma": "no-cache",
}

# Content Security Policy
DEFAULT_CSP = (
    "default-src 'self'; "
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
    "style-src 'self' 'unsafe-inline'; "
    "img-src 'self' data:; "
    "font-src 'self'; "
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co; "
    "frame-ancestors 'none'; "
    "form-action 'self'; "
    "base-uri 'self'; "
    "object-src 'none'"
)


async def security_headers_middleware(
    request: Request, call_next: Callable[[Request], Awaitable[Response]]
) -> Response:
    """
    Middleware to add security headers to all responses.
    
    Args:
        request: The incoming request
        call_next: The next middleware or route handler
        
    Returns:
        The response with added security headers
    """
    # Process the request and get the response
    response = await call_next(request)
    
    # Get security headers from environment variables or use defaults
    security_headers = _get_security_headers()
    
    # Add security headers to the response
    for header_name, header_value in security_headers.items():
        response.headers[header_name] = header_value
    
    # Add Content-Security-Policy header if enabled
    if os.getenv("ENABLE_CSP", "true").lower() == "true":
        csp = os.getenv("CONTENT_SECURITY_POLICY", DEFAULT_CSP)
        response.headers["Content-Security-Policy"] = csp
    
    return response


def _get_security_headers() -> Dict[str, str]:
    """
    Get security headers from environment variables or use defaults.
    
    Returns:
        A dictionary of security headers
    """
    headers = DEFAULT_SECURITY_HEADERS.copy()
    
    # Override headers from environment variables
    env_headers = {
        "X_CONTENT_TYPE_OPTIONS": "X-Content-Type-Options",
        "X_XSS_PROTECTION": "X-XSS-Protection",
        "X_FRAME_OPTIONS": "X-Frame-Options",
        "REFERRER_POLICY": "Referrer-Policy",
        "PERMISSIONS_POLICY": "Permissions-Policy",
        "CACHE_CONTROL": "Cache-Control",
        "PRAGMA": "Pragma",
    }
    
    for env_name, header_name in env_headers.items():
        env_value = os.getenv(env_name)
        if env_value:
            headers[header_name] = env_value
    
    return headers
