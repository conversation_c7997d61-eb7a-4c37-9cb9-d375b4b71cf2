"""Enhanced rate limiting middleware for the PI Lawyer AI system."""
import hashlib
import logging
import os
import time
from typing import Any, Awaitable, Callable, Dict, Tuple

from fastapi import Request, status
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

# In-memory storage for rate limiting
# Production-optimized: Includes cleanup and monitoring capabilities
# For multi-instance deployments, consider Redis for shared state
rate_limit_store: Dict[str, Dict[str, Any]] = {}

# Rate limiting policies for different endpoint types
RATE_LIMIT_POLICIES = {
    "auth": {
        "limit": int(os.getenv("RATE_LIMIT_AUTH", "10")),
        "window": int(os.getenv("RATE_LIMIT_AUTH_WINDOW", "300")),
        "endpoints": ["/auth/", "/login", "/register", "/reset-password", "/verify"]
    },
    "upload": {
        "limit": int(os.getenv("RATE_LIMIT_UPLOAD", "20")),
        "window": int(os.getenv("RATE_LIMIT_UPLOAD_WINDOW", "300")),
        "endpoints": ["/upload", "/documents/upload", "/files/"]
    },
    "ai": {
        "limit": int(os.getenv("RATE_LIMIT_AI", "30")),
        "window": int(os.getenv("RATE_LIMIT_AI_WINDOW", "60")),
        "endpoints": ["/copilotkit", "/ai/", "/llm/", "/chat", "/generate"]
    },
    "admin": {
        "limit": int(os.getenv("RATE_LIMIT_ADMIN", "200")),
        "window": int(os.getenv("RATE_LIMIT_ADMIN_WINDOW", "60")),
        "endpoints": ["/admin/", "/superadmin/", "/config/"]
    },
    "webhook": {
        "limit": int(os.getenv("RATE_LIMIT_WEBHOOK", "1000")),
        "window": int(os.getenv("RATE_LIMIT_WEBHOOK_WINDOW", "60")),
        "endpoints": ["/webhooks/", "/webhook"]
    },
    "subscription": {
        "limit": int(os.getenv("RATE_LIMIT_SUBSCRIPTION", "50")),
        "window": int(os.getenv("RATE_LIMIT_SUBSCRIPTION_WINDOW", "60")),
        "endpoints": ["/subscription/", "/plans/", "/addons/", "/billing/"]
    },
    "general": {
        "limit": int(os.getenv("RATE_LIMIT", "100")),
        "window": int(os.getenv("RATE_LIMIT_WINDOW", "60")),
        "endpoints": []  # Default for all other endpoints
    }
}


async def rate_limit_middleware(
    request: Request, call_next: Callable[[Request], Awaitable[Any]]
) -> Any:
    """
    Enhanced middleware to implement rate limiting with endpoint-specific policies.

    Args:
        request: The incoming request
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler, or a 429 response
        if rate limited
    """
    # Skip rate limiting for public endpoints
    if _is_public_endpoint(request.url.path):
        return await call_next(request)

    # Skip rate limiting if disabled
    if os.getenv("ENABLE_RATE_LIMIT", "true").lower() != "true":
        logger.info("Rate limiting disabled via ENABLE_RATE_LIMIT=false")
        return await call_next(request)

    # Get client identifier (IP address or user ID if authenticated)
    client_id = _get_client_identifier(request)

    # Get rate limit policy for this endpoint
    policy_name, rate_limit, rate_limit_window = _get_rate_limit_policy(
        request.url.path
    )

    # Create a unique key for this policy and client
    rate_limit_key = f"{policy_name}:{client_id}"

    # Check if client is rate limited
    if _is_rate_limited(rate_limit_key, rate_limit, rate_limit_window):
        logger.warning(
            f"Rate limit exceeded for client: {client_id}, "
            f"endpoint: {request.url.path}, policy: {policy_name}, "
            f"limit: {rate_limit}/{rate_limit_window}s"
        )

        # Enhanced rate limit response with policy information
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "detail": (
                    f"Rate limit exceeded for {policy_name} endpoints. "
                    "Please try again later."
                ),
                "policy": policy_name,
                "limit": rate_limit,
                "window": rate_limit_window,
                "retry_after": rate_limit_window
            },
            headers={
                "Retry-After": str(rate_limit_window),
                "X-RateLimit-Policy": policy_name,
                "X-RateLimit-Limit": str(rate_limit),
                "X-RateLimit-Window": str(rate_limit_window)
            },
        )

    # Log successful rate limit check for monitoring
    logger.debug(
        f"Rate limit check passed for client: {client_id}, "
        f"endpoint: {request.url.path}, policy: {policy_name}"
    )

    # Proceed with the request
    return await call_next(request)


def _get_rate_limit_policy(path: str) -> Tuple[str, int, int]:
    """
    Determine the appropriate rate limit policy for a given endpoint path.

    Args:
        path: The request path

    Returns:
        Tuple of (policy_name, rate_limit, window_seconds)
    """
    # Check each policy to see if the path matches
    for policy_name, policy_config in RATE_LIMIT_POLICIES.items():
        if policy_name == "general":
            continue  # Skip general, it's the default

        for endpoint_pattern in policy_config["endpoints"]:
            if path.startswith(endpoint_pattern):
                return (
                    policy_name,
                    policy_config["limit"],
                    policy_config["window"]
                )

    # Default to general policy
    general_policy = RATE_LIMIT_POLICIES["general"]
    return ("general", general_policy["limit"], general_policy["window"])


def _is_public_endpoint(path: str) -> bool:
    """
    Check if the path is a public endpoint that doesn't require rate limiting.

    Args:
        path: The request path

    Returns:
        True if the path is a public endpoint, False otherwise
    """
    public_paths = [
        "/health",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/config/check",
        "/metrics",  # Added metrics endpoint
        "/status",   # Added status endpoint
    ]

    return any(path.startswith(public_path) for public_path in public_paths)


def _get_client_identifier(request: Request) -> str:
    """
    Get a unique identifier for the client.

    Args:
        request: The incoming request

    Returns:
        A unique identifier for the client
    """
    # Check for X-User-ID header (used in tests)
    header_user_id = request.headers.get("X-User-ID")
    if header_user_id:
        return f"user:{header_user_id}"

    # Try to get user ID from request state (set by JWT middleware)
    user_id = getattr(request.state, "user_id", None)
    if user_id:
        return f"user:{user_id}"

    # Fall back to IP address
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Get the first IP in the list (client IP)
        client_ip = forwarded_for.split(",")[0].strip()
    else:
        # Get the client IP from the request
        client_ip = request.client.host if request.client else "unknown"

    # Hash the IP address to protect privacy
    hashed_ip = hashlib.sha256(client_ip.encode()).hexdigest()
    return f"ip:{hashed_ip}"


def _is_rate_limited(client_id: str, rate_limit: int, window: int) -> bool:
    """
    Enhanced rate limiting check with improved tracking and monitoring.

    Args:
        client_id: The client identifier (includes policy prefix)
        rate_limit: The maximum number of requests allowed in the window
        window: The time window in seconds

    Returns:
        True if the client is rate limited, False otherwise
    """
    current_time = time.time()

    # Periodic cleanup to prevent memory leaks
    if len(rate_limit_store) > 10000:  # Cleanup threshold
        _clean_rate_limit_store()

    # Get or create client entry
    if client_id not in rate_limit_store:
        rate_limit_store[client_id] = {
            "count": 0,
            "window_start": current_time,
            "first_request": current_time,
            "last_request": current_time,
        }

    client_data = rate_limit_store[client_id]

    # Check if window has expired
    if current_time - client_data["window_start"] > window:
        # Reset window but preserve tracking data
        client_data["count"] = 1
        client_data["window_start"] = current_time
        client_data["last_request"] = current_time
        return False

    # Update tracking data
    client_data["count"] += 1
    client_data["last_request"] = current_time

    # Check if rate limit exceeded
    is_limited = client_data["count"] > rate_limit

    # Log rate limiting events for monitoring
    if is_limited:
        logger.warning(
            f"Rate limit exceeded: {client_id}, "
            f"count: {client_data['count']}, limit: {rate_limit}, "
            f"window: {window}s"
        )

    return is_limited


def _clean_rate_limit_store() -> None:
    """
    Enhanced cleanup of expired entries in the rate limit store.
    Prevents memory leaks and provides monitoring data.
    """
    current_time = time.time()
    initial_count = len(rate_limit_store)

    # Find expired entries (consider the longest window for safety)
    max_window = max(
        policy["window"] for policy in RATE_LIMIT_POLICIES.values()
    )

    expired_keys = []
    for key, data in rate_limit_store.items():
        # Remove entries that haven't been accessed recently
        time_since_last_request = current_time - data.get(
            "last_request", data["window_start"]
        )
        if time_since_last_request > max_window * 2:  # Double the window for safety
            expired_keys.append(key)

    # Remove expired entries
    for key in expired_keys:
        del rate_limit_store[key]

    cleaned_count = len(expired_keys)
    remaining_count = len(rate_limit_store)

    if cleaned_count > 0:
        logger.info(
            f"Rate limit store cleanup: removed {cleaned_count} expired entries, "
            f"{remaining_count} entries remaining (was {initial_count})"
        )


def get_rate_limit_stats() -> Dict[str, Any]:
    """
    Get current rate limiting statistics for monitoring.

    Returns:
        Dictionary containing rate limiting statistics
    """
    current_time = time.time()

    # Count active entries by policy
    policy_counts = {}
    total_active = 0

    for key, data in rate_limit_store.items():
        policy_name = key.split(":", 1)[0] if ":" in key else "unknown"

        # Check if entry is still active (within window)
        policy_config = RATE_LIMIT_POLICIES.get(
            policy_name, RATE_LIMIT_POLICIES["general"]
        )
        window = policy_config["window"]

        if current_time - data["window_start"] <= window:
            policy_counts[policy_name] = policy_counts.get(policy_name, 0) + 1
            total_active += 1

    return {
        "total_entries": len(rate_limit_store),
        "active_entries": total_active,
        "policy_breakdown": policy_counts,
        "policies_configured": list(RATE_LIMIT_POLICIES.keys()),
        "cleanup_threshold": 10000,
        "timestamp": current_time
    }
