"""
CopilotKit integration route for FastAPI.

This module provides endpoints for CopilotKit to interact with our LangGraph agents.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Request

from ..agents.research_copilot_handler import ResearchCopilotHandler

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/copilotkit",
    tags=["copilotkit"],
    responses={404: {"description": "Not found"}},
)

# Initialize handlers
research_handler = ResearchCopilotHandler()

# Agent mapping
AGENT_HANDLERS = {"research_agent": research_handler}


@router.post("")
async def handle_copilotkit_request(request: Request):
    """
    Main endpoint for CopilotKit requests.
    Routes requests to the appropriate agent handler based on the agent name.
    """
    try:
        # Parse request body
        body = await request.json()
        logger.info(f"Received CopilotKit request: {str(body)[:200]}...")

        # Extract GraphQL operation if present
        operation_name = body.get("operationName")

        # Handle different request structures
        if operation_name == "generateCopilotResponse":
            # Extract GraphQL variables
            variables = body.get("variables", {})
            data = variables.get("data", {})

            # Get agent name from variables or fall back to default
            agent_name = data.get("agent", "research_agent")

            if agent_name not in AGENT_HANDLERS:
                logger.warning(f"Unknown agent: {agent_name}")
                return format_graphql_response(
                    {
                        "messages": [
                            {
                                "content": [
                                    f"Agent '{agent_name}' not found. Please specify a valid agent."
                                ],
                                "role": "assistant",
                            }
                        ],
                        "done": True,
                        "threadId": data.get("threadId", "unknown-thread"),
                    }
                )

            # Process with the appropriate handler
            handler = AGENT_HANDLERS[agent_name]
            result = await handler.handle_request(data)

            # Return formatted GraphQL response
            return format_graphql_response(result)

        else:
            # Direct API request (non-GraphQL)
            agent_name = body.get("agent", "research_agent")

            if agent_name not in AGENT_HANDLERS:
                logger.warning(f"Unknown agent: {agent_name}")
                raise HTTPException(
                    status_code=404, detail=f"Agent '{agent_name}' not found"
                )

            # Process with the appropriate handler
            handler = AGENT_HANDLERS[agent_name]
            return await handler.handle_request(body)

    except Exception as e:
        logger.exception(f"Error handling CopilotKit request: {str(e)}")

        # Return error in GraphQL format if it was a GraphQL request
        if body.get("operationName") == "generateCopilotResponse":
            return format_graphql_response(
                {
                    "messages": [
                        {
                            "content": [
                                "I encountered an error processing your request. Please try again."
                            ],
                            "role": "assistant",
                        }
                    ],
                    "done": True,
                    "threadId": body.get("variables", {})
                    .get("data", {})
                    .get("threadId", "error-thread"),
                }
            )
        else:
            # Regular error for direct API
            raise HTTPException(status_code=500, detail=str(e))


def format_graphql_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Format a result dictionary as a GraphQL response."""
    return {"data": {"generateCopilotResponse": result}}


@router.options("")
async def options_handler():
    """Handle CORS preflight requests."""
    return {}


@router.get("/available-agents")
async def get_available_agents():
    """Return a list of available agents."""
    return {
        "data": {
            "availableAgents": [
                {
                    "id": "research_agent",
                    "name": "Legal Research Assistant",
                    "description": "Specialized agent for legal research in personal injury cases",
                    "capabilities": [
                        "Texas law research",
                        "Personal injury expertise",
                        "Case law citation",
                    ],
                }
                # Add more agents as they become available
            ]
        }
    }
