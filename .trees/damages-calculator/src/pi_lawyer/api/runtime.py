"""
Minimal FastAPI Runtime for PI Lawyer AI

This is a simplified runtime that focuses on getting the basic server running
without complex agent dependencies that cause startup crashes.
"""

import os
import sys
import logging

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("pi_lawyer.api")

logger.info("Starting minimal PI Lawyer AI runtime...")

# Import FastAPI with error handling
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    logger.info("FastAPI imported successfully")
except ImportError as e:
    logger.error(f"Failed to import FastAPI: {e}")
    sys.exit(1)

# Create a minimal FastAPI app
logger.info("Creating FastAPI application...")
app = FastAPI(
    title="PI Lawyer AI API",
    description="Minimal runtime for PI Lawyer AI",
    version="1.0.0"
)

# Add basic CORS middleware
logger.info("Adding CORS middleware...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, this should be restricted
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("FastAPI application created successfully")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring and container health checks."""
    import platform
    import time
    from datetime import datetime, timezone

    # Get application start time (or use current time if not available)
    start_time = getattr(app, "start_time", time.time())
    uptime_seconds = int(time.time() - start_time)

    # Basic system information
    system_info = {
        "python_version": platform.python_version(),
        "system": platform.system(),
        "platform": platform.platform(),
    }

    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": os.environ.get("APP_ENV", "development"),
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": uptime_seconds,
        "system_info": system_info,
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint that returns a simple message."""
    return {"message": "PI Lawyer AI API is running"}

def main():
    """Run the uvicorn server."""
    import uvicorn
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "pi_lawyer.api.runtime:app",
        host="0.0.0.0",
        port=port,
    )

if __name__ == "__main__":
    main()
