"""
API router initialization.

This module initializes and registers all API routers for the application.
"""

from fastapi import APIRouter

from .authored_document_embedding import router as authored_document_embedding_router

# Import all api routers
from .health_api import router as health_router

# Create main router
api_router = APIRouter()

# Register all routers
api_router.include_router(health_router)
api_router.include_router(authored_document_embedding_router)

__all__ = ["api_router"]
