import logging
from datetime import datetime

from fastapi import APIRouter, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware

from ..agents.intake_agent import get_graph

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI()

# Add CORS middleware to the app, not the router
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create router for intake routes
router = APIRouter()


@router.post("/api/intake")
async def handle_intake_request(request: Request):
    """
    Handle intake agent requests through LangGraph
    """
    try:
        # Get request body
        body = await request.json()
        logger.info(f"Incoming request received: {body.get('type', 'unknown')}")

        # Get LangGraph instance
        graph = await get_graph()

        # Process request through LangGraph
        result = await graph.ainvoke(
            state=body.get("state", {}), config=body.get("config", {})
        )

        return result

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return {"error": str(e), "timestamp": datetime.utcnow().isoformat()}


# Include the router in the app
app.include_router(router)
