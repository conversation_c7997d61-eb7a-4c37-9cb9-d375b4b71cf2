"""
API route validation decorators and utilities.

This module provides decorators and utilities for comprehensive input validation
on FastAPI routes, including enhanced Pydantic models and validation patterns.
"""

import logging
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Type, Union

from fastapi import HTTPException, Request
from pydantic import BaseModel, ValidationError, validator
from pydantic.fields import Field

logger = logging.getLogger(__name__)


class ValidationConfig:
    """Configuration for API validation."""
    
    # String validation limits
    MAX_STRING_LENGTH = 10000
    MAX_TEXT_LENGTH = 50000
    MAX_NAME_LENGTH = 100
    MAX_EMAIL_LENGTH = 254
    MAX_PHONE_LENGTH = 20
    MAX_URL_LENGTH = 2048
    
    # Numeric limits
    MAX_INTEGER = 2147483647
    MIN_INTEGER = -2147483648
    MAX_FLOAT = 1e308
    MIN_FLOAT = -1e308
    
    # Collection limits
    MAX_ARRAY_LENGTH = 1000
    MAX_OBJECT_KEYS = 100


class EnhancedBaseModel(BaseModel):
    """Enhanced base model with additional validation."""
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"  # Forbid extra fields
        max_anystr_length = ValidationConfig.MAX_STRING_LENGTH
    
    @validator('*', pre=True)
    def validate_string_length(cls, v, field):
        """Validate string length based on field name patterns."""
        if isinstance(v, str):
            field_name = field.name.lower()
            
            # Apply specific limits based on field name
            if any(name in field_name for name in ['name', 'title', 'label']):
                if len(v) > ValidationConfig.MAX_NAME_LENGTH:
                    raise ValueError(f'Field {field.name} exceeds maximum length of {ValidationConfig.MAX_NAME_LENGTH}')
            elif 'email' in field_name:
                if len(v) > ValidationConfig.MAX_EMAIL_LENGTH:
                    raise ValueError(f'Email field exceeds maximum length of {ValidationConfig.MAX_EMAIL_LENGTH}')
            elif 'phone' in field_name:
                if len(v) > ValidationConfig.MAX_PHONE_LENGTH:
                    raise ValueError(f'Phone field exceeds maximum length of {ValidationConfig.MAX_PHONE_LENGTH}')
            elif 'url' in field_name or 'link' in field_name:
                if len(v) > ValidationConfig.MAX_URL_LENGTH:
                    raise ValueError(f'URL field exceeds maximum length of {ValidationConfig.MAX_URL_LENGTH}')
            elif any(name in field_name for name in ['description', 'content', 'text', 'message']):
                if len(v) > ValidationConfig.MAX_TEXT_LENGTH:
                    raise ValueError(f'Text field {field.name} exceeds maximum length of {ValidationConfig.MAX_TEXT_LENGTH}')
        
        return v


class SecureStringField:
    """Factory for creating secure string fields with validation."""
    
    @staticmethod
    def create(
        max_length: int = ValidationConfig.MAX_STRING_LENGTH,
        min_length: int = 0,
        regex: Optional[str] = None,
        description: Optional[str] = None,
        **kwargs
    ):
        """Create a secure string field with validation."""
        return Field(
            ...,
            min_length=min_length,
            max_length=max_length,
            regex=regex,
            description=description,
            **kwargs
        )
    
    @staticmethod
    def name(max_length: int = ValidationConfig.MAX_NAME_LENGTH, **kwargs):
        """Create a name field."""
        return SecureStringField.create(
            max_length=max_length,
            min_length=1,
            regex=r'^[a-zA-Z0-9\s\-_.]+$',
            description="Name field with alphanumeric characters, spaces, hyphens, underscores, and periods",
            **kwargs
        )
    
    @staticmethod
    def email(**kwargs):
        """Create an email field."""
        return Field(
            ...,
            max_length=ValidationConfig.MAX_EMAIL_LENGTH,
            regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            description="Valid email address",
            **kwargs
        )
    
    @staticmethod
    def phone(**kwargs):
        """Create a phone field."""
        return Field(
            ...,
            max_length=ValidationConfig.MAX_PHONE_LENGTH,
            regex=r'^\+?[1-9]\d{1,14}$',
            description="Valid phone number",
            **kwargs
        )
    
    @staticmethod
    def uuid(**kwargs):
        """Create a UUID field."""
        return Field(
            ...,
            regex=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            description="Valid UUID",
            **kwargs
        )


class SecureIntegerField:
    """Factory for creating secure integer fields."""
    
    @staticmethod
    def create(
        min_value: int = ValidationConfig.MIN_INTEGER,
        max_value: int = ValidationConfig.MAX_INTEGER,
        description: Optional[str] = None,
        **kwargs
    ):
        """Create a secure integer field."""
        return Field(
            ...,
            ge=min_value,
            le=max_value,
            description=description,
            **kwargs
        )
    
    @staticmethod
    def positive(**kwargs):
        """Create a positive integer field."""
        return SecureIntegerField.create(min_value=1, **kwargs)
    
    @staticmethod
    def non_negative(**kwargs):
        """Create a non-negative integer field."""
        return SecureIntegerField.create(min_value=0, **kwargs)


class SecureListField:
    """Factory for creating secure list fields."""
    
    @staticmethod
    def create(
        item_type: Type,
        max_items: int = ValidationConfig.MAX_ARRAY_LENGTH,
        min_items: int = 0,
        description: Optional[str] = None,
        **kwargs
    ):
        """Create a secure list field."""
        return Field(
            default_factory=list,
            min_items=min_items,
            max_items=max_items,
            description=description,
            **kwargs
        )


def validate_request_data(model_class: Type[BaseModel]):
    """
    Decorator to validate request data using a Pydantic model.
    
    Args:
        model_class: The Pydantic model class to use for validation
        
    Returns:
        Decorated function with request validation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Find the request object in the arguments
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(
                    status_code=500,
                    detail="Request object not found in route handler"
                )
            
            try:
                # Get JSON data from request
                if hasattr(request.state, 'sanitized_json') and request.state.sanitized_json:
                    json_data = request.state.sanitized_json
                else:
                    json_data = await request.json()
                
                # Validate using the model
                validated_data = model_class(**json_data)
                
                # Add validated data to request state
                request.state.validated_data = validated_data
                
                # Call the original function
                return await func(*args, **kwargs)
                
            except ValidationError as e:
                logger.warning(f"Validation error in {func.__name__}: {e}")
                raise HTTPException(
                    status_code=422,
                    detail={
                        "error": "Validation failed",
                        "details": e.errors()
                    }
                )
            except Exception as e:
                logger.error(f"Unexpected error in validation decorator: {e}")
                raise HTTPException(
                    status_code=500,
                    detail="Internal validation error"
                )
        
        return wrapper
    return decorator


def get_validated_data(request: Request, model_class: Type[BaseModel]) -> BaseModel:
    """
    Get validated data from request state.
    
    Args:
        request: The FastAPI request object
        model_class: The expected model class
        
    Returns:
        The validated data instance
        
    Raises:
        HTTPException: If validated data is not found or is wrong type
    """
    if not hasattr(request.state, 'validated_data'):
        raise HTTPException(
            status_code=500,
            detail="No validated data found. Ensure @validate_request_data decorator is used."
        )
    
    validated_data = request.state.validated_data
    if not isinstance(validated_data, model_class):
        raise HTTPException(
            status_code=500,
            detail=f"Validated data is not of expected type {model_class.__name__}"
        )
    
    return validated_data
