"""
File upload validation utilities.

This module provides comprehensive validation for file uploads including
type checking, size limits, content scanning, and security validation.
"""

import hashlib
import logging
import mimetypes
import os
import re
from pathlib import Path
from typing import BinaryIO, Dict, List, Optional, Set, Tuple

from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile

# Optional import for python-magic
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    logging.warning("python-magic not available - file content validation will be limited")

logger = logging.getLogger(__name__)


class FileValidationError(Exception):
    """Exception raised when file validation fails."""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Optional[str] = None):
        super().__init__(message)
        self.field = field
        self.value = value


class FileValidator:
    """Comprehensive file upload validator."""
    
    # File size limits (in bytes)
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_DOCUMENT_SIZE = 50 * 1024 * 1024  # 50MB
    MAX_AUDIO_SIZE = 100 * 1024 * 1024  # 100MB
    MAX_VIDEO_SIZE = 500 * 1024 * 1024  # 500MB
    
    # Allowed file extensions and MIME types
    ALLOWED_EXTENSIONS = {
        # Documents
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.txt': 'text/plain',
        '.rtf': 'application/rtf',
        '.odt': 'application/vnd.oasis.opendocument.text',
        
        # Images
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.tiff': 'image/tiff',
        '.webp': 'image/webp',
        
        # Audio
        '.mp3': 'audio/mpeg',
        '.wav': 'audio/wav',
        '.m4a': 'audio/x-m4a',
        '.ogg': 'audio/ogg',
        '.flac': 'audio/flac',
        
        # Video
        '.mp4': 'video/mp4',
        '.avi': 'video/x-msvideo',
        '.mov': 'video/quicktime',
        '.wmv': 'video/x-ms-wmv',
        '.webm': 'video/webm',
    }
    
    # Dangerous file patterns
    DANGEROUS_PATTERNS = [
        # Executable files
        r'\.exe$', r'\.bat$', r'\.cmd$', r'\.com$', r'\.scr$',
        r'\.pif$', r'\.vbs$', r'\.js$', r'\.jar$', r'\.app$',
        
        # Script files
        r'\.php$', r'\.asp$', r'\.jsp$', r'\.py$', r'\.rb$',
        r'\.pl$', r'\.sh$', r'\.ps1$',
        
        # Archive files that could contain malware
        r'\.zip$', r'\.rar$', r'\.7z$', r'\.tar$', r'\.gz$',
        
        # Double extensions
        r'\.[^.]+\.(exe|bat|cmd|com|scr|pif|vbs|js)$',
    ]
    
    # Magic number signatures for file type validation
    MAGIC_SIGNATURES = {
        'application/pdf': [b'%PDF'],
        'image/jpeg': [b'\xff\xd8\xff'],
        'image/png': [b'\x89PNG\r\n\x1a\n'],
        'image/gif': [b'GIF87a', b'GIF89a'],
        'application/msword': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
        'audio/mpeg': [b'ID3', b'\xff\xfb', b'\xff\xf3', b'\xff\xf2'],
        'video/mp4': [b'ftyp'],
    }
    
    def __init__(self):
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in self.DANGEROUS_PATTERNS
        ]
    
    def validate_filename(self, filename: str) -> None:
        """
        Validate filename for security issues.
        
        Args:
            filename: The filename to validate
            
        Raises:
            FileValidationError: If filename is invalid
        """
        if not filename:
            raise FileValidationError("Filename cannot be empty")
        
        if len(filename) > 255:
            raise FileValidationError("Filename too long (max 255 characters)")
        
        # Check for dangerous patterns
        for pattern in self.compiled_patterns:
            if pattern.search(filename):
                raise FileValidationError(
                    f"Dangerous file type detected: {filename}",
                    field="filename",
                    value=filename
                )
        
        # Check for path traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            raise FileValidationError(
                "Path traversal attempt detected in filename",
                field="filename",
                value=filename
            )
        
        # Check for null bytes
        if '\x00' in filename:
            raise FileValidationError(
                "Null byte detected in filename",
                field="filename",
                value=filename
            )
        
        logger.debug(f"Filename validated: {filename}")
    
    def validate_file_extension(self, filename: str) -> str:
        """
        Validate file extension against whitelist.
        
        Args:
            filename: The filename to check
            
        Returns:
            The validated file extension
            
        Raises:
            FileValidationError: If extension is not allowed
        """
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        
        if not extension:
            raise FileValidationError(
                "File must have an extension",
                field="extension",
                value=filename
            )
        
        if extension not in self.ALLOWED_EXTENSIONS:
            raise FileValidationError(
                f"File extension '{extension}' not allowed",
                field="extension",
                value=extension
            )
        
        logger.debug(f"File extension validated: {extension}")
        return extension
    
    def validate_file_size(self, file_size: int, content_type: str) -> None:
        """
        Validate file size based on content type.
        
        Args:
            file_size: Size of the file in bytes
            content_type: MIME type of the file
            
        Raises:
            FileValidationError: If file size exceeds limits
        """
        if file_size <= 0:
            raise FileValidationError("File cannot be empty")
        
        # Apply specific size limits based on content type
        if content_type.startswith('image/'):
            max_size = self.MAX_IMAGE_SIZE
        elif content_type.startswith('audio/'):
            max_size = self.MAX_AUDIO_SIZE
        elif content_type.startswith('video/'):
            max_size = self.MAX_VIDEO_SIZE
        elif content_type in ['application/pdf', 'application/msword', 'text/plain']:
            max_size = self.MAX_DOCUMENT_SIZE
        else:
            max_size = self.MAX_FILE_SIZE
        
        if file_size > max_size:
            raise FileValidationError(
                f"File size {file_size} exceeds maximum {max_size} bytes for {content_type}",
                field="file_size",
                value=str(file_size)
            )
        
        logger.debug(f"File size validated: {file_size} bytes for {content_type}")
    
    def validate_mime_type(self, content_type: str, extension: str) -> None:
        """
        Validate MIME type matches file extension.
        
        Args:
            content_type: The reported MIME type
            extension: The file extension
            
        Raises:
            FileValidationError: If MIME type doesn't match extension
        """
        expected_mime = self.ALLOWED_EXTENSIONS.get(extension)
        if not expected_mime:
            raise FileValidationError(
                f"Unknown extension: {extension}",
                field="extension",
                value=extension
            )
        
        # Normalize MIME types for comparison
        content_type = content_type.lower().split(';')[0].strip()
        
        if content_type != expected_mime:
            raise FileValidationError(
                f"MIME type '{content_type}' doesn't match extension '{extension}' (expected '{expected_mime}')",
                field="content_type",
                value=content_type
            )
        
        logger.debug(f"MIME type validated: {content_type} for {extension}")
    
    def validate_file_content(self, file_content: bytes, content_type: str) -> None:
        """
        Validate file content using magic numbers.
        
        Args:
            file_content: The first few bytes of the file
            content_type: The expected MIME type
            
        Raises:
            FileValidationError: If content doesn't match expected type
        """
        if len(file_content) < 10:
            # Not enough content to validate
            return
        
        signatures = self.MAGIC_SIGNATURES.get(content_type, [])
        if not signatures:
            # No signature validation available for this type
            return
        
        # Check if any signature matches
        for signature in signatures:
            if file_content.startswith(signature):
                logger.debug(f"File content validated for {content_type}")
                return
        
        # Special case for MP4 - signature is not at the beginning
        if content_type == 'video/mp4' and b'ftyp' in file_content[:20]:
            logger.debug(f"File content validated for {content_type}")
            return
        
        raise FileValidationError(
            f"File content doesn't match expected type {content_type}",
            field="content_type",
            value=content_type
        )
    
    def calculate_file_hash(self, file_content: bytes) -> str:
        """
        Calculate SHA-256 hash of file content.
        
        Args:
            file_content: The file content
            
        Returns:
            The SHA-256 hash as a hex string
        """
        return hashlib.sha256(file_content).hexdigest()


# Global validator instance
file_validator = FileValidator()


async def validate_upload_file(upload_file: UploadFile) -> Dict[str, str]:
    """
    Comprehensive validation of uploaded file.
    
    Args:
        upload_file: The FastAPI UploadFile object
        
    Returns:
        Dictionary with validation results and metadata
        
    Raises:
        FileValidationError: If validation fails
    """
    if not upload_file.filename:
        raise FileValidationError("No filename provided")
    
    # Validate filename
    file_validator.validate_filename(upload_file.filename)
    
    # Validate extension
    extension = file_validator.validate_file_extension(upload_file.filename)
    
    # Get file size
    file_content = await upload_file.read()
    file_size = len(file_content)
    
    # Reset file position
    await upload_file.seek(0)
    
    # Validate MIME type
    content_type = upload_file.content_type or mimetypes.guess_type(upload_file.filename)[0]
    if not content_type:
        raise FileValidationError("Could not determine file content type")
    
    file_validator.validate_mime_type(content_type, extension)
    
    # Validate file size
    file_validator.validate_file_size(file_size, content_type)
    
    # Validate file content
    file_validator.validate_file_content(file_content[:1024], content_type)
    
    # Calculate file hash
    file_hash = file_validator.calculate_file_hash(file_content)
    
    logger.info(f"File validation successful: {upload_file.filename}")
    
    return {
        'filename': upload_file.filename,
        'content_type': content_type,
        'extension': extension,
        'size': str(file_size),
        'hash': file_hash
    }
