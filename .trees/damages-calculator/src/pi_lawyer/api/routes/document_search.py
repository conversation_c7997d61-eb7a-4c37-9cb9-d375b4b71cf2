"""
Document search API routes for semantic searching using vector embeddings.
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException, status
from pydantic import BaseModel, Field

from pi_lawyer.auth.auth_dependencies import get_db_pool, get_tenant_id, verify_api_key
from pi_lawyer.services.tenant_document_embedding_service import (
    TenantDocumentEmbeddingService,
)

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/document_search", tags=["document_search"])

# Initialize embedding service
embedding_service = TenantDocumentEmbeddingService()


class DocumentSearchRequest(BaseModel):
    """Request model for document semantic search"""

    query: str = Field(..., description="Search query text")
    tenant_id: str = Field(..., description="Tenant ID for isolation")
    user_id: str = Field(..., description="User ID performing the search")
    role: str = Field(..., description="User's role (partner, attorney, etc.)")
    accessible_case_ids: List[str] = Field(
        default=[], description="List of case IDs the user has access to"
    )
    document_type: Optional[str] = Field(None, description="Filter by document type")
    limit: int = Field(10, description="Maximum number of results to return")
    include_preview: bool = Field(
        True, description="Whether to include content previews"
    )


class DocumentSearchResult(BaseModel):
    """Result model for document search matches"""

    document_id: str
    score: float
    metadata: Dict[str, Any]


class DocumentSearchResponse(BaseModel):
    """Response model for document search"""

    results: List[DocumentSearchResult]
    count: int
    query: str


@router.post("", response_model=DocumentSearchResponse)
async def search_documents(
    request: DocumentSearchRequest = Body(...),
    api_key: str = Depends(verify_api_key),
    tenant_id: str = Depends(get_tenant_id),
    db_pool=Depends(get_db_pool),
):
    """
    Search for documents using semantic search with vector embeddings.

    This endpoint performs access control filtering based on the user's role and
    case assignments to ensure users can only search documents they have access to.
    """
    # Verify tenant ID matches the one in the request for additional security
    if tenant_id != request.tenant_id:
        logger.warning(
            f"Tenant ID mismatch in document search: {tenant_id} vs {request.tenant_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant ID mismatch in request",
        )

    try:
        # Build filter parameters for Pinecone
        filter_params = {}

        # Add document type filter if provided
        if request.document_type:
            filter_params["document_type"] = request.document_type

        # Add case ID filter if the user has limited case access
        if request.accessible_case_ids and request.role != "partner":
            filter_params["case_id"] = {"$in": request.accessible_case_ids}

        # Get user context for access control
        user_context = {
            "role": request.role,
            "accessible_case_ids": request.accessible_case_ids,
        }

        # Perform the search
        search_results = await embedding_service.search_similar(
            query=request.query,
            tenant_id=request.tenant_id,
            top_k=request.limit,
            filter_params=filter_params,
            user_context=user_context,
        )

        # Log the search for compliance
        logger.info(
            f"Document search: tenant={request.tenant_id}, "
            f"user={request.user_id}, query='{request.query}', "
            f"results={len(search_results)}"
        )

        # Format results
        results = [
            DocumentSearchResult(
                document_id=result["metadata"]["document_id"],
                score=result["score"],
                metadata=result["metadata"],
            )
            for result in search_results
        ]

        return DocumentSearchResponse(
            results=results, count=len(results), query=request.query
        )

    except Exception as e:
        logger.error(f"Error in document search: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching documents: {str(e)}",
        )
