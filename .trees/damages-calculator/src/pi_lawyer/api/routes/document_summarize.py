import os
from typing import Optional

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel

from pi_lawyer.models.auth import get_auth_data
from pi_lawyer.services.document_summarization import summarize_and_highlight

router = APIRouter()


class SummarizeRequest(BaseModel):
    document_id: Optional[str] = None
    text: Optional[str] = None


@router.post("/api/documents/summarize")
async def summarize_document(request: Request, body: SummarizeRequest):
    # Extract auth/tenant/role info
    auth = get_auth_data(request)
    user_id = auth.get("user_id")
    tenant_id = auth.get("tenant_id")
    role = auth.get("role")

    # Only allow staff roles
    staff_roles = {"partner", "attorney", "paralegal", "staff"}
    if role not in staff_roles:
        raise HTTPException(status_code=403, detail="Forbidden: insufficient role.")

    # Validate input
    if not body.text and not body.document_id:
        raise HTTPException(
            status_code=400, detail="Either 'text' or 'document_id' must be provided."
        )

    text = body.text
    # If document_id is provided, fetch securely with tenant isolation
    if not text and body.document_id:
        try:
            from supabase import create_client

            supabase = create_client(
                os.getenv("SUPABASE_URL"),
                os.getenv("SUPABASE_SERVICE_KEY", os.getenv("SUPABASE_KEY")),
            )
            response = (
                supabase.table("documents")
                .select("text")
                .eq("id", body.document_id)
                .eq("tenant_id", tenant_id)
                .single()
                .execute()
            )
            if response.error or not response.data:
                raise HTTPException(
                    status_code=404, detail="Document not found for this tenant."
                )
            text = response.data["text"]
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Error fetching document: {str(e)}"
            )

    if not text:
        raise HTTPException(status_code=404, detail="Document text not found.")

    # Call the summarization service
    result = summarize_and_highlight(text)
    if "error" in result:
        raise HTTPException(
            status_code=500, detail=f"Summarization error: {result['error']}"
        )
    return result
