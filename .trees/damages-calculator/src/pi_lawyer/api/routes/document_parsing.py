"""
Document parsing API routes for retrieving AI-parsed document data.
"""

import logging
import os
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, HTTPException, Request, status

from ...models.auth import get_auth_data

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/documents/parsing",
    tags=["Document Parsing"],
    responses={
        404: {"description": "Resource not found"},
        403: {"description": "Not authorized to access this resource"},
    },
)


@router.get("/{document_id}", response_model=Dict[str, Any])
async def get_document_parsing_result(request: Request, document_id: str):
    """
    Get the AI-parsed structured data for a document (only available for complex processing path).

    This endpoint returns the structured data that was extracted from a document
    that was processed using the complex path with AI parsing.
    """
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # Get Supabase client
        from supabase import Client, create_client

        supabase: Client = create_client(
            os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
            os.getenv(
                "SUPABASE_SERVICE_KEY", os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", "")
            ),
        )

        # Check if document exists and belongs to tenant
        response = (
            supabase.table("tenants.documents")
            .select("*")
            .eq("id", document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        document = response.data[0]

        # Check if document has been processed with complex path
        if document.get("metadata", {}).get("processing_path") != "complex":
            return {
                "status": "error",
                "message": "This document was not processed with the complex path",
                "document_id": document_id,
                "processing_path": document.get("metadata", {}).get(
                    "processing_path", "simple"
                ),
            }

        # Check parsing status
        parsing_status = document.get("ai_parsing_status")
        parsing_result = document.get("ai_parsing_result")
        parsing_error = document.get("ai_parsing_error")

        if parsing_status == "pending" or parsing_status == "processing":
            return {
                "status": "processing",
                "message": "Document parsing is still in progress",
                "document_id": document_id,
                "parsing_status": parsing_status,
            }

        if parsing_status == "error":
            return {
                "status": "error",
                "message": "Document parsing failed",
                "document_id": document_id,
                "parsing_status": parsing_status,
                "error": parsing_error,
            }

        if not parsing_result:
            return {
                "status": "error",
                "message": "No parsing results available for this document",
                "document_id": document_id,
                "parsing_status": parsing_status,
            }

        # Return the parsing result
        return {
            "status": "success",
            "message": "Document parsing results retrieved successfully",
            "document_id": document_id,
            "document_type": document.get("metadata", {}).get(
                "document_type", "unknown"
            ),
            "parsing_status": parsing_status,
            "parsed_at": document.get("parsed_at"),
            "parsed_data": parsing_result,
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving document parsing result: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve document parsing result: {str(e)}",
        )


@router.get("/reparse/{document_id}", response_model=Dict[str, Any])
async def reparse_document(
    request: Request, document_id: str, custom_schema: Optional[Dict[str, Any]] = None
):
    """
    Trigger a re-parsing of a document using the AI parser.

    This will re-run the AI parsing process on an existing document,
    which must already be using the complex processing path.
    """
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # Get Supabase client
        from supabase import Client, create_client

        supabase: Client = create_client(
            os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
            os.getenv(
                "SUPABASE_SERVICE_KEY", os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", "")
            ),
        )

        # Check if document exists and belongs to tenant
        response = (
            supabase.table("tenants.documents")
            .select("*")
            .eq("id", document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        document = response.data[0]

        # Check if document has been processed with complex path
        metadata = document.get("metadata", {})
        if metadata.get("processing_path") != "complex":
            # Update metadata to use complex path
            metadata["processing_path"] = "complex"
            if custom_schema:
                metadata["custom_schema"] = custom_schema

            # Update document metadata
            supabase.table("tenants.documents").update(
                {
                    "metadata": metadata,
                    "ai_parsing_status": "pending",
                    "ai_parsing_result": None,
                    "ai_parsing_error": None,
                }
            ).eq("id", document_id).execute()

            logger.info(
                f"Document {document_id} updated to use complex processing path"
            )
        else:
            # Just reset parsing status
            supabase.table("tenants.documents").update(
                {
                    "ai_parsing_status": "pending",
                    "ai_parsing_result": None,
                    "ai_parsing_error": None,
                }
            ).eq("id", document_id).execute()

        # Queue document for reprocessing
        import tempfile

        from ...services.document_processing_queue import get_document_queue

        # Get document from GCS
        from ...services.storage_client import StorageClient

        storage_client = StorageClient()
        file_content = storage_client.download_file(document["upload_path"])

        if not file_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document file not found in storage",
            )

        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Queue for reprocessing
        queue = get_document_queue()
        job_id = queue.enqueue_document(
            document_id=document_id,
            file_path=temp_file_path,
            file_name=document["file_name"],
            file_type=document["file_type"],
            tenant_id=tenant_id,
            user_id=user_id,
            priority=1,
            metadata=metadata,
        )

        # Update document status
        supabase.table("tenants.documents").update(
            {
                "processing_job_id": job_id,
                "processing_status": "reprocessing",
                "last_reprocessed_at": datetime.now().isoformat(),
            }
        ).eq("id", document_id).execute()

        return {
            "status": "success",
            "message": "Document queued for reparsing",
            "document_id": document_id,
            "job_id": job_id,
            "processing_path": "complex",
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error reprocessing document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reprocess document: {str(e)}",
        )
