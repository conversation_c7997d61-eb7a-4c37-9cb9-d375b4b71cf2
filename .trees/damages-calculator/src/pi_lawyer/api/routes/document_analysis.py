import logging
import os
import shutil
import tempfile
from typing import Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from ...services.document_analysis_service import DocumentAnalysisService

logger = logging.getLogger(__name__)
router = APIRouter()

# Create a singleton instance of the document analysis service
document_analysis_service = DocumentAnalysisService()


def get_document_analysis_service():
    return document_analysis_service


@router.post("/analyze")
async def analyze_document(
    file: UploadFile = File(...),
    document_type: str = Form("general"),
    analysis_type: str = Form("text"),  # Options: text, tasks, medical
    case_context: Optional[str] = Form(None),
    service: DocumentAnalysisService = Depends(get_document_analysis_service),
):
    """
    Analyze a document using Gemini 2.0 Flash Thinking.

    Parameters:
    - file: The PDF file to analyze
    - document_type: Type of document (general, contract, court_filing, medical_record)
    - analysis_type: Type of analysis to perform (text, tasks, medical)
    - case_context: Optional context about the case for better analysis

    Returns:
    - Analysis results based on the document and analysis type
    """
    # Save the uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
        try:
            shutil.copyfileobj(file.file, temp_file)
            temp_path = temp_file.name

            # Choose analysis method based on analysis_type
            if analysis_type == "medical":
                result = service.analyze_medical_form(temp_path)
            elif analysis_type == "tasks":
                result = service.extract_tasks_from_document(temp_path, case_context)
            else:  # text
                result = service.analyze_legal_document(
                    temp_path, document_type, case_context
                )

            # Check for errors in the result
            if isinstance(result, dict) and "error" in result:
                raise HTTPException(
                    status_code=500, detail=f"Analysis failed: {result['error']}"
                )

            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error analyzing document: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Document analysis failed: {str(e)}"
            )
        finally:
            # Clean up the temporary file
            os.unlink(temp_path)
