"""
API routes for async job management.

This module provides endpoints for submitting and monitoring async jobs
processed by Celery workers.
"""

import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import UUID4, BaseModel

from jobs.helpers import (
    check_job_status,
    submit_document_processing_job,
    submit_embedding_job,
)

from ...auth.jwt_auth import UserContext, verify_jwt

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/jobs",
    tags=["jobs"],
    responses={404: {"description": "Not found"}},
)


# Request models
class EmbeddingJobRequest(BaseModel):
    """Request model for submitting an embedding job."""
    document_id: UUID4
    case_id: Optional[UUID4] = None
    client_id: Optional[UUID4] = None
    priority: int = 5
    metadata: Optional[Dict[str, Any]] = None


class DocumentProcessingRequest(BaseModel):
    """Request model for document processing job."""
    document_id: UUID4
    file_path: str
    metadata: Optional[Dict[str, Any]] = None


# Response models
class JobResponse(BaseModel):
    """Response model for job submission."""
    job_id: str
    document_id: UUID4
    status: str
    message: str


class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job_id: str
    status: str
    ready: bool
    successful: Optional[bool] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/embeddings", response_model=JobResponse)
async def submit_embedding_job_endpoint(
    request: EmbeddingJobRequest,
    auth_user: UserContext = Depends(verify_jwt),
) -> Dict[str, Any]:
    """
    Submit a document for embedding generation.

    This endpoint queues a document for embedding generation using Celery.
    The job will be processed asynchronously by a worker.
    """
    try:
        # Submit job to Celery
        job_id = submit_embedding_job(
            document_id=request.document_id,
            tenant_id=auth_user.tenantId,
            user_id=auth_user.id,
            case_id=request.case_id,
            client_id=request.client_id,
            metadata=request.metadata,
            priority=request.priority,
        )

        # Return response
        return {
            "job_id": job_id,
            "document_id": request.document_id,
            "status": "queued",
            "message": "Document queued for embedding",
        }

    except Exception as e:
        logger.error(f"Error submitting embedding job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit embedding job: {str(e)}",
        )


@router.post("/documents/process", response_model=JobResponse)
async def submit_document_processing_endpoint(
    request: DocumentProcessingRequest,
    auth_user: UserContext = Depends(verify_jwt),
) -> Dict[str, Any]:
    """
    Submit a document for processing.

    This endpoint queues a document for processing using Celery.
    The job will be processed asynchronously by a worker.
    """
    try:
        # Submit job to Celery
        job_id = submit_document_processing_job(
            document_id=request.document_id,
            tenant_id=auth_user.tenantId,
            user_id=auth_user.id,
            file_path=request.file_path,
            metadata=request.metadata,
        )

        # Return response
        return {
            "job_id": job_id,
            "document_id": request.document_id,
            "status": "queued",
            "message": "Document queued for processing",
        }

    except Exception as e:
        logger.error(f"Error submitting document processing job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit document processing job: {str(e)}",
        )


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    auth_user: UserContext = Depends(verify_jwt),
) -> Dict[str, Any]:
    """
    Get the status of a job.

    This endpoint checks the status of a Celery task and returns
    information about its progress and result.
    """
    try:
        # Check job status
        status_info = check_job_status(job_id)

        # Return response
        return status_info

    except Exception as e:
        logger.error(f"Error checking job status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check job status: {str(e)}",
        )
