"""
Main FastAPI application entry point.

This module initializes the FastAPI application and registers all routers.
"""

# Ensure .env is loaded before importing any route or service modules
from dotenv import load_dotenv

# Load .env and override any existing vars to pick up updated values
load_dotenv(override=True)

import logging
import os

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .copilotkit_route import router as copilotkit_router

# Import all route modules
from .document_route import register_document_routes
from .intake_route import router as intake_router
from .research_route import router as research_router
from .routes.document_parsing import router as document_parsing_router
from .routes.document_upload import register_document_upload_routes
from .routes.jobs import router as jobs_router
from .security.config import router as security_config_router
from .task_route import router as task_router

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Initialization function to create the FastAPI app
def create_app():
    # Create FastAPI app
    app = FastAPI(
        title="PI Lawyer AI API",
        description="API for Personal Injury Lawyer AI Assistant",
        version="1.0.0",
    )

    # Configure CORS
    origins = os.getenv("CORS_ORIGINS", "*").split(",")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Register routers
    register_document_routes(app)
    register_document_upload_routes(app)
    app.include_router(document_parsing_router)
    app.include_router(research_router)
    app.include_router(task_router)
    app.include_router(intake_router)
    app.include_router(copilotkit_router)
    app.include_router(jobs_router)
    app.include_router(security_config_router)

    # Add startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        logger.info("Starting PI Lawyer API")

        # Initialize any services that need to be started
        from ..services.document_processing_queue import get_document_queue

        queue = get_document_queue()
        queue.start()

        logger.info("Document processing queue started")

    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Shutting down PI Lawyer API")

        # Shutdown any services that need to be stopped
        from ..services.document_processing_queue import get_document_queue

        queue = get_document_queue()
        queue.stop()

        logger.info("Document processing queue stopped")

    return app


# Create the app instance to be used by ASGI servers (like Uvicorn)
app = create_app()

# For direct execution (development)
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
