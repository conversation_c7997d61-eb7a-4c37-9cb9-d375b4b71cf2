"""
Temporary file containing updated update_document_variables function
to be manually copied into document_route.py
"""


@router.patch("/{document_id}/variables", response_model=DocumentResponse)
async def update_document_variables(
    request: Request, document_id: UUID4, variable_update: DocumentVariableUpdate
):
    """
    Update just the variables for a document.
    This is a more efficient endpoint when only variables need to be changed.
    """
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        existing_document = doc_response.data[0]

        # Check if the user has edit permission
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", user_id)
            .eq("can_edit", True)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to edit this document",
            )

        # Merge existing variables with new ones
        current_variables = existing_document.get("variables_used") or {}
        updated_variables = {**current_variables, **variable_update.variables}

        # Update only the variables and timestamp
        update_data = {
            "variables_used": updated_variables,
            "updated_by": user_id,
            "updated_at": "now()",
        }

        # Update the document
        response = (
            supabase.table("tenants.authored_documents")
            .update(update_data)
            .eq("id", str(document_id))
            .execute()
        )
        updated_document = response.data[0]

        # Check if the document should be embedded based on variable updates
        should_embed = False
        embed_reason = None

        # Check if the document is in a final state that should trigger embedding when variables change
        doc_status = existing_document.get("status")
        if doc_status in ["final", "approved", "published"]:
            should_embed = True
            embed_reason = f"Variables updated in '{doc_status}' document"

        # Check if embedding was explicitly requested in variables
        if variable_update.variables.get("should_embed", False):
            should_embed = True
            embed_reason = "Explicitly requested in variables update"

        # Queue for embedding if needed
        if should_embed:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=str(document_id),
                tenant_id=tenant_id,
                user_id=user_id,
                document_data={
                    "title": updated_document.get("title"),
                    "case_id": updated_document.get("case_id"),
                    "client_id": updated_document.get("client_id"),
                    "status": updated_document.get("status", "draft"),
                    "metadata": updated_document.get("metadata"),
                    "variables_used": updated_variables,
                },
                trigger_reason=embed_reason,
                source="api_variable_update",
                supabase_client=supabase,
            )

            if embedding_result.get("status") == "error":
                # Log error but don't fail the request
                logger.error(
                    f"Error queueing document for embedding: {embedding_result.get('error')}"
                )

        return updated_document
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating document variables: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update document variables: {str(e)}",
        )
