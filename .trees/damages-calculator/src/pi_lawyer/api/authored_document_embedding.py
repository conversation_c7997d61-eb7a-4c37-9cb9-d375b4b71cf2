"""
API route for handling authored document embedding.

This module provides endpoints for triggering and managing embeddings for authored documents,
allowing them to be searchable alongside uploaded documents.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException
from pydantic import UUID4, BaseModel, Field

from pi_lawyer.auth.auth_helpers import AuthUser, requireAuth
from pi_lawyer.auth.middleware import withAuth
from pi_lawyer.services.authored_document_embedding_service import (
    get_authored_document_embedding_service,
)
from pi_lawyer.services.document_processing_queue import get_authored_document_queue

# Configure logging
logger = logging.getLogger(__name__)

# Define router
router = APIRouter(prefix="/api/authored-documents", tags=["Authored Documents"])


# Request/Response models
class EmbeddingRequestModel(BaseModel):
    """Request model for initiating embedding of an authored document."""

    document_id: UUID4 = Field(..., description="ID of the authored document to embed")
    case_id: Optional[UUID4] = Field(None, description="Associated case ID")
    client_id: Optional[UUID4] = Field(None, description="Associated client ID")
    priority: int = Field(
        1, description="Processing priority (lower = higher priority)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional document metadata"
    )


class EmbeddingResponseModel(BaseModel):
    """Response model for embedding requests."""

    job_id: str = Field(..., description="ID of the embedding job")
    document_id: UUID4 = Field(..., description="ID of the authored document")
    status: str = Field(..., description="Initial status of the embedding job")
    message: str = Field(..., description="Status message")
    queued_at: datetime = Field(..., description="When the job was queued")


class EmbeddingStatusResponse(BaseModel):
    """Response model for embedding status checks."""

    document_id: UUID4 = Field(..., description="ID of the authored document")
    embedding_status: str = Field(..., description="Current embedding status")
    last_embedded_at: Optional[datetime] = Field(
        None, description="When the document was last embedded"
    )
    chunk_count: Optional[int] = Field(None, description="Number of document chunks")
    message: Optional[str] = Field(None, description="Status message")


@router.post("/embed", response_model=EmbeddingResponseModel)
@withAuth(["partner", "attorney", "paralegal", "staff"])
async def embed_authored_document(
    request: EmbeddingRequestModel = Body(...),
    auth_user: AuthUser = Depends(requireAuth),
):
    """
    Queue an authored document for embedding.

    This endpoint initiates the embedding process for an authored document,
    making it searchable alongside uploaded documents.
    """
    try:
        # Get the queue service
        queue_service = get_authored_document_queue()

        # Enqueue the document for embedding
        job_id = queue_service.enqueue_authored_document(
            authored_document_id=str(request.document_id),
            tenant_id=auth_user.tenantId,
            user_id=auth_user.id,
            case_id=str(request.case_id) if request.case_id else None,
            client_id=str(request.client_id) if request.client_id else None,
            priority=request.priority,
            metadata=request.metadata or {},
        )

        # Return response
        return {
            "job_id": job_id,
            "document_id": request.document_id,
            "status": "queued",
            "message": "Document queued for embedding",
            "queued_at": datetime.now(),
        }

    except Exception as e:
        logger.error(
            f"Error queueing authored document for embedding: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to queue document for embedding: {str(e)}"
        )


@router.get("/{document_id}/embedding-status", response_model=EmbeddingStatusResponse)
@withAuth(["partner", "attorney", "paralegal", "staff"])
async def get_embedding_status(
    document_id: UUID4, auth_user: AuthUser = Depends(requireAuth)
):
    """
    Get the embedding status for an authored document.

    This endpoint checks the current embedding status for an authored document,
    providing information about when it was last embedded and the number of chunks.
    """
    try:
        # Import Supabase client
        import os

        from supabase import create_client

        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        supabase = create_client(supabase_url, supabase_key)

        # Query document with tenant isolation
        response = (
            supabase.table("authored_documents")
            .select("id, embedding_status, last_embedded_at, metadata")
            .eq("id", str(document_id))
            .eq("tenant_id", auth_user.tenantId)
            .execute()
        )

        if response.error:
            logger.error(f"Supabase error: {response.error.message}")
            raise HTTPException(
                status_code=500, detail="Error retrieving document status"
            )

        if not response.data or len(response.data) == 0:
            raise HTTPException(status_code=404, detail="Document not found")

        document = response.data[0]

        # Get chunk count
        chunk_count = None
        message = None

        if document.get("embedding_status") in ["completed", "processing"]:
            # Query chunks to get count
            chunk_response = (
                supabase.table("document_chunks")
                .select("id", count="exact")
                .eq("authored_document_id", str(document_id))
                .eq("tenant_id", auth_user.tenantId)
                .execute()
            )

            if not chunk_response.error:
                chunk_count = (
                    chunk_response.count if hasattr(chunk_response, "count") else 0
                )

        # Extract message from metadata if available
        metadata = document.get("metadata", {})
        if metadata and isinstance(metadata, dict) and "embedding_info" in metadata:
            message = metadata.get("embedding_info", {}).get("last_message")

        return {
            "document_id": document_id,
            "embedding_status": document.get("embedding_status", "pending"),
            "last_embedded_at": document.get("last_embedded_at"),
            "chunk_count": chunk_count,
            "message": message,
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting embedding status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get embedding status: {str(e)}"
        )


@router.post("/{document_id}/reprocess-embedding")
@withAuth(["partner", "attorney", "paralegal", "staff"])
async def reprocess_embedding(
    document_id: UUID4, auth_user: AuthUser = Depends(requireAuth)
):
    """
    Reprocess embeddings for an authored document.

    This endpoint deletes existing embeddings and triggers re-embedding of the document,
    useful after content updates.
    """
    try:
        # Get the embedding service
        embedding_service = get_authored_document_embedding_service()

        # Process immediately (synchronously)
        result = await embedding_service.reprocess_authored_document(
            authored_document_id=str(document_id), tenant_id=auth_user.tenantId
        )

        return {
            "document_id": document_id,
            "status": "reprocessing",
            "message": "Document embeddings deleted and reprocessing started",
            "details": result,
        }

    except Exception as e:
        logger.error(f"Error reprocessing document embeddings: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to reprocess document embeddings: {str(e)}"
        )


# Batch embedding endpoint
class BatchEmbeddingRequest(BaseModel):
    """Request model for batch embedding of authored documents."""

    document_ids: List[UUID4] = Field(
        ..., description="IDs of authored documents to embed"
    )
    case_id: Optional[UUID4] = Field(None, description="Associated case ID")
    client_id: Optional[UUID4] = Field(None, description="Associated client ID")
    priority: int = Field(
        1, description="Processing priority (lower = higher priority)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional document metadata"
    )


@router.post("/batch-embed")
@withAuth(["partner", "attorney", "paralegal", "staff"])
async def batch_embed_authored_documents(
    request: BatchEmbeddingRequest = Body(...),
    auth_user: AuthUser = Depends(requireAuth),
):
    """
    Queue multiple authored documents for embedding in batch.

    This endpoint allows efficient processing of multiple documents at once.
    """
    try:
        # Get the queue service
        queue_service = get_authored_document_queue()

        # Process each document
        results = []
        for doc_id in request.document_ids:
            job_id = queue_service.enqueue_authored_document(
                authored_document_id=str(doc_id),
                tenant_id=auth_user.tenantId,
                user_id=auth_user.id,
                case_id=str(request.case_id) if request.case_id else None,
                client_id=str(request.client_id) if request.client_id else None,
                priority=request.priority,
                metadata=request.metadata or {},
            )

            results.append(
                {"document_id": doc_id, "job_id": job_id, "status": "queued"}
            )

        return {
            "message": f"Queued {len(results)} documents for embedding",
            "results": results,
            "queued_at": datetime.now(),
        }

    except Exception as e:
        logger.error(f"Error in batch embedding: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process batch embedding request: {str(e)}",
        )
