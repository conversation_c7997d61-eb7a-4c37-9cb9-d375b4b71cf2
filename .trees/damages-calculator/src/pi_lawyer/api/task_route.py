"""Task management routes including embedding handling."""
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, HTTPException, Request
from pydantic import BaseModel, Field

from ..services.task_embedding_service import task_embedding_service

# Set up logging
logger = logging.getLogger(__name__)

# Create API router
router = APIRouter(tags=["tasks"])


# Models for request/response
class TaskEmbeddingRequest(BaseModel):
    """Request model for task embedding generation."""

    task_id: str = Field(..., description="ID of the task")
    tenant_id: str = Field(..., description="Tenant ID for multi-tenant isolation")
    force_refresh: bool = Field(
        False, description="Force refresh the embedding even if it exists"
    )


class TaskSearchRequest(BaseModel):
    """Request model for semantic task search."""

    query: str = Field(..., description="Search query text")
    tenant_id: str = Field(..., description="Tenant ID for multi-tenant isolation")
    user_id: str = Field(..., description="User ID for access control")
    user_role: str = Field("user", description="User role for access control")
    accessible_case_ids: Optional[List[str]] = Field(
        None, description="List of case IDs the user has access to"
    )
    top_k: int = Field(5, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(
        None, description="Additional filters to apply"
    )


class TaskEmbeddingResponse(BaseModel):
    """Response model for task embedding operations."""

    status: str = Field(..., description="Operation status (success/error)")
    task_id: str = Field(..., description="ID of the task")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    timestamp: str = Field(..., description="Timestamp of the operation")


@router.post("/api/tasks/embedding", response_model=TaskEmbeddingResponse)
async def create_task_embedding(request: Request):
    """Generate and store an embedding for a task."""
    try:
        # Parse request body
        body = await request.json()
        logger.info(f"Received task embedding request for task {body.get('task_id')}")

        # Extract task data from request
        task_id = body.get("task_id")
        tenant_id = body.get("tenant_id")

        if not task_id or not tenant_id:
            raise HTTPException(
                status_code=400,
                detail="Missing required fields: task_id and tenant_id are required",
            )

        # Get task data from database
        # In a real implementation, you would fetch this from Supabase or your DB
        # This is a placeholder - you'll need to implement the actual DB fetch
        task_data = await _get_task_from_database(task_id, tenant_id)

        if not task_data:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Generate and store embedding
        result = await task_embedding_service.generate_task_embedding(
            task_data=task_data, tenant_id=tenant_id
        )

        # Return response
        return {
            "status": result.get("status", "error"),
            "task_id": task_id,
            "error": result.get("error"),
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException as he:
        # Rethrow HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error processing task embedding request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/api/tasks/search", response_model=List[Dict[str, Any]])
async def search_similar_tasks(request: TaskSearchRequest = Body(...)):
    """Search for tasks based on semantic similarity."""
    try:
        logger.info(f"Received task search request: {request.query}")

        # Create user context for RBAC filtering
        user_context = {
            "tenant_id": request.tenant_id,
            "user_id": request.user_id,
            "role": request.user_role,
            "accessible_case_ids": request.accessible_case_ids or [],
        }

        # Perform search with RBAC filtering
        results = await task_embedding_service.search_similar_tasks(
            query=request.query,
            tenant_id=request.tenant_id,
            user_context=user_context,
            top_k=request.top_k,
            filters=request.filters,
        )

        return results

    except Exception as e:
        logger.error(f"Error processing task search request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(
    "/api/tasks/embedding/{tenant_id}/{task_id}", response_model=TaskEmbeddingResponse
)
async def delete_task_embedding(tenant_id: str, task_id: str):
    """Delete a task embedding from the index."""
    try:
        logger.info(f"Received task embedding deletion request for task {task_id}")

        # Delete embedding
        result = await task_embedding_service.delete_task_embedding(
            task_id=task_id, tenant_id=tenant_id
        )

        # Return response
        return {
            "status": result.get("status", "error"),
            "task_id": task_id,
            "error": result.get("error"),
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error processing task embedding deletion request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def _get_task_from_database(task_id: str, tenant_id: str) -> Dict[str, Any]:
    """Placeholder for fetching task data from database.

    In a real implementation, this would fetch the task from your database (e.g., Supabase).

    Args:
        task_id: ID of the task
        tenant_id: Tenant ID for multi-tenant isolation

    Returns:
        Task data dictionary
    """
    # This is a placeholder - you'll need to implement the actual DB fetch
    # For example, using Supabase client to fetch from 'tasks' table

    # from ..db.supabase_client import supabase_client
    # response = await supabase_client.from_("tasks").select("*").eq("id", task_id).eq("tenant_id", tenant_id).execute()
    # if not response.data or len(response.data) == 0:
    #     return None
    # return response.data[0]

    # For now, return dummy data for testing
    return {
        "id": task_id,
        "title": "Sample Task",
        "description": "This is a sample task for testing the embedding service",
        "status": "open",
        "priority": "medium",
        "created_by": "user123",
        "assigned_to": "user456",
        "related_case": "case789",
        "tenant_id": tenant_id,
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat(),
        "due_date": (
            datetime.utcnow().replace(day=datetime.utcnow().day + 7)
        ).isoformat(),
    }


"""
Task API Routes for Pi Lawyer

This module provides API routes for task management with admin-level access.
"""

import os
from typing import Any, Dict, List, Optional

import httpx
from fastapi import APIRouter, Request
from pydantic import BaseModel

admin_router = APIRouter()

# Get Supabase credentials
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_KEY")  # Service key for admin access


# Define models
class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    due_date: Optional[str] = None
    status: str = "todo"
    assigned_to: Optional[str] = None
    related_case: Optional[str] = None
    tenant_id: str
    created_by: str


@admin_router.post("/api/admin/tasks")
async def create_task_admin(task: TaskCreate):
    """
    Create a task with admin privileges, bypassing RLS.
    This endpoint should be secured and only accessible to authenticated users.
    """
    try:
        # Current timestamp for created_at and updated_at
        timestamp = datetime.now().isoformat()

        # Prepare data for insertion
        task_data = {**task.dict(), "created_at": timestamp, "updated_at": timestamp}

        # Make direct admin request to Supabase
        async with httpx.AsyncClient() as client:
            headers = {
                "apikey": SUPABASE_SERVICE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                "Content-Type": "application/json",
                "Prefer": "return=representation",
            }

            url = f"{SUPABASE_URL}/rest/v1/tasks"
            response = await client.post(url, json=[task_data], headers=headers)

            if response.status_code >= 400:
                print(f"Error creating task: {response.text}")
                raise HTTPException(
                    status_code=response.status_code, detail=response.text
                )

            return response.json()

    except Exception as e:
        print(f"Exception in create_task_admin: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
