"""
Security Configuration API Endpoints

Provides REST API endpoints for security configuration validation and monitoring.
These endpoints are used by the superadmin portal for real-time security health monitoring.
"""

import logging
import os
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status, Header
from fastapi.responses import JSONResponse
import jwt
from jwt.exceptions import PyJWTError

from ...config.security_validator import SecurityConfigValidator
from ...security.startup_validation import SecurityValidationError

logger = logging.getLogger(__name__)

# Create the router for security configuration endpoints
router = APIRouter(prefix="/api/security/config", tags=["security-config"])


def _get_jwt_secret() -> str:
    """
    Get JWT secret from environment variables with validation.

    Returns:
        str: JWT secret for token verification

    Raises:
        HTTPException: If JWT secret is not configured or too short
    """
    jwt_secret = os.getenv("SUPABASE_JWT_SECRET")
    if not jwt_secret:
        logger.error("SUPABASE_JWT_SECRET not configured")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="JWT secret not configured"
        )

    # Validate secret length for security
    if len(jwt_secret) < 32:
        logger.error("SUPABASE_JWT_SECRET must be at least 32 characters long for security")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="JWT secret configuration error"
        )

    return jwt_secret


def _verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    Verify JWT token and return payload.

    Args:
        token: JWT token to verify

    Returns:
        Dict containing token payload

    Raises:
        HTTPException: If token verification fails
    """
    try:
        jwt_secret = _get_jwt_secret()

        # Decode and verify the token
        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=["HS256"],
            options={"verify_signature": True},
        )

        return payload

    except PyJWTError as e:
        logger.warning(f"JWT verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    except Exception as e:
        logger.error(f"Unexpected error during JWT verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token verification error"
        )


async def verify_superadmin_access(
    authorization: str = Header(..., alias="Authorization")
) -> Dict[str, Any]:
    """
    Verify superadmin access for security configuration endpoints.

    This dependency validates that the user has super admin privileges.

    Args:
        authorization: Authorization header with Bearer token

    Returns:
        Dict: Super admin user context

    Raises:
        HTTPException: If authentication fails or user is not super admin
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract and verify JWT token
    token = authorization.replace("Bearer ", "")
    payload = _verify_jwt_token(token)

    # Extract user information
    user_id = payload.get("sub")
    email = payload.get("email")
    role = payload.get("role")
    is_super_admin = payload.get("is_super_admin", False)

    # Validate required fields
    if not user_id or not email:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing user information"
        )

    # Get super admin emails from environment configuration
    super_admin_emails_env = os.getenv("SUPER_ADMIN_EMAILS", "")
    super_admin_emails = [email.strip() for email in super_admin_emails_env.split(",") if email.strip()]

    # SECURITY: Removed hardcoded fallback emails for production security
    # In production, SUPER_ADMIN_EMAILS environment variable must be configured
    if not super_admin_emails:
        if os.getenv("APP_ENV", "development") == "production":
            raise ValueError(
                "SUPER_ADMIN_EMAILS environment variable must be configured in "
                "production. Set SUPER_ADMIN_EMAILS='<EMAIL>,"
                "<EMAIL>' for security."
            )
        else:
            # Development fallback only - never used in production
            super_admin_emails = [
                "<EMAIL>",
                "<EMAIL>"
            ]

    # Check for super admin privileges
    # Super admin can be determined by:
    # 1. Explicit is_super_admin claim in JWT
    # 2. Role is 'super_admin'
    # 3. Email is in the super admin list
    is_authorized = (
        is_super_admin or
        role == "super_admin" or
        email in super_admin_emails
    )

    if not is_authorized:
        logger.warning(f"Super admin access denied for user {email}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin privileges required"
        )

    logger.info(f"Super admin access granted for user {email}")

    return {
        "user_id": user_id,
        "email": email,
        "role": "super_admin",
        "permissions": ["security:config", "admin:all"]
    }


@router.get("/audit")
async def get_security_config_audit(
    current_user: Dict[str, Any] = Depends(verify_superadmin_access)
) -> JSONResponse:
    """
    Get comprehensive security configuration audit results.
    
    This endpoint performs a full security configuration audit and returns
    detailed results including validation status, issues, and recommendations.
    
    Requires superadmin authentication.
    
    Returns:
        JSONResponse: Security configuration audit results
        
    Raises:
        HTTPException: If audit fails or user lacks permissions
    """
    try:
        logger.info(f"Security config audit requested by superadmin user: {current_user.get('user_id', 'unknown')}")
        
        # Perform comprehensive security configuration audit
        audit_result = SecurityConfigValidator.perform_security_config_audit()
        
        # Generate human-readable summary
        summary = SecurityConfigValidator.get_security_config_summary(audit_result)
        
        # Log audit completion
        logger.info(
            f"Security config audit completed - Status: {audit_result.overall_status}, "
            f"Critical: {audit_result.critical_issues}, High: {audit_result.high_issues}, "
            f"Failed: {audit_result.failed_checks}, Passed: {audit_result.passed_checks}"
        )
        
        # Return comprehensive audit data
        response_data = {
            "success": True,
            "audit": audit_result.dict(),
            "summary": summary,
            "timestamp": audit_result.timestamp,
            "requested_by": current_user.get('user_id'),
        }
        
        return JSONResponse(
            content=response_data,
            status_code=status.HTTP_200_OK
        )
        
    except SecurityValidationError as e:
        logger.error(f"Security validation error during audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Security validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during security config audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform security configuration audit"
        )


@router.post("/validate")
async def trigger_security_config_validation(
    current_user: Dict[str, Any] = Depends(verify_superadmin_access)
) -> JSONResponse:
    """
    Trigger a fresh security configuration validation.
    
    This endpoint performs a comprehensive security configuration validation
    and returns the results. It's useful for on-demand validation checks
    from the superadmin portal.
    
    Requires superadmin authentication.
    
    Returns:
        JSONResponse: Fresh security configuration validation results
        
    Raises:
        HTTPException: If validation fails or user lacks permissions
    """
    try:
        logger.info(f"Security config validation triggered by superadmin user: {current_user.get('user_id', 'unknown')}")
        
        # Perform fresh security configuration validation
        audit_result = SecurityConfigValidator.perform_security_config_audit()
        
        # Generate human-readable summary
        summary = SecurityConfigValidator.get_security_config_summary(audit_result)
        
        # Check for critical issues that would block production
        if audit_result.critical_issues > 0:
            logger.warning(
                f"Critical security configuration issues detected: {audit_result.critical_issues} issues found"
            )
        
        # Log validation completion
        logger.info(
            f"Security config validation completed - Status: {audit_result.overall_status}, "
            f"Critical: {audit_result.critical_issues}, High: {audit_result.high_issues}, "
            f"Failed: {audit_result.failed_checks}, Passed: {audit_result.passed_checks}"
        )
        
        # Return validation results with additional metadata
        response_data = {
            "success": True,
            "validation": audit_result.dict(),
            "summary": summary,
            "timestamp": audit_result.timestamp,
            "triggered_by": current_user.get('user_id'),
            "production_ready": audit_result.critical_issues == 0,
            "recommendations": _get_validation_recommendations(audit_result),
        }
        
        return JSONResponse(
            content=response_data,
            status_code=status.HTTP_200_OK
        )
        
    except SecurityValidationError as e:
        logger.error(f"Security validation error during triggered validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Security validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during security config validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform security configuration validation"
        )


@router.get("/health")
async def get_security_config_health(
    current_user: Dict[str, Any] = Depends(verify_superadmin_access)
) -> JSONResponse:
    """
    Get basic security configuration health status.
    
    This endpoint provides a lightweight health check for security configuration
    without performing a full audit. Useful for dashboard widgets and monitoring.
    
    Requires superadmin authentication.
    
    Returns:
        JSONResponse: Basic security configuration health status
        
    Raises:
        HTTPException: If health check fails or user lacks permissions
    """
    try:
        logger.debug(f"Security config health check requested by superadmin user: {current_user.get('user_id', 'unknown')}")
        
        # Perform lightweight security configuration audit
        audit_result = SecurityConfigValidator.perform_security_config_audit()
        
        # Return simplified health status
        response_data = {
            "success": True,
            "health": {
                "status": audit_result.overall_status,
                "critical_issues": audit_result.critical_issues,
                "high_issues": audit_result.high_issues,
                "total_failed": audit_result.failed_checks,
                "total_passed": audit_result.passed_checks,
                "production_ready": audit_result.critical_issues == 0,
            },
            "timestamp": audit_result.timestamp,
            "checked_by": current_user.get('user_id'),
        }
        
        return JSONResponse(
            content=response_data,
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Error during security config health check: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check security configuration health"
        )


def _get_validation_recommendations(audit_result) -> list[str]:
    """
    Generate actionable recommendations based on audit results.
    
    Args:
        audit_result: Security configuration audit results
        
    Returns:
        List of actionable recommendations
    """
    recommendations = []
    
    if audit_result.critical_issues > 0:
        recommendations.append(
            f"🚨 CRITICAL: {audit_result.critical_issues} critical security issues must be resolved before production deployment"
        )
    
    if audit_result.high_issues > 0:
        recommendations.append(
            f"⚠️ HIGH: {audit_result.high_issues} high-priority security issues should be addressed soon"
        )
    
    if audit_result.failed_checks > 0:
        recommendations.append(
            f"📋 REVIEW: {audit_result.failed_checks} total configuration checks failed - review security settings"
        )
    
    if audit_result.overall_status == 'healthy':
        recommendations.append("✅ GOOD: All security configuration checks passed - system is production-ready")
    elif audit_result.overall_status == 'warning':
        recommendations.append("⚠️ WARNING: Some security configuration issues detected - review recommended")
    
    # Add category-specific recommendations
    for category, summary in audit_result.summary.items():
        if summary.failed > 0:
            recommendations.append(f"🔧 {category}: {summary.failed} configuration issues need attention")
    
    return recommendations
