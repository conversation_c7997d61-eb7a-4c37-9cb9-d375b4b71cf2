import logging
from datetime import datetime

from fastapi import APIRouter, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware

from ..agents.research.graph import graph
from ..agents.research.state import ResearchState

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI()

# Add CORS middleware to the app, not the router
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create router for research routes
router = APIRouter()


@router.post("/api/research")
async def handle_research_request(request: Request):
    """
    Handle legal research agent requests through LangGraph
    """
    try:
        # Get request body
        body = await request.json()
        logger.info(
            f"Incoming research request received: {body.get('type', 'unknown')}"
        )

        # Get LangGraph instance - already imported at the top
        # graph = create_research_graph() - using pre-compiled graph

        # Process request through LangGraph
        if "state" in body:
            # Use provided state
            state_dict = body.get("state", {})

            # Convert to ResearchState with proper defaults
            state = ResearchState(
                messages=state_dict.get("messages", []),
                user_input=state_dict.get("user_input"),
                router=state_dict.get("router", {"type": "legal research"}),
                documents=state_dict.get("documents", []),
                cited_documents=state_dict.get("cited_documents", {}),
                research_summary=state_dict.get("research_summary"),
                last_response_type=state_dict.get("last_response_type"),
                error=state_dict.get("error"),
                progress_percentage=state_dict.get("progress_percentage", 0.0),
                show_progress_widget=state_dict.get("show_progress_widget", False),
            )
        else:
            # Create new state
            state = ResearchState(
                question="",
                user_context={
                    "user_id": "",
                    "tenant_id": "",
                    "role": "staff",
                    "assigned_case_ids": [],
                    "settings": {},
                },
            )

        result = await graph.ainvoke(state=state, config=body.get("config", {}))

        return result

    except Exception as e:
        logger.error(f"Error processing research request: {str(e)}")
        return {"error": str(e), "timestamp": datetime.utcnow().isoformat()}


# Include the router in the app
app.include_router(router)
