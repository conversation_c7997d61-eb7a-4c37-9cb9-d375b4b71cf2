"""
Health check and monitoring API endpoints.

Provides health check endpoints for system monitoring, including
component status, metrics, and debug information for operations.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, status

from ..services.health_service import get_health_service
from ..services.metrics_collector import get_metrics_collector
from ..utils.structured_logging import (
    get_correlation_id,
)

# Create router
router = APIRouter(prefix="/health", tags=["health"])

logger = logging.getLogger(__name__)


@router.get("/status")
async def get_health_status() -> Dict[str, Any]:
    """
    Get system health status.

    Returns overview of all system components and their health state.
    Used for monitoring and automated health checks.
    """
    correlation_id = get_correlation_id()
    logger.info(
        "Health check requested",
        extra={"correlation_id": correlation_id, "endpoint": "/health/status"},
    )

    health_service = get_health_service()
    status = health_service.get_system_status()

    # Log health check result
    log_level = "info" if status["status"] == "healthy" else "warning"
    getattr(logger, log_level)(
        f"Health check result: {status['status']}",
        extra={
            "correlation_id": correlation_id,
            "health_status": status["status"],
            "component_statuses": {
                name: info["status"] for name, info in status["components"].items()
            },
        },
    )

    return status


@router.get("/metrics")
async def get_metrics(format: str = "prometheus") -> str:
    """
    Get system metrics in Prometheus format.

    Args:
        format: Output format (currently only 'prometheus' is supported)

    Returns:
        Metrics in Prometheus text format
    """
    correlation_id = get_correlation_id()
    logger.debug(
        "Metrics requested", extra={"correlation_id": correlation_id, "format": format}
    )

    metrics = get_metrics_collector()

    if format == "prometheus":
        metrics_text = metrics.export_prometheus()
        return metrics_text
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported metrics format: {format}",
        )


@router.get("/queue")
async def get_queue_metrics() -> Dict[str, Any]:
    """
    Get document processing queue metrics.

    Returns metrics about queue depths, processing rates, and error rates.
    """
    from ..services.redis_queue_service import get_redis_queue_service

    correlation_id = get_correlation_id()
    logger.debug("Queue metrics requested", extra={"correlation_id": correlation_id})

    redis_queue = get_redis_queue_service()
    stats = redis_queue.get_queue_stats()

    # Get stalled jobs
    stalled_jobs = redis_queue.get_stalled_jobs_count(timeout_minutes=30)

    return {
        "queue_stats": stats,
        "stalled_jobs": stalled_jobs,
        "performance": {
            "processing_rate": redis_queue.calculate_processing_rate(),
            "failure_rate": redis_queue.calculate_failure_rate(),
        },
    }


@router.get("/test_error")
async def test_error() -> Dict[str, Any]:
    """
    Test endpoint to trigger an error for testing error handling.
    Only available in development mode.

    Raises a deliberate exception to test error handling and logging.
    """
    import os

    if os.getenv("APP_ENV", "development") != "development":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="This endpoint is only available in development mode",
        )

    correlation_id = get_correlation_id()
    logger.info("Test error endpoint called", extra={"correlation_id": correlation_id})

    # Trigger a deliberate error for testing
    try:
        # This will raise a ZeroDivisionError
        result = 1 / 0
        return {"result": result}
    except Exception as e:
        logger.error(
            f"Test error triggered: {str(e)}",
            exc_info=True,
            extra={"correlation_id": correlation_id},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Test error triggered: {str(e)}",
        )
