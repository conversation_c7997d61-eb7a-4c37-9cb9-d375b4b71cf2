"""
Integration tests for the CopilotKit endpoint key verification.

This module contains integration tests for the CopilotKit endpoint key verification
middleware and dependency function.
"""


import pytest
from fastapi.testclient import TestClient

from pi_lawyer.api.runtime import app as fastapi_app

# Create a test client
client = TestClient(fastapi_app)


@pytest.fixture
def mock_env_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable."""
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-secret-123456789")


@pytest.fixture
def mock_env_no_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable to be empty."""
    monkeypatch.delenv("CPK_ENDPOINT_SECRET", raising=False)


@pytest.fixture
def mock_env_debug_mode(monkeypatch):
    """Mock the DEBUG environment variable to enable debug mode."""
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-endpoint-secret-123456789")


def test_copilotkit_endpoint_valid_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is accessible with a valid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret-123456789"},
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code != 403  # Not forbidden


def test_copilotkit_endpoint_invalid_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is not accessible with an invalid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "invalid-secret"},
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 403
    assert "Invalid CopilotKit endpoint key" in response.json()["detail"]


def test_copilotkit_endpoint_missing_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is not accessible with a missing key."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 403
    assert "CopilotKit endpoint key required" in response.json()["detail"]


def test_copilotkit_endpoint_no_secret(mock_env_no_endpoint_secret):
    """Test that the CopilotKit endpoint is accessible when no secret is set."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code != 403  # Not forbidden


def test_copilotkit_endpoint_debug_mode(mock_env_debug_mode):
    """Test that the CopilotKit endpoint is accessible in debug mode with default secret."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code != 403  # Not forbidden


def test_copilotkit_health_endpoint():
    """Test that the CopilotKit health endpoint is accessible without authentication."""
    response = client.get("/copilotkit/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_copilotkit_options_endpoint():
    """Test that the CopilotKit options endpoint is accessible without authentication."""
    response = client.options("/copilotkit")
    assert response.status_code == 200


def test_non_copilotkit_endpoint():
    """Test that non-CopilotKit endpoints are not affected by the middleware."""
    response = client.get("/api/health")
    assert response.status_code != 403  # Not forbidden
