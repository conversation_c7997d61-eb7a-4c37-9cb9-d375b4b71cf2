"""
Tests for schema evolution in LangGraph state management.

This module tests the schema evolution functionality of the LangGraph state management system.
"""

from typing import Optional
from unittest.mock import patch

from langchain_core.messages import AIMessage

from src.pi_lawyer.state.langgraph_state import (
    STATE_VERSION,
    BaseAgentState,
    ResearchAgentState,
    StateManager,
    UserContext,
    create_state,
    create_typed_state,
)


class TestSchemaEvolution:
    """Tests for schema evolution."""
    
    def test_optional_field_compatibility(self):
        """Test that optional fields maintain compatibility."""
        # Create a state without providing a value for the optional question field
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        
        # Verify that the state was created successfully
        assert state.agent_type == "research"
        assert state.tenant_id == "tenant-123"
        assert state.user_id == "user-456"
        assert state.thread_id == "thread-789"
        assert state.question is None  # Optional field should be None
        
        # Convert to TypedDict for LangGraph
        typed_state = create_typed_state(state)
        
        # Verify that the TypedDict was created successfully
        assert typed_state["agent_type"] == "research"
        assert typed_state["tenant_id"] == "tenant-123"
        assert typed_state["user_id"] == "user-456"
        assert typed_state["thread_id"] == "thread-789"
        assert typed_state["question"] is None  # Optional field should be None
    
    def test_state_migration(self):
        """Test migrating a state from one version to another."""
        # Create a state with the current version
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        
        # Set an old version
        state.version = "0.9.0"
        
        # Migrate the state to the current version
        migrated_state = StateManager.migrate_state(state)
        
        # Verify that the state was migrated successfully
        assert migrated_state.version == STATE_VERSION
        assert migrated_state is not state  # Should be a clone
        
        # Verify that the state data was preserved
        assert migrated_state.agent_type == "research"
        assert migrated_state.tenant_id == "tenant-123"
        assert migrated_state.user_id == "user-456"
        assert migrated_state.thread_id == "thread-789"
    
    def test_reducer_with_optional_field(self):
        """Test that reducers work with optional fields."""
        # Create a state without providing a value for the optional question field
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        
        # Convert to TypedDict for LangGraph
        typed_state = create_typed_state(state)
        
        # Simulate a LangGraph node that returns a delta with the optional field
        delta = {
            "messages": [AIMessage(content="I'll research that for you.")],
            "question": "What is the statute of limitations for personal injury in Texas?"
        }
        
        # Apply the delta to the state dictionary
        from src.pi_lawyer.state.langgraph_state import add_messages
        
        updated_state_dict = typed_state.copy()
        updated_state_dict["messages"] = add_messages(typed_state["messages"], delta["messages"])
        updated_state_dict["question"] = delta["question"]
        
        # Verify that the state was updated successfully
        assert updated_state_dict["messages"][-1].content == "I'll research that for you."
        assert updated_state_dict["question"] == "What is the statute of limitations for personal injury in Texas?"
        
        # Convert back to a state object
        updated_state = StateManager.from_typed_dict(updated_state_dict)
        
        # Verify that the state object was created successfully
        assert updated_state.messages[-1].content == "I'll research that for you."
        assert updated_state.question == "What is the statute of limitations for personal injury in Texas?"
    
    def test_adding_new_field(self):
        """Test adding a new field to a state class."""
        # Create a custom state class with a new field
        class CustomResearchAgentState(ResearchAgentState):
            """Custom research agent state with a new field."""
            
            custom_field: Optional[str] = None
        
        # Create a state with the custom class
        state = CustomResearchAgentState(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            user_context=UserContext(
                user_id="user-456",
                tenant_id="tenant-123",
                role="staff",
                assigned_case_ids=[],
                settings={}
            ),
            custom_field="custom value"
        )
        
        # Verify that the state was created successfully
        assert state.agent_type == "research"
        assert state.tenant_id == "tenant-123"
        assert state.user_id == "user-456"
        assert state.thread_id == "thread-789"
        assert state.custom_field == "custom value"
        
        # Convert to dict
        state_dict = state.to_dict()
        
        # Verify that the dict includes the new field
        assert state_dict["custom_field"] == "custom value"
        
        # Convert to TypedDict for LangGraph
        typed_state = state.to_typed_dict()
        
        # Verify that the TypedDict includes the new field
        assert typed_state["custom_field"] == "custom value"
        
        # Create a standard ResearchAgentState from the dict
        standard_state = ResearchAgentState(**{k: v for k, v in state_dict.items() if k != "custom_field"})
        
        # Verify that the standard state was created successfully
        assert standard_state.agent_type == "research"
        assert standard_state.tenant_id == "tenant-123"
        assert standard_state.user_id == "user-456"
        assert standard_state.thread_id == "thread-789"
        assert not hasattr(standard_state, "custom_field")  # Should not have the custom field
    
    @patch("src.pi_lawyer.state.langgraph_state.STATE_VERSION", "2.0.0")
    def test_version_upgrade(self):
        """Test upgrading a state to a new version."""
        # Create a state with the old version
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        
        # Set the old version
        state.version = "1.0.0"
        
        # Define a migration function
        def migrate_1_0_0_to_2_0_0(state: BaseAgentState) -> BaseAgentState:
            """Migrate a state from version 1.0.0 to 2.0.0."""
            # Clone the state to avoid modifying the original
            migrated_state = state.clone()
            
            # Add a new field or transform existing ones
            if isinstance(migrated_state, ResearchAgentState):
                migrated_state.set_memory("migrated", True)
            
            # Set the new version
            migrated_state.version = "2.0.0"
            
            return migrated_state
        
        # Patch the migrate_state method to use our custom migration function
        with patch.object(StateManager, "migrate_state", side_effect=migrate_1_0_0_to_2_0_0):
            # Migrate the state
            migrated_state = StateManager.migrate_state(state)
            
            # Verify that the state was migrated successfully
            assert migrated_state.version == "2.0.0"
            assert migrated_state.get_memory("migrated") is True
            
            # Verify that the original state was not modified
            assert state.version == "1.0.0"
            assert state.get_memory("migrated", None) is None
