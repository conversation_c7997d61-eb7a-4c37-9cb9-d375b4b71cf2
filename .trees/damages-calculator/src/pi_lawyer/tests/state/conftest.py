"""
Test configuration for state tests

This module contains fixtures for state tests.
"""

import asyncio
from unittest.mock import MagicMock

import pytest


@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_langgraph_agents():
    """
    Mock the LangGraph agents.
    
    This fixture is used to mock the LangGraph agents for testing.
    """
    # Create mock objects for each agent
    mock_intake_graph = MagicMock()
    mock_document_graph = MagicMock()
    mock_research_graph = MagicMock()
    mock_event_graph = MagicMock()
    mock_deadline_graph = MagicMock()
    
    # Return the mocks
    return {
        "intake_graph": mock_intake_graph,
        "document_graph": mock_document_graph,
        "research_graph": mock_research_graph,
        "event_graph": mock_event_graph,
        "deadline_graph": mock_deadline_graph,
    }
