"""
Integration tests for LangGraph state persistence.

This module tests the integration between LangGraph and our state persistence layer.
"""

import time
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import StateGraph

from pi_lawyer.state.langgraph_state import (
    ResearchAgentState,
    StateManager,
    UserContext,
    create_typed_state,
    get_state_class_for_agent_type,
)
from pi_lawyer.state.persistence import (
    DatabaseCheckpointSaver,
    load_state,
    save_state,
    serialize_state,
    serialize_typed_state,
)


@pytest.mark.asyncio
class TestLangGraphPersistenceIntegration:
    """Integration tests for LangGraph state persistence."""

    @pytest.fixture
    async def mock_db_client(self):
        """Mock database client for testing."""
        mock_client = MagicMock()
        mock_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value = None
        mock_client.schema.return_value.table.return_value.insert.return_value.execute.return_value.data = [{"id": "record-123"}]
        
        with patch("pi_lawyer.state.persistence.get_db_client", return_value=mock_client):
            yield mock_client

    @pytest.fixture
    def research_state(self):
        """Create a research state for testing."""
        return ResearchAgentState(
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            agent_type="research",
            user_context=UserContext(
                id="user-456",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-123",
                permissions=["read", "write"]
            ),
            messages=[
                HumanMessage(content="What is the statute of limitations for personal injury in Texas?"),
                AIMessage(content="In Texas, the statute of limitations for personal injury claims is 2 years from the date of the injury.")
            ],
            memory={"last_query": "statute of limitations"},
            question="What is the statute of limitations for personal injury in Texas?",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            queries=["texas personal injury statute of limitations"]
        )

    async def test_langgraph_checkpoint_saver(self, mock_db_client, research_state):
        """Test the LangGraph checkpoint saver."""
        # Create a typed state
        typed_state = create_typed_state(research_state)
        
        # Create a checkpoint saver
        saver = DatabaseCheckpointSaver(tenant_id="tenant-123")
        
        # Mock the database response for get
        mock_db_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value = {
            "id": "record-123",
            "thread_id": "thread-789",
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "agent_type": "research",
            "state_data": serialize_typed_state(typed_state),
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-01T00:00:00+00:00"
        }
        
        # Test put
        await saver.put("thread-789", typed_state)
        
        # Verify put
        mock_db_client.schema.assert_called_with("security")
        mock_db_client.schema.return_value.table.assert_called_with("agent_states")
        mock_db_client.schema.return_value.table.return_value.select.assert_called()
        
        # Test get
        result = await saver.get("thread-789")
        
        # Verify get
        assert result is not None
        assert "messages" in result
        assert len(result["messages"]) == 2
        assert result["tenant_id"] == "tenant-123"
        assert result["user_id"] == "user-456"
        assert result["thread_id"] == "thread-789"
        assert result["agent_type"] == "research"
        
        # Test delete
        await saver.delete("thread-789")
        
        # Verify delete
        mock_db_client.schema.return_value.table.return_value.delete.assert_called()

    async def test_langgraph_integration(self, mock_db_client, research_state):
        """Test the integration with LangGraph."""
        # Create a StateGraph
        state_class = get_state_class_for_agent_type("research")
        graph = StateGraph(state_class)
        
        # Define a simple node
        async def echo_node(state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
            """Echo node that returns a partial state with a new message."""
            return {
                "messages": [AIMessage(content="Echo: " + state["messages"][-1].content)]
            }
        
        # Add the node to the graph
        graph.add_node("echo", echo_node)
        graph.set_entry_point("echo")
        graph.set_finish_point("echo")
        
        # Compile the graph
        compiled_graph = graph.compile()
        
        # Create a typed state
        typed_state = create_typed_state(research_state)
        
        # Mock the checkpoint saver
        with patch("pi_lawyer.state.persistence.DatabaseCheckpointSaver") as mock_saver_class:
            mock_saver = AsyncMock()
            mock_saver_class.return_value = mock_saver
            mock_saver.get.return_value = typed_state
            
            # Create a StateManager
            state_manager = StateManager(tenant_id="tenant-123")
            
            # Test loading state
            loaded_state = await state_manager.load_state("thread-789")
            assert loaded_state is not None
            assert loaded_state.tenant_id == "tenant-123"
            assert loaded_state.thread_id == "thread-789"
            
            # Test saving state
            await state_manager.save_state(research_state)
            mock_saver.put.assert_called()
            
            # Test running the graph with state persistence
            # This would be a more complex test in a real implementation
            pass

    async def test_performance(self, mock_db_client, research_state):
        """Test the performance of state persistence operations."""
        # Measure save performance
        start_time = time.time()
        for _ in range(10):
            await save_state(research_state)
        save_time = time.time() - start_time
        
        # Measure load performance
        mock_db_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value = {
            "id": "record-123",
            "thread_id": "thread-789",
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "agent_type": "research",
            "state_data": serialize_state(research_state),
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-01T00:00:00+00:00"
        }
        
        start_time = time.time()
        for _ in range(10):
            await load_state("thread-789", "tenant-123")
        load_time = time.time() - start_time
        
        # Assert performance meets requirements
        assert save_time < 1.0, f"Save performance too slow: {save_time:.3f}s for 10 operations"
        assert load_time < 1.0, f"Load performance too slow: {load_time:.3f}s for 10 operations"

    async def test_tenant_isolation(self, mock_db_client):
        """Test tenant isolation in state persistence."""
        # Create states for different tenants
        state1 = ResearchAgentState(
            tenant_id="tenant-1",
            user_id="user-1",
            thread_id="thread-1",
            agent_type="research",
            user_context=UserContext(
                id="user-1",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-1",
                permissions=["read", "write"]
            ),
            messages=[HumanMessage(content="Query for tenant 1")]
        )
        
        state2 = ResearchAgentState(
            tenant_id="tenant-2",
            user_id="user-2",
            thread_id="thread-1",  # Same thread ID, different tenant
            agent_type="research",
            user_context=UserContext(
                id="user-2",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-2",
                permissions=["read", "write"]
            ),
            messages=[HumanMessage(content="Query for tenant 2")]
        )
        
        # Save both states
        await save_state(state1)
        await save_state(state2)
        
        # Mock the database response for tenant 1
        mock_db_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.side_effect = [
            {
                "id": "record-1",
                "thread_id": "thread-1",
                "tenant_id": "tenant-1",
                "user_id": "user-1",
                "agent_type": "research",
                "state_data": serialize_state(state1),
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-01T00:00:00+00:00"
            },
            {
                "id": "record-2",
                "thread_id": "thread-1",
                "tenant_id": "tenant-2",
                "user_id": "user-2",
                "agent_type": "research",
                "state_data": serialize_state(state2),
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-01T00:00:00+00:00"
            }
        ]
        
        # Load states for each tenant
        loaded_state1 = await load_state("thread-1", "tenant-1")
        loaded_state2 = await load_state("thread-1", "tenant-2")
        
        # Verify tenant isolation
        assert loaded_state1 is not None
        assert loaded_state2 is not None
        assert loaded_state1.tenant_id == "tenant-1"
        assert loaded_state2.tenant_id == "tenant-2"
        assert loaded_state1.messages[0].content == "Query for tenant 1"
        assert loaded_state2.messages[0].content == "Query for tenant 2"
