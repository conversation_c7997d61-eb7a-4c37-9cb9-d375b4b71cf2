"""
Simple tests for the LangGraph state schema.

This module contains simple tests for the LangGraph state schema that don't depend on the conftest.py file.
"""


from langchain_core.messages import AIMessage, HumanMessage

from pi_lawyer.state.langgraph_state import (
    BaseAgentState,
    IntakeAgentState,
    ResearchAgentState,
    UserContext,
    create_state,
)


class TestBaseAgentState:
    """Unit tests for the BaseAgentState class."""

    def test_initialization(self):
        """Test initialization of BaseAgentState."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )
        
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )
        
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "intake"
        assert state.user_context == user_context
        assert isinstance(state.messages, list)
        assert len(state.messages) == 0
        assert isinstance(state.memory, dict)
        assert len(state.memory) == 0
        assert isinstance(state.created_at, str)
        assert isinstance(state.updated_at, str)

    def test_add_message(self):
        """Test adding a message."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney"
        )
        
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )
        
        message = HumanMessage(content="Hello")
        state.add_message(message)
        assert len(state.messages) == 1
        assert state.messages[0].content == "Hello"
        
        # Add another message
        message2 = AIMessage(content="Hi there")
        state.add_message(message2)
        assert len(state.messages) == 2
        assert state.messages[1].content == "Hi there"

    def test_get_last_message(self):
        """Test getting the last message."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney"
        )
        
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )
        
        # No messages
        assert state.get_last_message() is None
        
        # Add messages
        message1 = HumanMessage(content="Hello")
        message2 = AIMessage(content="Hi there")
        state.add_message(message1)
        state.add_message(message2)
        
        # Get last message
        last_message = state.get_last_message()
        assert last_message is not None
        assert last_message.content == "Hi there"

    def test_memory_operations(self):
        """Test memory operations."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney"
        )
        
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )
        
        # Set memory values
        state.set_memory("key1", "value1")
        state.set_memory("key2", {"nested": "value"})
        
        # Get memory values
        assert state.get_memory("key1") == "value1"
        assert state.get_memory("key2") == {"nested": "value"}
        assert state.get_memory("key3") is None
        assert state.get_memory("key3", "default") == "default"


class TestStateFactory:
    """Integration tests for the state factory function."""

    def test_create_intake_state(self):
        """Test creating an intake agent state."""
        state = create_state(
            agent_type="intake",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789"
        )
        assert isinstance(state, IntakeAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "intake"

    def test_create_research_state(self):
        """Test creating a research agent state."""
        state = create_state(
            agent_type="research",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )
        assert isinstance(state, ResearchAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "research"
        assert state.question == "What is the statute of limitations for personal injury in Texas?"
        assert state.jurisdiction == "texas"  # Default value

    def test_create_with_custom_user_context(self):
        """Test creating a state with a custom user context."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="partner",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )
        
        state = create_state(
            agent_type="intake",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            user_context=user_context
        )
        
        assert state.user_context == user_context
        assert state.user_context.role == "partner"
        assert "case-1" in state.user_context.assigned_case_ids
        assert state.user_context.settings["theme"] == "dark"
