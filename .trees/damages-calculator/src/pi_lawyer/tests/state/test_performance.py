"""
Performance tests for LangGraph state management.

This module tests the performance of state management operations,
particularly focusing on the conversion between Pydantic models and TypedDict states.
"""

import time
from statistics import mean, stdev

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src.pi_lawyer.state.langgraph_state import (
    ResearchAgentState,
    StateManager,
    create_state,
    create_typed_state,
)


def create_test_state(num_messages: int = 10, memory_size: int = 10) -> ResearchAgentState:
    """Create a test state with the specified number of messages and memory items."""
    # Create a state
    state = create_state(
        agent_type="research",
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        question="What is the statute of limitations for personal injury in Texas?",
        jurisdiction="texas"
    )

    # Add messages
    for i in range(num_messages):
        if i % 3 == 0:
            state.add_message(SystemMessage(content=f"System message {i}"))
        elif i % 3 == 1:
            state.add_message(HumanMessage(content=f"Human message {i}"))
        else:
            state.add_message(AIMessage(content=f"AI message {i}"))

    # Add memory items
    for i in range(memory_size):
        state.set_memory(f"key_{i}", f"value_{i}")

    return state


def time_function(func, *args, **kwargs) -> float:
    """Time a function call in milliseconds."""
    start = time.time()
    result = func(*args, **kwargs)
    end = time.time()
    return (end - start) * 1000, result  # Convert to milliseconds


def test_to_dict_performance():
    """Test the performance of state.to_dict()."""
    # Create states of different sizes
    small_state = create_test_state(num_messages=5, memory_size=5)
    medium_state = create_test_state(num_messages=20, memory_size=20)
    large_state = create_test_state(num_messages=50, memory_size=50)

    # Time the to_dict() method
    small_times = []
    medium_times = []
    large_times = []

    for _ in range(100):  # Run 100 times to get a good average
        small_time, _ = time_function(small_state.to_dict)
        medium_time, _ = time_function(medium_state.to_dict)
        large_time, _ = time_function(large_state.to_dict)

        small_times.append(small_time)
        medium_times.append(medium_time)
        large_times.append(large_time)

    # Calculate statistics
    small_avg = mean(small_times)
    medium_avg = mean(medium_times)
    large_avg = mean(large_times)

    small_std = stdev(small_times)
    medium_std = stdev(medium_times)
    large_std = stdev(large_times)

    # Print results
    print("\nto_dict() Performance:")
    print(f"Small state (5 messages, 5 memory items): {small_avg:.3f}ms ± {small_std:.3f}ms")
    print(f"Medium state (20 messages, 20 memory items): {medium_avg:.3f}ms ± {medium_std:.3f}ms")
    print(f"Large state (50 messages, 50 memory items): {large_avg:.3f}ms ± {large_std:.3f}ms")

    # Assert that all times are under 1ms
    assert small_avg < 1.0, f"Small state to_dict() took {small_avg:.3f}ms, which is over 1ms"
    assert medium_avg < 1.0, f"Medium state to_dict() took {medium_avg:.3f}ms, which is over 1ms"
    assert large_avg < 1.0, f"Large state to_dict() took {large_avg:.3f}ms, which is over 1ms"


def test_to_typed_dict_performance():
    """Test the performance of state.to_typed_dict()."""
    # Create states of different sizes
    small_state = create_test_state(num_messages=5, memory_size=5)
    medium_state = create_test_state(num_messages=20, memory_size=20)
    large_state = create_test_state(num_messages=50, memory_size=50)

    # Time the to_typed_dict() method
    small_times = []
    medium_times = []
    large_times = []

    for _ in range(100):  # Run 100 times to get a good average
        small_time, _ = time_function(small_state.to_typed_dict)
        medium_time, _ = time_function(medium_state.to_typed_dict)
        large_time, _ = time_function(large_state.to_typed_dict)

        small_times.append(small_time)
        medium_times.append(medium_time)
        large_times.append(large_time)

    # Calculate statistics
    small_avg = mean(small_times)
    medium_avg = mean(medium_times)
    large_avg = mean(large_times)

    small_std = stdev(small_times)
    medium_std = stdev(medium_times)
    large_std = stdev(large_times)

    # Print results
    print("\nto_typed_dict() Performance:")
    print(f"Small state (5 messages, 5 memory items): {small_avg:.3f}ms ± {small_std:.3f}ms")
    print(f"Medium state (20 messages, 20 memory items): {medium_avg:.3f}ms ± {medium_std:.3f}ms")
    print(f"Large state (50 messages, 50 memory items): {large_avg:.3f}ms ± {large_std:.3f}ms")

    # Assert that all times are under 1ms
    assert small_avg < 1.0, f"Small state to_typed_dict() took {small_avg:.3f}ms, which is over 1ms"
    assert medium_avg < 1.0, f"Medium state to_typed_dict() took {medium_avg:.3f}ms, which is over 1ms"
    assert large_avg < 1.0, f"Large state to_typed_dict() took {large_avg:.3f}ms, which is over 1ms"


def test_create_typed_state_performance():
    """Test the performance of create_typed_state()."""
    # Create states of different sizes
    small_state = create_test_state(num_messages=5, memory_size=5)
    medium_state = create_test_state(num_messages=20, memory_size=20)
    large_state = create_test_state(num_messages=50, memory_size=50)

    # Time the create_typed_state() function
    small_times = []
    medium_times = []
    large_times = []

    for _ in range(100):  # Run 100 times to get a good average
        small_time, _ = time_function(create_typed_state, small_state)
        medium_time, _ = time_function(create_typed_state, medium_state)
        large_time, _ = time_function(create_typed_state, large_state)

        small_times.append(small_time)
        medium_times.append(medium_time)
        large_times.append(large_time)

    # Calculate statistics
    small_avg = mean(small_times)
    medium_avg = mean(medium_times)
    large_avg = mean(large_times)

    small_std = stdev(small_times)
    medium_std = stdev(medium_times)
    large_std = stdev(large_times)

    # Print results
    print("\ncreate_typed_state() Performance:")
    print(f"Small state (5 messages, 5 memory items): {small_avg:.3f}ms ± {small_std:.3f}ms")
    print(f"Medium state (20 messages, 20 memory items): {medium_avg:.3f}ms ± {medium_std:.3f}ms")
    print(f"Large state (50 messages, 50 memory items): {large_avg:.3f}ms ± {large_std:.3f}ms")

    # Assert that all times are under 1ms
    assert small_avg < 1.0, f"Small state create_typed_state() took {small_avg:.3f}ms, which is over 1ms"
    assert medium_avg < 1.0, f"Medium state create_typed_state() took {medium_avg:.3f}ms, which is over 1ms"
    assert large_avg < 1.0, f"Large state create_typed_state() took {large_avg:.3f}ms, which is over 1ms"


def test_round_trip_performance():
    """Test the performance of a round-trip conversion (Pydantic → TypedDict → Pydantic)."""
    # Create a state
    state = create_test_state(num_messages=20, memory_size=20)

    # Time the round-trip conversion
    times = []

    for _ in range(100):  # Run 100 times to get a good average
        start = time.time()

        # Convert to TypedDict
        typed_dict = state.to_typed_dict()

        # Convert back to Pydantic
        state_obj = StateManager.from_typed_dict(typed_dict)

        end = time.time()
        times.append((end - start) * 1000)  # Convert to milliseconds

    # Calculate statistics
    avg = mean(times)
    std = stdev(times)

    # Print results
    print("\nRound-trip conversion Performance:")
    print(f"Medium state (20 messages, 20 memory items): {avg:.3f}ms ± {std:.3f}ms")

    # Assert that the time is under 2ms
    assert avg < 2.0, f"Round-trip conversion took {avg:.3f}ms, which is over 2ms"


if __name__ == "__main__":
    # Run the tests
    test_to_dict_performance()
    test_to_typed_dict_performance()
    test_create_typed_state_performance()
    test_round_trip_performance()
