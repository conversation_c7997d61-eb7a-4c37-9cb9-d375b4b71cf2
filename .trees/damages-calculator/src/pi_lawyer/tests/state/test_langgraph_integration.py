"""
Integration tests for LangGraph state management.

This module tests the integration between our state management system and LangGraph.
"""

from typing import Any, Dict
from unittest.mock import patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import StateGraph

from pi_lawyer.state.langgraph_state import (
    LangGraphCheckpointSaver,
    create_graph_for_agent,
    create_state,
    create_typed_state,
    get_state_class_for_agent_type,
)


# Mock node functions for LangGraph
async def mock_research_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """Mock research node for testing."""
    # Add a message to the state
    return {
        "messages": [AIMessage(content="This is a response from the research node.")],
        "legal_documents": [{"title": "Mock Document", "content": "Mock content"}]
    }


async def mock_router_node(state: Dict[str, Any]) -> str:
    """Mock router node for testing."""
    # Simple routing logic
    if "research" in state["messages"][-1].content.lower():
        return "research"
    return "default"


# Integration tests with LangGraph
@pytest.mark.asyncio
class TestLangGraphIntegration:
    """Integration tests for LangGraph state management."""

    async def test_create_graph_for_agent(self):
        """Test creating a LangGraph StateGraph for an agent type."""
        # Arrange
        agent_type = "research"
        tenant_id = "tenant-123"

        # Act
        graph = create_graph_for_agent(agent_type, tenant_id)

        # Assert
        assert isinstance(graph, StateGraph)

    async def test_graph_with_state(self):
        """Test using a LangGraph StateGraph with our state management."""
        # Create a state
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )

        # Convert to typed dict for LangGraph
        typed_state = create_typed_state(state)

        # Create a graph
        graph = create_graph_for_agent("research", "tenant-123")

        # Add nodes to the graph
        graph.add_node("research", mock_research_node)
        graph.add_node("default", lambda x: {"messages": [AIMessage(content="I don't understand.")]})

        # Add conditional edges
        graph.add_conditional_edges(
            "__start__",
            mock_router_node,
            {
                "research": "research",
                "default": "default"
            }
        )

        # Add edges to end
        graph.add_edge("research", "__end__")
        graph.add_edge("default", "__end__")

        # Compile the graph
        compiled_graph = graph.compile()

        # Act
        # Add a message to trigger the research node
        typed_state["messages"] = [HumanMessage(content="Can you research the statute of limitations?")]

        # Run the graph
        result = await compiled_graph.ainvoke(typed_state)

        # Assert
        assert "messages" in result
        assert len(result["messages"]) == 2  # Original message + response
        assert "This is a response from the research node." in result["messages"][1].content
        assert "legal_documents" in result
        assert len(result["legal_documents"]) == 1

    @patch("pi_lawyer.state.persistence.save_state")
    @patch("pi_lawyer.state.persistence.load_state")
    async def test_checkpoint_saver(self, mock_load_state, mock_save_state):
        """Test the LangGraphCheckpointSaver."""
        # Arrange
        tenant_id = "tenant-123"
        thread_id = "thread-789"

        state = create_state(
            agent_type="research",
            tenant_id=tenant_id,
            user_id="user-456",
            thread_id=thread_id,
            question="What is the statute of limitations for personal injury in Texas?"
        )

        typed_state = create_typed_state(state)

        mock_load_state.return_value = state
        mock_save_state.return_value = "record-123"

        saver = LangGraphCheckpointSaver(tenant_id)

        # Act - Test get
        result = await saver.get(thread_id)

        # Assert - get
        assert result is not None
        assert result["agent_type"] == "research"
        assert result["question"] == "What is the statute of limitations for personal injury in Texas?"
        mock_load_state.assert_called_once_with(thread_id, tenant_id)

        # Act - Test put
        await saver.put(thread_id, typed_state)

        # Assert - put
        mock_save_state.assert_called_once()

        # Act - Test delete (simplified for testing)
        with patch("pi_lawyer.state.langgraph_state.logger") as mock_logger:
            await saver.delete(thread_id)
            # Assert - delete
            mock_logger.info.assert_called_once_with(
                f"Deleting state for key {thread_id} in tenant {tenant_id}"
            )


# Test with a complete LangGraph workflow
@pytest.mark.asyncio
class TestCompleteWorkflow:
    """Test a complete LangGraph workflow with our state management."""

    @patch.object(LangGraphCheckpointSaver, "get")
    @patch.object(LangGraphCheckpointSaver, "put")
    async def test_research_workflow(self, mock_put, mock_get):
        """Test a complete research workflow."""
        # Arrange
        mock_get.return_value = None  # No existing state
        mock_put.return_value = None

        # Create initial state
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )

        # Add a message to the state
        state.add_message(HumanMessage(content="I need to research personal injury law in Texas."))

        # Convert to typed dict for LangGraph
        typed_state = create_typed_state(state)

        # Create a research workflow
        workflow = StateGraph(get_state_class_for_agent_type("research"))

        # Define node functions
        async def classify_query(state: Dict[str, Any]) -> Dict[str, Any]:
            return {"query_type": "public"}

        async def generate_queries(state: Dict[str, Any]) -> Dict[str, Any]:
            return {"queries": ["texas personal injury statute of limitations"]}

        async def search_data(state: Dict[str, Any]) -> Dict[str, Any]:
            return {
                "legal_documents": [
                    {"title": "Texas Civil Practice and Remedies Code", "content": "§ 16.003. TWO-YEAR LIMITATIONS PERIOD"}
                ],
                "messages": [AIMessage(content="In Texas, the statute of limitations for personal injury is 2 years.")]
            }

        # Add nodes
        workflow.add_node("classify", classify_query)
        workflow.add_node("generate_queries", generate_queries)
        workflow.add_node("search", search_data)

        # Add edges
        workflow.add_edge("__start__", "classify")
        workflow.add_edge("classify", "generate_queries")
        workflow.add_edge("generate_queries", "search")
        workflow.add_edge("search", "__end__")

        # Compile the workflow
        compiled_workflow = workflow.compile()

        # Act
        result = await compiled_workflow.ainvoke(typed_state)

        # Assert
        assert "messages" in result
        assert len(result["messages"]) == 2  # Original message + response
        assert "In Texas, the statute of limitations for personal injury is 2 years." in result["messages"][1].content
        assert "legal_documents" in result
        assert len(result["legal_documents"]) == 1
        assert "Texas Civil Practice and Remedies Code" in result["legal_documents"][0]["title"]
        assert "queries" in result
        assert "query_type" in result
        assert result["query_type"] == "public"
