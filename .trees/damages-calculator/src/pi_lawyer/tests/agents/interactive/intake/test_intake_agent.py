"""
Tests for the Intake Agent

This module contains tests for the Intake Agent implementation.
"""

from typing import Any, Dict
from unittest.mock import AsyncMock, patch

import pytest

from pi_lawyer.agents.config import AgentConfig
from pi_lawyer.agents.interactive.intake.agent import IntakeAgent
from pi_lawyer.agents.interactive.intake.routers import (
    intake_client_router,
    intake_staff_router,
)
from pi_lawyer.agents.interactive.intake.tasks import (
    check_conflicts,
    collect_case_details,
    collect_personal_info,
    initial_contact,
    save_client_info,
    summarize_and_confirm,
)


class TestIntakeAgent:
    """Tests for the IntakeAgent class."""

    @pytest.fixture
    def agent_config(self) -> AgentConfig:
        """Create a test agent configuration."""
        return AgentConfig(
            name="intake_agent",
            agent_type="interactive",
            description="Intake agent for collecting client information",
            version="1.0.0"
        )

    @pytest.fixture
    def intake_agent(self, agent_config: AgentConfig) -> IntakeAgent:
        """Create a test intake agent."""
        return IntakeAgent(agent_config)

    @pytest.fixture
    def mock_llm_client(self) -> AsyncMock:
        """Create a mock LLM client."""
        mock_client = AsyncMock()
        mock_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "I'll help you with the intake process. Let's start by collecting your name."
                    }
                }
            ]
        }
        return mock_client

    @pytest.fixture
    def mock_tool_executor(self) -> AsyncMock:
        """Create a mock tool executor."""
        mock_executor = AsyncMock()
        mock_executor.execute_tool.return_value = {"id": "test-id"}
        return mock_executor

    @pytest.fixture
    def basic_state(self) -> Dict[str, Any]:
        """Create a basic state for testing."""
        return {
            "messages": [
                {"type": "human", "content": "I need to submit a new case"}
            ],
            "client": {},
            "case": {},
            "memory": {},
            "intake_mode": "client"
        }

    @pytest.fixture
    def config(self) -> Dict[str, Any]:
        """Create a basic config for testing."""
        return {
            "configurable": {
                "thread_id": "test-thread",
                "tenant_id": "test-tenant",
                "user_id": "test-user"
            }
        }

    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_initialize(self, mock_voyage_client, intake_agent: IntakeAgent, basic_state: Dict[str, Any], config: Dict[str, Any]):
        """Test the initialize method."""
        # Arrange
        mock_voyage_client.return_value = AsyncMock()

        # Act
        result = await intake_agent.initialize(basic_state, config)

        # Assert
        assert "intake_mode" in result
        assert result["intake_mode"] == "client"
        assert "client" in result
        assert "case" in result
        assert "next" in result
        assert result["next"] == "initial_contact"
        assert any(m.get("type") == "system" for m in result["messages"])

    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_execute(self, mock_voyage_client, intake_agent: IntakeAgent, basic_state: Dict[str, Any], config: Dict[str, Any], mock_llm_client: AsyncMock):
        """Test the execute method."""
        # Arrange
        intake_agent.llm_client = mock_llm_client

        # Make a copy of the basic state to ensure we're comparing against the original
        original_message_count = len(basic_state["messages"])

        # Act
        result = await intake_agent.execute(basic_state, config)

        # Assert
        assert "next" in result
        assert len(result["messages"]) > original_message_count
        mock_llm_client.chat_completion.assert_called_once()

    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_cleanup(self, mock_voyage_client, intake_agent: IntakeAgent, basic_state: Dict[str, Any], config: Dict[str, Any]):
        """Test the cleanup method."""
        # Arrange
        basic_state["memory"] = {}

        # Act
        result = await intake_agent.cleanup(basic_state, config)

        # Assert
        assert "cleaned_up_at" in result["memory"]

    async def test_client_router(self):
        """Test the client router."""
        # Arrange
        state = {"messages": []}
        config = {}

        # Act
        result = await intake_client_router(state, config)

        # Assert
        assert result["intake_mode"] == "client"
        assert result["next"] == "initial_contact"
        assert any(m.get("content", "").startswith("Welcome to the client intake") for m in result["messages"])

    async def test_staff_router(self):
        """Test the staff router."""
        # Arrange
        state = {"messages": []}
        config = {}

        # Act
        result = await intake_staff_router(state, config)

        # Assert
        assert result["intake_mode"] == "staff"
        assert result["next"] == "initial_contact"
        assert any(m.get("content", "").startswith("Welcome to the staff intake") for m in result["messages"])

    async def test_staff_router_with_prefilled_data(self):
        """Test the staff router with prefilled data."""
        # Arrange
        state = {
            "messages": [],
            "client": {"name": "John Doe"},
            "case": {"description": "Car accident"}
        }
        config = {}

        # Act
        result = await intake_staff_router(state, config)

        # Assert
        assert result["intake_mode"] == "staff"
        assert result["next"] == "summarize_and_confirm"

    async def test_initial_contact(self):
        """Test the initial contact task."""
        # Arrange
        state = {"messages": [{"type": "human", "content": "Hello"}]}
        config = {}

        # Act
        result = await initial_contact(state, config)

        # Assert
        assert len(result["messages"]) > 1
        assert any("welcome" in m.get("content", "").lower() for m in result["messages"])

    async def test_initial_contact_with_name(self):
        """Test the initial contact task with a name."""
        # Arrange
        state = {
            "messages": [
                {"type": "system", "content": "System message"},
                {"type": "ai", "content": "Welcome message"},
                {"type": "human", "content": "John Doe"}
            ],
            "client": {}
        }
        config = {}

        # Act
        result = await initial_contact(state, config)

        # Assert
        assert "name" in result["client"]
        assert result["client"]["name"] == "John Doe"
        assert result["next"] == "collect_personal_info"

    @patch("pi_lawyer.agents.interactive.intake.tasks.get_tool_executor")
    async def test_save_client_info(self, mock_get_tool_executor, mock_tool_executor: AsyncMock):
        """Test the save client info task."""
        # Arrange
        mock_get_tool_executor.return_value = mock_tool_executor
        state = {
            "messages": [],
            "client": {"name": "John Doe", "email": "<EMAIL>", "phone": "************"},
            "case": {"description": "Car accident", "case_type": "auto_accident", "practice_area": "personal_injury"}
        }
        config = {"configurable": {"tenant_id": "test-tenant", "user_id": "test-user"}}

        # Act
        result = await save_client_info(state, config)

        # Assert
        assert "id" in result["client"]
        assert "id" in result["case"]
        assert result["next"] == "FINISH"
        assert mock_tool_executor.execute_tool.call_count == 2


class TestIntakeAgentIntegration:
    """Integration tests for the Intake Agent."""

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.tasks.get_tool_executor")
    async def test_client_flow(self, mock_get_tool_executor, mock_voyage_client):
        """Test the client flow from start to finish."""
        # Arrange
        mock_voyage_client.return_value = AsyncMock()
        mock_voyage_client.return_value.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "I'll help you with the intake process. Let's collect your information. Next step: collect_personal_info"
                    }
                }
            ]
        }

        mock_tool_executor = AsyncMock()
        mock_tool_executor.execute_tool.return_value = {"id": "test-id"}
        mock_get_tool_executor.return_value = mock_tool_executor

        agent = IntakeAgent()
        agent.llm_client = mock_voyage_client.return_value

        # Initial state
        state = {
            "messages": [{"type": "human", "content": "I need to submit a new case"}],
            "client": {},
            "case": {},
            "memory": {},
            "next": "initial_contact"
        }
        config = {"configurable": {"thread_id": "test-thread", "tenant_id": "test-tenant", "user_id": "test-user"}}

        # Act - Initial contact
        state = await initial_contact(state, config)

        # Update state with client name
        state["messages"].append({"type": "human", "content": "John Doe"})
        state = await initial_contact(state, config)

        # Act - Collect personal info
        state["messages"].append({"type": "human", "content": "<EMAIL> ************"})
        state = await collect_personal_info(state, config)

        # Act - Collect case details
        state["messages"].append({"type": "human", "content": "I was in a car accident on Main Street. The other driver ran a red light and hit my car."})
        state = await collect_case_details(state, config)

        # Act - Check conflicts
        state = await check_conflicts(state, config)

        # Act - Summarize and confirm
        state = await summarize_and_confirm(state, config)

        # Act - Confirm and save
        state["messages"].append({"type": "human", "content": "Yes, that's correct"})
        state = await summarize_and_confirm(state, config)
        state = await save_client_info(state, config)

        # Assert
        assert state["next"] == "FINISH"
        assert "id" in state["client"]
        assert "id" in state["case"]
        # The tool is called 3 times: check_conflicts, create_client, create_case
        assert mock_tool_executor.execute_tool.call_count == 3
