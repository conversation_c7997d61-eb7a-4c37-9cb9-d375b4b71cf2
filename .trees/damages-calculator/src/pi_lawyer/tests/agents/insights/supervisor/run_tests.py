#!/usr/bin/env python
"""
Test Runner for Supervisor Agent Tests

This script runs all the tests for the Supervisor Agent implementation.
"""

import os
import sys
import time
from datetime import datetime

import pytest

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../")))


def run_tests():
    """Run all the tests for the Supervisor Agent implementation."""
    print("=" * 80)
    print(f"Running Supervisor Agent Tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Get the directory of this script
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Run the tests
    test_files = [
        "test_supervisor_agent.py",
        "test_supervisor_integration.py",
        "test_supervisor_performance.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        test_path = os.path.join(test_dir, test_file)
        
        print(f"\nRunning tests in {test_file}...")
        start_time = time.time()
        
        # Run the tests
        result = pytest.main(["-xvs", test_path])
        
        end_time = time.time()
        duration = end_time - start_time
        
        results[test_file] = {
            "result": "PASSED" if result == 0 else "FAILED",
            "duration": duration
        }
    
    # Print the summary
    print("\n" + "=" * 80)
    print("Test Summary")
    print("=" * 80)
    
    total_duration = 0
    all_passed = True
    
    for test_file, data in results.items():
        print(f"{test_file}: {data['result']} in {data['duration']:.2f} seconds")
        total_duration += data["duration"]
        if data["result"] != "PASSED":
            all_passed = False
    
    print("-" * 80)
    print(f"Total duration: {total_duration:.2f} seconds")
    print(f"Overall result: {'PASSED' if all_passed else 'FAILED'}")
    print("=" * 80)
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(run_tests())
