# Supervisor Agent Tests

This directory contains comprehensive tests for the Supervisor Agent implementation.

## Test Categories

### Unit Tests (`test_supervisor_agent.py`)

These tests focus on individual methods of the Supervisor Agent:

- `initialize`: Tests that the agent initializes state correctly
- `_classify_intent`: Tests intent classification with various inputs
  - Function calling
  - Content parsing fallback
  - Empty messages
  - Non-human messages
  - With matter_id
- `execute`: Tests the execution flow
  - Interactive agents
  - Async agents
  - Low confidence handling
- `cleanup`: Tests the cleanup method

### Integration Tests (`test_supervisor_integration.py`)

These tests focus on the integration of the Supervisor Agent with other components:

- Router integration
  - With page_intent
  - With next
  - Default behavior
  - String input
- Tenant isolation
- Confidence threshold behavior
- StateGraph integration

### Performance and Error Handling Tests (`test_supervisor_performance.py`)

These tests focus on performance and error handling:

- Cache performance
- Cache with different tenants
- Concurrent requests
- LLM failure recovery
- Malformed LLM responses
- Async job failure

### Property-Based Tests

These tests use hypothesis to fuzz-test the agent with random inputs:

- `test_classify_intent_fuzz`: Tests that the `_classify_intent` method never throws an exception regardless of input

## Running the Tests

You can run all the tests using the provided test runner:

```bash
./run_tests.py
```

Or run individual test files using pytest:

```bash
pytest -xvs test_supervisor_agent.py
pytest -xvs test_supervisor_integration.py
pytest -xvs test_supervisor_performance.py
```

## Test Coverage

The tests cover:

- Happy path scenarios
- Error handling and recovery
- Edge cases
- Performance characteristics
- Tenant isolation
- Command API integration
- StateGraph integration

## Adding New Tests

When adding new tests, follow these guidelines:

1. Use descriptive test names that clearly indicate what is being tested
2. Use fixtures to set up common test dependencies
3. Mock external dependencies like the LLM client
4. Test both success and failure scenarios
5. Add property-based tests for complex methods
6. Update this README when adding new test categories
