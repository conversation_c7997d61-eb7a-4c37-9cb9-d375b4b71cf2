#!/usr/bin/env python
"""
Standalone Test for Supervisor Agent

This script tests the Supervisor Agent implementation without depending on the existing codebase.
"""

import asyncio
import json
import logging
from typing import Any, Dict, Union
from unittest.mock import AsyncMock, MagicMock

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock Command class
class Command:
    def __init__(self, goto):
        self.goto = goto

# Mock Classification class
class Classification:
    def __init__(self, agent, args, confidence):
        self.agent = agent
        self.args = args
        self.confidence = confidence

    def dict(self):
        return {
            "agent": self.agent,
            "args": self.args,
            "confidence": self.confidence
        }

# Mock JsonOutputParser class
class JsonOutputParser:
    def __init__(self):
        pass

    def parse(self, text):
        return json.loads(text)

# Mock VoyageClient class
class VoyageClient:
    def __init__(self):
        pass

    async def chat_completion(self, messages, model, temperature, max_tokens, functions=None, function_call=None):
        return {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": '{"agent": "researchAgent", "args": {"query": "test query"}, "confidence": 0.95}'
                        }
                    }
                }
            ]
        }

# Mock EnqueueAsyncJobTool class
class EnqueueAsyncJobTool:
    async def execute(self, **kwargs):
        return MagicMock(job_id="job-123")

# Mock BaseAgent class
class BaseAgent:
    def __init__(self, config=None):
        self.config = config or {}

    async def initialize(self, state, config):
        return state

    async def execute(self, state, config):
        return state

    async def cleanup(self, state, config):
        return state

# Constants
DEFAULT_AGENT = "intakeAgent"
CONFIDENCE_THRESHOLD = 0.6
VOYAGE_MODEL = "voyage-large"
INTERACTIVE_AGENTS = {
    "intakeAgent",
    "researchAgent",
    "taskCrudAgent",
    "calendarCrudAgent"
}
ASYNC_AGENTS = {
    "documentDraftAgent",
    "insightSwarmAgent"
}

# SupervisorAgent implementation
class SupervisorAgent(BaseAgent):
    """
    Supervisor Agent for AiLex.

    This agent is responsible for:
    1. Classifying user intent in ≤ 1 LLM call
    2. Dispatching to the correct node
    3. Maintaining tenant isolation
    4. Failing gracefully
    """

    def __init__(self, config=None, llm_client=None):
        """
        Initialize the supervisor agent.

        Args:
            config: Agent configuration
            llm_client: Voyage LLM client (for testing)
        """
        super().__init__(config)

        # Initialize LLM client
        self.llm_client = llm_client or VoyageClient()

        # Initialize JSON output parser
        self.parser = JsonOutputParser()

        # Initialize simple in-memory cache
        self._cache = {}
        self._cache_max_size = 100
        self._cache_ttl_ms = 500  # milliseconds

    async def _classify_intent(self, state: Dict[str, Any]) -> Classification:
        """
        Classify the user's intent using structured output parsing.

        Args:
            state: Current state

        Returns:
            Classification result with agent, args, and confidence
        """
        # Extract the last human message
        last_message = None
        for message in reversed(state.get("messages", [])):
            if message.get("type") == "human" or message.get("role") == "user":
                last_message = message
                break

        if not last_message:
            logger.warning("No human message found in state")
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )

        # Get message content
        content = last_message.get("content", "")
        if isinstance(content, list):
            content = " ".join([str(c) for c in content])

        try:
            # Call the LLM with function calling
            response = await self.llm_client.chat_completion(
                messages=[{"role": "user", "content": content}],
                model=VOYAGE_MODEL,
                temperature=0.2,
                max_tokens=500,
                functions=[{}],
                function_call={"name": "classify_intent"}
            )

            # Extract the function call result
            function_call = response.get("choices", [{}])[0].get("message", {}).get("function_call", {})

            if function_call and function_call.get("name") == "classify_intent":
                try:
                    # Parse the arguments
                    args_json = function_call.get("arguments", "{}")
                    parsed = json.loads(args_json)

                    # Create a Classification object
                    result = Classification(
                        agent=parsed.get("agent", DEFAULT_AGENT),
                        args=parsed.get("args", {}),
                        confidence=parsed.get("confidence", 0.0)
                    )

                    return result
                except Exception as e:
                    logger.error(f"Error parsing function call result: {str(e)}")

            # Fallback to default
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )
        except Exception as e:
            logger.error(f"Error calling LLM for intent classification: {str(e)}")
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )

    async def execute(self, state: Dict[str, Any], config: Dict[str, Any]) -> Union[Dict[str, Any], Command]:
        """
        Execute the supervisor agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with Command for routing
        """
        logger.info("Executing supervisor agent")

        # Get tenant_id from state
        tenant_id = state.get("tenant_id")
        if not tenant_id:
            logger.warning("No tenant_id found in state, using default")
            tenant_id = "default"

        # Classify the user's intent
        classification = await self._classify_intent(state)

        # Extract agent and args
        agent = classification.agent
        args = classification.args
        confidence = classification.confidence

        logger.info(f"Classified intent: agent={agent}, confidence={confidence}")

        # Check confidence threshold
        if confidence < CONFIDENCE_THRESHOLD:
            logger.info(f"Confidence below threshold ({confidence} < {CONFIDENCE_THRESHOLD}), using default agent")
            agent = DEFAULT_AGENT
            args = {}

        # Store the classification in state
        if "memory" in state:
            state["memory"]["classification"] = classification.dict()

        # Store the args in state for the next agent
        state["agent_args"] = args

        # Handle interactive vs. async agents
        if agent in INTERACTIVE_AGENTS:
            # For interactive agents, return Command to route directly
            logger.info(f"Routing to interactive agent: {agent}")
            return Command(goto=agent)

        elif agent in ASYNC_AGENTS:
            # For async agents, enqueue a job
            logger.info(f"Enqueueing async job for agent: {agent}")

            try:
                # Use the enqueue_async_job_tool directly
                job_result = await EnqueueAsyncJobTool().execute(
                    tool_name=agent,
                    params=args,
                    tenant_id=tenant_id,
                    user_id=state.get("user_id", "unknown"),
                    thread_id=state.get("thread_id", "unknown"),
                    matter_id=state.get("matter_id")
                )

                # Store the job ID in state
                state["async_job_id"] = job_result.job_id

                # Add a message indicating the job was queued
                if "messages" in state:
                    state["messages"].append({
                        "type": "ai",
                        "content": "I'm working on that... I'll update you soon."
                    })

                logger.info(f"Async job enqueued with ID: {job_result.job_id}")
                return Command(goto="FINISH")

            except Exception as e:
                logger.error(f"Failed to enqueue async job: {str(e)}")

                # Add an error message
                if "messages" in state:
                    state["messages"].append({
                        "type": "ai",
                        "content": "I'm sorry, I encountered an error while processing your request."
                    })

                return Command(goto="FINISH")

        # Default case or unknown agent
        logger.warning(f"Unknown agent type: {agent}, using FINISH")
        return Command(goto="FINISH")

# Test the SupervisorAgent
async def test_supervisor_agent():
    print("\n=== Test 1: Interactive Agent ===")
    # Create a SupervisorAgent
    agent = SupervisorAgent()

    # Create a state for research agent
    state = {
        "messages": [
            {"type": "human", "content": "Research the statute of limitations for personal injury in Texas"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    # Execute the agent
    result = await agent.execute(state, {})

    # Print the result
    print(f"Result: {result.goto}")
    print(f"State: {state}")

    # Test with async agent
    print("\n=== Test 2: Async Agent ===")
    # Create a mock VoyageClient that returns documentDraftAgent
    mock_client = VoyageClient()
    mock_client.chat_completion = AsyncMock(return_value={
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": '{"agent": "documentDraftAgent", "args": {"template": "demand_letter"}, "confidence": 0.9}'
                    }
                }
            }
        ]
    })

    # Create a SupervisorAgent with the mock client
    agent = SupervisorAgent(llm_client=mock_client)

    # Create a state for document draft agent
    state = {
        "messages": [
            {"type": "human", "content": "Draft a demand letter for my case"}
        ],
        "tenant_id": "test-tenant",
        "user_id": "user-123",
        "thread_id": "thread-456",
        "matter_id": "case-789",
        "memory": {}
    }

    # Execute the agent
    result = await agent.execute(state, {})

    # Print the result
    print(f"Result: {result.goto}")
    print(f"State: {state}")

    # Test with low confidence
    print("\n=== Test 3: Low Confidence ===")
    # Create a mock VoyageClient that returns low confidence
    mock_client = VoyageClient()
    mock_client.chat_completion = AsyncMock(return_value={
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": '{"agent": "researchAgent", "args": {"query": "unclear query"}, "confidence": 0.3}'
                    }
                }
            }
        ]
    })

    # Create a SupervisorAgent with the mock client
    agent = SupervisorAgent(llm_client=mock_client)

    # Create a state with unclear message
    state = {
        "messages": [
            {"type": "human", "content": "I'm not sure what I need"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    # Execute the agent
    result = await agent.execute(state, {})

    # Print the result
    print(f"Result: {result.goto}")
    print(f"State: {state}")

    # Return success
    return True

# Run the test
if __name__ == "__main__":
    result = asyncio.run(test_supervisor_agent())
    print(f"\nAll tests {'passed' if result else 'failed'}")
