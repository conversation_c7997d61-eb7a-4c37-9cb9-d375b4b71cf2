"""
Tests for Insight Agents

This module contains tests for the Insight Agents implementation.
"""

import importlib.util
import os
from unittest.mock import MagicMock

import pytest

# Define paths to the modules we want to test
document_path = os.path.join(os.path.dirname(__file__), '../../../agents/insights/document.py')
daily_path = os.path.join(os.path.dirname(__file__), '../../../agents/insights/daily.py')
swarm_path = os.path.join(os.path.dirname(__file__), '../../../agents/insights/swarm.py')

# Import the modules directly using importlib
spec = importlib.util.spec_from_file_location('document', document_path)
document = importlib.util.module_from_spec(spec)
spec.loader.exec_module(document)

spec = importlib.util.spec_from_file_location('daily', daily_path)
daily = importlib.util.module_from_spec(spec)
spec.loader.exec_module(daily)

spec = importlib.util.spec_from_file_location('swarm', swarm_path)
swarm = importlib.util.module_from_spec(spec)
spec.loader.exec_module(swarm)


class TestInsightAgents:
    """Tests for the Insight Agents."""

    @pytest.fixture
    def mock_graph(self):
        """Create a mock StateGraph for testing."""
        # Create a mock StateGraph
        mock_graph = MagicMock()
        
        # Mock the add_node method
        mock_graph.add_node = MagicMock()
        
        return mock_graph

    def test_document_register(self, mock_graph):
        """Test that the document agent register function adds nodes without error."""
        # Call the register function
        node_ids = document.register(mock_graph)
        
        # Verify that add_node was called for each node
        assert mock_graph.add_node.call_count == 2
        
        # Verify that the correct node IDs were returned
        assert "collect_document_context" in node_ids
        assert "draft_document_insights" in node_ids
        
        # Verify that the nodes were added with the correct functions
        mock_graph.add_node.assert_any_call("collect_document_context", document.collect_context)
        mock_graph.add_node.assert_any_call("draft_document_insights", document.draft_doc)

    def test_daily_register(self, mock_graph):
        """Test that the daily agent register function adds nodes without error."""
        # Call the register function
        node_ids = daily.register(mock_graph)
        
        # Verify that add_node was called for each node
        assert mock_graph.add_node.call_count == 2
        
        # Verify that the correct node IDs were returned
        assert "gather_daily_events" in node_ids
        assert "summarize_daily_activities" in node_ids
        
        # Verify that the nodes were added with the correct functions
        mock_graph.add_node.assert_any_call("gather_daily_events", daily.gather_events)
        mock_graph.add_node.assert_any_call("summarize_daily_activities", daily.summarize)

    def test_swarm_register(self, mock_graph):
        """Test that the swarm agent register function adds nodes without error."""
        # Call the register function
        node_ids = swarm.register(mock_graph)
        
        # Verify that add_node was called for each node
        assert mock_graph.add_node.call_count == 1
        
        # Verify that the correct node IDs were returned
        assert "spawn_child_agents" in node_ids
        
        # Verify that the nodes were added with the correct functions
        mock_graph.add_node.assert_any_call("spawn_child_agents", swarm.spawn_children)
