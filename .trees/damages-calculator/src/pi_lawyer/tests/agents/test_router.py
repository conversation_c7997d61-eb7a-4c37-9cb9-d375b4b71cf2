"""
Tests for the Router module

This module contains tests for the router implementation.
"""

from src.pi_lawyer.agents.router import (
    analyze_request,
    get_agent_description,
    route_request,
)
from src.pi_lawyer.state.state import create_state


def test_route_request_with_active_agent():
    """Test routing with an active agent in memory."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Set an active agent in memory
    state.set_active_agent("research_agent")
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "research_agent"

def test_route_request_with_no_matter_id():
    """Test routing with no matter ID."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "intake_agent"

def test_route_request_with_research_keywords():
    """Test routing with research keywords."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Add a message with research keywords
    state.add_message("user", "Can you research this legal question for me?")
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "research_agent"

def test_route_request_with_document_keywords():
    """Test routing with document keywords."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Add a message with document keywords
    state.add_message("user", "Can you draft a document for me?")
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "document_agent"

def test_route_request_with_deadline_keywords():
    """Test routing with deadline keywords."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Add a message with deadline keywords
    state.add_message("user", "What's the deadline for this case?")
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "deadline_agent"

def test_route_request_default():
    """Test default routing."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Add a message with no specific keywords
    state.add_message("user", "Hello, how are you?")
    
    # Route the request
    agent = route_request(state)
    
    # Verify the routing
    assert agent == "supervisor_agent"

def test_get_agent_description():
    """Test getting agent descriptions."""
    # Test known agents
    assert "Echo Agent" in get_agent_description("echo_agent")
    assert "Intake Agent" in get_agent_description("intake_agent")
    assert "Research Agent" in get_agent_description("research_agent")
    assert "Document Agent" in get_agent_description("document_agent")
    assert "Deadline Agent" in get_agent_description("deadline_agent")
    assert "Supervisor Agent" in get_agent_description("supervisor_agent")
    
    # Test unknown agent
    assert "Unknown agent" == get_agent_description("unknown_agent")

def test_analyze_request():
    """Test request analysis."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112"
    )
    
    # Add some messages
    state.add_message("user", "Can you research this legal question for me?")
    state.add_message("assistant", "I'll look into that for you.")
    
    # Analyze the request
    analysis = analyze_request(state)
    
    # Verify the analysis
    assert analysis["agent"] == "research_agent"
    assert "Research Agent" in analysis["agent_description"]
    assert analysis["context"]["tenant_id"] == "tenant-123"
    assert analysis["context"]["user_id"] == "user-456"
    assert analysis["context"]["thread_id"] == "thread-789"
    assert analysis["context"]["matter_id"] == "matter-101112"
    assert analysis["context"]["message_count"] == 2
    assert analysis["context"]["has_user_messages"] is True
    assert analysis["context"]["has_assistant_messages"] is True
    assert "timestamp" in analysis
