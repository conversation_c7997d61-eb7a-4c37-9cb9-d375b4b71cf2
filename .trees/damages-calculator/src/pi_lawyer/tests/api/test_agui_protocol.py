"""
Tests for the enhanced AG-UI protocol implementation.

This module contains comprehensive tests for the enhanced AG-UI protocol implementation,
including model validation, streaming response handling, authentication integration,
error handling, and telemetry collection.

Key test areas:
- Model validation for all AG-UI protocol models
- Streaming response format and handling
- Non-streaming response format and handling
- Error handling for various error scenarios
- Thread management and isolation
- Telemetry collection and performance monitoring
- Authentication integration
"""

import json
import os
import sys
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

# Mock the get_config function to avoid import errors
import sys

# Create a mock for the get_config function
mock_get_config = MagicMock()
mock_get_config.return_value = {
    "supabase": {
        "url": "https://example.com",
        "key": "test-key"
    }
}

# Add the mock to sys.modules
sys.modules["pi_lawyer.config.get_config"] = mock_get_config

# Patch the import in auth middleware
import types

mock_config_module = types.ModuleType("pi_lawyer.config")
mock_config_module.get_config = mock_get_config
sys.modules["pi_lawyer.config"] = mock_config_module

# Mock the telemetry manager
class MockTelemetryManager:
    def __init__(self, *args, **kwargs):
        pass

    def start_request(self, *args, **kwargs):
        return "mock-request-id"

    def update_request(self, *args, **kwargs):
        pass

    def mark_first_token(self, *args, **kwargs):
        pass

    def complete_request(self, *args, **kwargs):
        pass

# Create a mock module for telemetry
mock_telemetry_module = types.ModuleType("pi_lawyer.utils.telemetry")
mock_telemetry_module.TelemetryManager = MockTelemetryManager
sys.modules["pi_lawyer.utils.telemetry"] = mock_telemetry_module

# Mock FastAPI HTTPException
class MockHTTPException(Exception):
    def __init__(self, status_code=500, detail="Internal Server Error"):
        self.status_code = status_code
        self.detail = detail
        super().__init__(self.detail)

# Create a mock module for fastapi
mock_fastapi_module = types.ModuleType("fastapi")
mock_fastapi_module.HTTPException = MockHTTPException
mock_fastapi_module.FastAPI = MagicMock
mock_fastapi_module.Request = MagicMock
mock_fastapi_module.Depends = MagicMock
mock_fastapi_module.status = MagicMock
mock_fastapi_module.APIRouter = MagicMock
mock_fastapi_module.Response = MagicMock
mock_fastapi_module.BackgroundTasks = MagicMock
mock_fastapi_module.responses = MagicMock
sys.modules["fastapi"] = mock_fastapi_module
sys.modules["fastapi.testclient"] = types.ModuleType("fastapi.testclient")
sys.modules["fastapi.testclient"].TestClient = MagicMock

# Mock httpx AsyncClient
mock_httpx_module = types.ModuleType("httpx")
mock_httpx_module.AsyncClient = MagicMock
sys.modules["httpx"] = mock_httpx_module

from fastapi import FastAPI

from pi_lawyer.api.routes.copilotkit_endpoint import (
    AGUIErrorType,
    AGUIEventType,
    AGUIMessage,
    AGUIRequest,
    AGUIResponse,
    AGUIStreamEvent,
    AGUIToolCall,
    AGUIToolCallFunction,
    router,
)
from pi_lawyer.models.auth import UserContext

# Create a test app
app = FastAPI()
app.include_router(router)

# Create a test client
client = TestClient(app)

# Fixtures for testing
@pytest.fixture
def mock_telemetry_manager():
    """Mock the telemetry manager."""
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.telemetry_manager") as mock:
        yield mock


@pytest.fixture
def mock_verify_endpoint_key():
    """Mock the verify_endpoint_key dependency."""
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.verify_endpoint_key") as mock:
        mock.return_value = None
        yield mock


@pytest.fixture
def mock_verify_jwt():
    """Mock the verify_jwt dependency."""
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.verify_jwt") as mock:
        mock.return_value = UserContext(
            user_id="test-user-id",
            tenant_id="test-tenant-id",
            role="user",
            is_authenticated=True,
        )
        yield mock


# Model validation tests
def test_agui_message_model():
    """Test the AGUIMessage model."""
    # Test valid message
    message = AGUIMessage(role="user", content="Test message")
    assert message.role == "user"
    assert message.content == "Test message"

    # Test message with tool calls
    tool_call = AGUIToolCall(
        id="tool-1",
        type="function",
        function=AGUIToolCallFunction(name="test_function", arguments="{}")
    )
    message = AGUIMessage(
        role="assistant",
        content="",
        tool_calls=[tool_call]
    )
    assert message.role == "assistant"
    assert message.tool_calls[0].id == "tool-1"

    # Test message with tool call ID
    message = AGUIMessage(
        role="tool",
        content="Tool result",
        tool_call_id="tool-1"
    )
    assert message.role == "tool"
    assert message.tool_call_id == "tool-1"


def test_agui_request_model():
    """Test the AGUIRequest model."""
    # Test minimal request
    request = AGUIRequest(
        messages=[AGUIMessage(role="user", content="Test message")]
    )
    assert len(request.messages) == 1
    assert request.messages[0].role == "user"
    assert request.stream is True  # Default value

    # Test full request
    request = AGUIRequest(
        messages=[AGUIMessage(role="user", content="Test message")],
        agent="test-agent",
        threadId="test-thread",
        stream=False,
        model="gpt-4",
        temperature=0.7,
        max_tokens=100,
        tools=[{"type": "function", "function": {"name": "test_function", "parameters": {}}}],
        tool_choice="auto",
        user="test-user",
        shared_state={"key": "value"},
        metadata={"session_id": "123"}
    )
    assert request.agent == "test-agent"
    assert request.threadId == "test-thread"
    assert request.stream is False
    assert request.model == "gpt-4"
    assert request.temperature == 0.7
    assert request.max_tokens == 100
    assert len(request.tools) == 1
    assert request.tool_choice == "auto"
    assert request.user == "test-user"
    assert request.shared_state == {"key": "value"}
    assert request.metadata == {"session_id": "123"}


def test_agui_response_model():
    """Test the AGUIResponse model."""
    # Test minimal response
    response = AGUIResponse(
        messages=[AGUIMessage(role="assistant", content="Test response")],
        threadId="test-thread"
    )
    assert len(response.messages) == 1
    assert response.messages[0].role == "assistant"
    assert response.done is True  # Default value

    # Test full response
    response = AGUIResponse(
        messages=[AGUIMessage(role="assistant", content="Test response")],
        threadId="test-thread",
        done=False,
        customData={"key": "value"}
    )
    assert response.done is False
    assert response.customData == {"key": "value"}


def test_agui_stream_event_model():
    """Test the AGUIStreamEvent model."""
    # Test start event
    event = AGUIStreamEvent(
        type="start",
        threadId="test-thread"
    )
    assert event.type == "start"
    assert event.threadId == "test-thread"

    # Test content event
    event = AGUIStreamEvent(
        type="content",
        content="Test content"
    )
    assert event.type == "content"
    assert event.content == "Test content"

    # Test tool calls event
    tool_call = AGUIToolCall(
        id="tool-1",
        type="function",
        function=AGUIToolCallFunction(name="test_function", arguments="{}")
    )
    event = AGUIStreamEvent(
        type="tool_calls",
        toolCalls=[tool_call]
    )
    assert event.type == "tool_calls"
    assert event.toolCalls[0].id == "tool-1"

    # Test error event
    event = AGUIStreamEvent(
        type="error",
        error="Test error"
    )
    assert event.type == "error"
    assert event.error == "Test error"

    # Test done event
    event = AGUIStreamEvent(
        type="done",
        threadId="test-thread"
    )
    assert event.type == "done"
    assert event.threadId == "test-thread"


# Streaming response tests
def test_streaming_response_format():
    """Test the format of streaming responses."""
    # Mock the streaming generator
    async def mock_streaming_generator(*_):
        # Start event
        start_event = AGUIStreamEvent(type="start", threadId="test-thread")
        yield f"data: {start_event.json()}\n\n"

        # Content events
        content_event = AGUIStreamEvent(type="content", content="Test content")
        yield f"data: {content_event.json()}\n\n"

        # Done event
        done_event = AGUIStreamEvent(type="done", threadId="test-thread")
        yield f"data: {done_event.json()}\n\n"

    with patch("pi_lawyer.api.routes.copilotkit_endpoint.generate_streaming_response", return_value=mock_streaming_generator()):
        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": True,
            },
        )

        assert response.status_code == 200
        assert "text/event-stream" in response.headers["content-type"]

        # Check the streaming response content
        content = response.content.decode("utf-8")

        # Verify events
        events = [json.loads(line.replace("data: ", "")) for line in content.strip().split("\n\n") if line.startswith("data: ")]

        assert len(events) == 3
        assert events[0]["type"] == "start"
        assert events[0]["threadId"] == "test-thread"
        assert events[1]["type"] == "content"
        assert events[1]["content"] == "Test content"
        assert events[2]["type"] == "done"
        assert events[2]["threadId"] == "test-thread"


# Error handling tests
def test_streaming_error_handling(mock_verify_endpoint_key, mock_verify_jwt, mock_telemetry_manager):
    """Test error handling in streaming responses."""
    # Mock the streaming generator to raise an exception
    async def mock_streaming_generator(*_):
        # Start event
        start_event = AGUIStreamEvent(type=AGUIEventType.START, threadId="test-thread")
        yield f"data: {start_event.json()}\n\n"

        # Raise an exception
        raise Exception("Test streaming error")

    with patch("pi_lawyer.api.routes.copilotkit_endpoint.generate_streaming_response", return_value=mock_streaming_generator()):
        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": True,
            },
        )

        assert response.status_code == 200
        assert "text/event-stream" in response.headers["content-type"]

        # The streaming response should still be valid SSE
        content = response.content.decode("utf-8")
        assert "data: " in content

        # Check that telemetry was called
        assert mock_telemetry_manager.start_request.called
        assert mock_telemetry_manager.update_request.called


def test_validation_error_handling(mock_verify_endpoint_key, mock_verify_jwt, mock_telemetry_manager):
    """Test validation error handling in non-streaming responses."""
    # Send a request with invalid data (missing required field)
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret"},
        json={
            "agent": "test-agent",
            # Missing required "messages" field
            "stream": False,
        },
    )

    # Check the response
    assert response.status_code == 400
    assert "error" in response.json()
    assert response.json()["type"] == AGUIErrorType.VALIDATION_ERROR

    # Check that telemetry was called
    assert mock_telemetry_manager.start_request.called
    assert mock_telemetry_manager.complete_request.called


def test_invalid_json_handling(mock_verify_endpoint_key, mock_verify_jwt, mock_telemetry_manager):
    """Test invalid JSON handling."""
    # Send a request with invalid JSON
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret", "Content-Type": "application/json"},
        content="This is not valid JSON",
    )

    # Check the response
    assert response.status_code == 400
    assert "error" in response.json()
    assert response.json()["type"] == AGUIErrorType.INVALID_REQUEST

    # Check that telemetry was called
    assert mock_telemetry_manager.start_request.called
    assert mock_telemetry_manager.complete_request.called


def test_authentication_error_handling(mock_verify_endpoint_key, mock_telemetry_manager):
    """Test authentication error handling."""
    # Mock the verify_jwt dependency to raise an HTTPException
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.verify_jwt") as mock_verify_jwt:
        from fastapi import HTTPException
        mock_verify_jwt.side_effect = HTTPException(status_code=401, detail="Invalid token")

        # Send a request
        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": False,
            },
        )

        # Check the response
        assert response.status_code == 401
        assert "error" in response.json()
        assert response.json()["type"] == AGUIErrorType.AUTHENTICATION_ERROR

        # Check that telemetry was called
        assert mock_telemetry_manager.start_request.called
        assert mock_telemetry_manager.complete_request.called


# Telemetry tests
def test_telemetry_collection(mock_verify_endpoint_key, mock_verify_jwt, mock_telemetry_manager):
    """Test telemetry collection for non-streaming requests."""
    # Mock the generate_response function
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.generate_response") as mock_generate_response:
        mock_generate_response.return_value = {
            "messages": [{"role": "assistant", "content": "Test response"}],
            "done": True,
            "threadId": "test-thread",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30,
            },
        }

        # Send a request
        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": False,
            },
        )

        # Check the response
        assert response.status_code == 200

        # Check that telemetry was called with the right parameters
        assert mock_telemetry_manager.start_request.called
        assert mock_telemetry_manager.update_request.called
        assert mock_telemetry_manager.mark_first_token.called
        assert mock_telemetry_manager.complete_request.called

        # Check that the request ID header is present
        assert "X-Request-ID" in response.headers


# Thread management tests
def test_thread_management(mock_verify_endpoint_key, mock_verify_jwt, mock_telemetry_manager):
    """Test thread management."""
    # Mock the generate_response function
    with patch("pi_lawyer.api.routes.copilotkit_endpoint.generate_response") as mock_generate_response:
        # Return the thread ID from the request
        mock_generate_response.side_effect = lambda agent, messages, thread_id, shared_state, request_id, request: {
            "messages": [{"role": "assistant", "content": f"Thread ID: {thread_id}"}],
            "done": True,
            "threadId": thread_id,
        }

        # Send a request with a specific thread ID
        response1 = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "threadId": "specific-thread-id",
                "stream": False,
            },
        )

        # Check that the thread ID was preserved
        assert response1.status_code == 200
        assert response1.json()["threadId"] == "specific-thread-id"

        # Send a request without a thread ID
        response2 = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": False,
            },
        )

        # Check that a thread ID was generated
        assert response2.status_code == 200
        assert "threadId" in response2.json()
        assert response2.json()["threadId"] is not None
        assert response2.json()["threadId"] != "specific-thread-id"


# Enhanced model validation tests
def test_enhanced_agui_message_validation():
    """Test enhanced validation for AGUIMessage model."""
    # Valid message
    message = AGUIMessage(role="user", content="Test message")
    assert message.role == "user"
    assert message.content == "Test message"

    # Valid tool message
    message = AGUIMessage(role="tool", content="Tool result", tool_call_id="tool-1")
    assert message.role == "tool"
    assert message.tool_call_id == "tool-1"

    # Invalid role
    with pytest.raises(ValueError):
        AGUIMessage(role="invalid_role", content="Test message")

    # Missing tool_call_id for tool role
    with pytest.raises(ValueError):
        AGUIMessage(role="tool", content="Tool result")


def test_enhanced_agui_stream_event_validation():
    """Test enhanced validation for AGUIStreamEvent model."""
    # Valid content event
    event = AGUIStreamEvent(type=AGUIEventType.CONTENT, content="Test content")
    assert event.type == AGUIEventType.CONTENT
    assert event.content == "Test content"

    # Valid tool calls event
    tool_call = AGUIToolCall(
        id="tool-1",
        type="function",
        function=AGUIToolCallFunction(name="test_function", arguments="{}")
    )
    event = AGUIStreamEvent(type=AGUIEventType.TOOL_CALLS, toolCalls=[tool_call])
    assert event.type == AGUIEventType.TOOL_CALLS
    assert event.toolCalls[0].id == "tool-1"

    # Valid error event
    event = AGUIStreamEvent(
        type=AGUIEventType.ERROR,
        error="Test error",
        errorType=AGUIErrorType.INTERNAL_SERVER_ERROR
    )
    assert event.type == AGUIEventType.ERROR
    assert event.error == "Test error"
    assert event.errorType == AGUIErrorType.INTERNAL_SERVER_ERROR

    # Missing required field for content event
    with pytest.raises(ValueError):
        AGUIStreamEvent(type=AGUIEventType.CONTENT)

    # Missing required field for tool calls event
    with pytest.raises(ValueError):
        AGUIStreamEvent(type=AGUIEventType.TOOL_CALLS)

    # Missing required field for error event
    with pytest.raises(ValueError):
        AGUIStreamEvent(type=AGUIEventType.ERROR)
