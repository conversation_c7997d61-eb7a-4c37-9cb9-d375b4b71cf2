"""
Tests for the CopilotKit endpoint with AG-UI protocol support.

This module contains tests for the CopilotKit endpoint implementation.
"""

import json
import os
import sys
from unittest.mock import patch

from fastapi.testclient import TestClient

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from fastapi import FastAPI

from src.pi_lawyer.api.routes.copilotkit_endpoint import router

# Create a test app
app = FastAPI()
app.include_router(router)

# Create a test client
client = TestClient(app)


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/copilotkit/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_options_handler():
    """Test the CORS preflight handler."""
    response = client.options("/copilotkit")
    assert response.status_code == 200


def test_endpoint_key_verification_success(mock_env_endpoint_secret):
    """Test that the endpoint key verification succeeds with a valid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret"},
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code != 403  # Not forbidden


def test_endpoint_key_verification_failure(mock_env_endpoint_secret):
    """Test that the endpoint key verification fails with an invalid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "invalid-secret"},
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code == 403
    assert "Invalid endpoint key" in response.json()["detail"]


def test_endpoint_key_verification_disabled(mock_env_no_endpoint_secret):
    """Test that the endpoint key verification is disabled when no secret is set."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code != 403  # Not forbidden


def test_handle_legacy_request():
    """Test handling a legacy GraphQL request."""
    with patch("src.pi_lawyer.api.routes.copilotkit_endpoint.handle_legacy_request") as mock_handler:
        mock_handler.return_value = {"data": {"generateCopilotResponse": {"messages": []}}}

        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "operationName": "generateCopilotResponse",
                "variables": {"data": {"agent": "test-agent", "messages": []}},
            },
        )

        mock_handler.assert_called_once()
        assert response.status_code == 200


def test_handle_agui_request_non_streaming():
    """Test handling an AG-UI request with non-streaming response."""
    with patch("src.pi_lawyer.api.routes.copilotkit_endpoint.handle_agui_request") as mock_handler:
        mock_response = {
            "messages": [{"role": "assistant", "content": "Test response"}],
            "done": True,
            "threadId": "test-thread",
        }
        mock_handler.return_value = mock_response

        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": False,
            },
        )

        mock_handler.assert_called_once()
        assert response.status_code == 200


def test_handle_agui_request_streaming():
    """Test handling an AG-UI request with streaming response."""
    # This test is more complex because it involves streaming responses
    # We'll need to mock the streaming generator function

    async def mock_streaming_generator(*_):
        yield f"data: {json.dumps({'type': 'start', 'threadId': 'test-thread'})}\n\n"
        yield f"data: {json.dumps({'type': 'content', 'content': 'Test response'})}\n\n"
        yield f"data: {json.dumps({'type': 'done', 'threadId': 'test-thread'})}\n\n"

    with patch("src.pi_lawyer.api.routes.copilotkit_endpoint.generate_streaming_response", return_value=mock_streaming_generator()):
        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": True,
            },
        )

        assert response.status_code == 200
        assert "text/event-stream" in response.headers["content-type"]

        # Check the streaming response content
        content = response.content.decode("utf-8")
        assert "data: " in content
        assert "test-thread" in content


def test_invalid_json():
    """Test handling an invalid JSON request."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret"},
        content="invalid json",
    )
    assert response.status_code == 400
    assert "Invalid JSON" in response.json()["error"]


def test_server_error():
    """Test handling a server error."""
    with patch("src.pi_lawyer.api.routes.copilotkit_endpoint.handle_agui_request") as mock_handler:
        mock_handler.side_effect = Exception("Test error")

        response = client.post(
            "/copilotkit",
            headers={"X-CPK-Endpoint-Key": "test-secret"},
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
            },
        )

        assert response.status_code == 500
        assert "Test error" in response.json()["error"]
