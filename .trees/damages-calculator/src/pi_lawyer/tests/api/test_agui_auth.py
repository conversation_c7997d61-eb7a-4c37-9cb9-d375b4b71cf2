"""
Tests for the AG-UI protocol authentication integration.

This module contains tests for the JWT authentication integration with the AG-UI protocol.
"""

import os
import sys
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from jose import jwt

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from fastapi import FastAP<PERSON>

from pi_lawyer.api.routes.copilotkit_endpoint import router
from pi_lawyer.models.auth import UserContext

# Create a test app
app = FastAPI()
app.include_router(router)

# Create a test client
client = TestClient(app)


# JWT authentication tests
@pytest.fixture
def mock_jwt_secret():
    """Mock the JWT secret environment variable."""
    with patch.dict(os.environ, {"SUPABASE_JWT_SECRET": "test-jwt-secret"}):
        yield


@pytest.fixture
def valid_jwt_token(mock_jwt_secret):
    """Create a valid JWT token for testing."""
    payload = {
        "sub": "test-user-id",
        "email": "<EMAIL>",
        "role": "user",
        "tenant_id": "test-tenant-id",
        "permissions": ["read", "write"],
    }
    token = jwt.encode(payload, "test-jwt-secret", algorithm="HS256")
    return token


def test_jwt_verification_success(mock_jwt_secret, valid_jwt_token):
    """Test that JWT verification succeeds with a valid token."""
    # Mock the verify_jwt function to return a user context
    user_context = UserContext(
        user_id="test-user-id",
        email="<EMAIL>",
        role="user",
        tenant_id="test-tenant-id",
        permissions=["read", "write"],
        is_authenticated=True,
    )

    with patch("pi_lawyer.middleware.jwt_middleware.verify_jwt", return_value=user_context):
        response = client.post(
            "/copilotkit",
            headers={
                "X-CPK-Endpoint-Key": "test-secret",
                "Authorization": f"Bearer {valid_jwt_token}",
            },
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
            },
        )

        assert response.status_code == 200


def test_jwt_verification_failure(mock_jwt_secret):
    """Test that JWT verification fails with an invalid token."""
    # Mock the verify_jwt function to raise an HTTPException
    with patch("pi_lawyer.middleware.jwt_middleware.verify_jwt", side_effect=Exception("Invalid token")):
        response = client.post(
            "/copilotkit",
            headers={
                "X-CPK-Endpoint-Key": "test-secret",
                "Authorization": "Bearer invalid-token",
            },
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
            },
        )

        # The endpoint should still work because JWT verification is optional
        assert response.status_code == 200


def test_jwt_verification_missing_token(mock_jwt_secret):
    """Test that JWT verification handles missing tokens gracefully."""
    # Mock the verify_jwt function to return None
    with patch("pi_lawyer.middleware.jwt_middleware.verify_jwt", return_value=None):
        response = client.post(
            "/copilotkit",
            headers={
                "X-CPK-Endpoint-Key": "test-secret",
            },
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
            },
        )

        # The endpoint should still work because JWT verification is optional
        assert response.status_code == 200


def test_user_context_in_telemetry(mock_jwt_secret, valid_jwt_token):
    """Test that user context is included in telemetry."""
    # Mock the verify_jwt function to return a user context
    user_context = UserContext(
        user_id="test-user-id",
        email="<EMAIL>",
        role="user",
        tenant_id="test-tenant-id",
        permissions=["read", "write"],
        is_authenticated=True,
    )

    # Mock the logger to capture telemetry
    mock_logger = MagicMock()

    with patch("pi_lawyer.middleware.jwt_middleware.verify_jwt", return_value=user_context), \
         patch("pi_lawyer.api.routes.copilotkit_endpoint.logger", mock_logger):
        response = client.post(
            "/copilotkit",
            headers={
                "X-CPK-Endpoint-Key": "test-secret",
                "Authorization": f"Bearer {valid_jwt_token}",
            },
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": False,
            },
        )

        assert response.status_code == 200

        # Check that the logger was called with telemetry containing user context
        for call_args in mock_logger.info.call_args_list:
            log_message = call_args[0][0]
            if "Request telemetry" in log_message:
                assert "user_id" in log_message
                assert "tenant_id" in log_message
                assert "role" in log_message
                assert "authenticated" in log_message
                assert "test-user-id" in log_message
                assert "test-tenant-id" in log_message
                break
        else:
            pytest.fail("No telemetry log message found")


def test_auth_info_in_streaming_response(mock_jwt_secret, valid_jwt_token):
    """Test that authentication info is passed to streaming response generator."""
    # Mock the verify_jwt function to return a user context
    user_context = UserContext(
        user_id="test-user-id",
        email="<EMAIL>",
        role="user",
        tenant_id="test-tenant-id",
        permissions=["read", "write"],
        is_authenticated=True,
    )

    # Mock the streaming generator
    mock_generator = AsyncMock()

    with patch("pi_lawyer.middleware.jwt_middleware.verify_jwt", return_value=user_context), \
         patch("pi_lawyer.api.routes.copilotkit_endpoint.generate_streaming_response", mock_generator):
        response = client.post(
            "/copilotkit",
            headers={
                "X-CPK-Endpoint-Key": "test-secret",
                "Authorization": f"Bearer {valid_jwt_token}",
            },
            json={
                "agent": "test-agent",
                "messages": [{"role": "user", "content": "Test message"}],
                "stream": True,
            },
        )

        # Check that generate_streaming_response was called with the request object
        mock_generator.assert_called_once()
        assert mock_generator.call_args[0][4]  # request_id
        assert mock_generator.call_args[0][5]  # request object
