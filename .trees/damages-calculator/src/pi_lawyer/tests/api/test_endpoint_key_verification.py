"""
Tests for the CopilotKit endpoint key verification.

This module contains tests for the CopilotKit endpoint key verification
middleware and dependency function.
"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi import Depends, FastAPI, Request
from fastapi.testclient import TestClient

from pi_lawyer.api.routes.copilotkit_endpoint import verify_endpoint_key

# Create a test app with a route that uses the verify_endpoint_key dependency
app = FastAPI()

@app.get("/test-auth")
async def test_auth_route(auth: None = Depends(verify_endpoint_key)):
    """Test route that uses the verify_endpoint_key dependency."""
    return {"authenticated": True}

@app.get("/test-state")
async def test_state_route(request: Request):
    """Test route that returns the request state."""
    return {
        "cpk_authenticated": hasattr(request.state, "cpk_authenticated") and request.state.cpk_authenticated,
        "cpk_auth_disabled": hasattr(request.state, "cpk_auth_disabled") and request.state.cpk_auth_disabled
    }

# Create a test client
client = TestClient(app)


@pytest.fixture
def mock_settings():
    """Mock the settings module."""
    with patch("pi_lawyer.config.settings.settings") as mock_settings:
        # Create a mock CopilotKitSettings
        mock_copilotkit = MagicMock()
        mock_copilotkit.endpoint_secret = "test-secret-123456789"

        # Assign the mock CopilotKitSettings to the mock settings
        mock_settings.copilotkit = mock_copilotkit
        mock_settings.debug = False

        yield mock_settings


@pytest.fixture
def mock_settings_debug():
    """Mock the settings module with debug mode enabled."""
    with patch("pi_lawyer.config.settings.settings") as mock_settings:
        # Create a mock CopilotKitSettings
        mock_copilotkit = MagicMock()
        mock_copilotkit.endpoint_secret = "test-endpoint-secret-123456789"

        # Assign the mock CopilotKitSettings to the mock settings
        mock_settings.copilotkit = mock_copilotkit
        mock_settings.debug = True

        yield mock_settings


@pytest.fixture
def mock_settings_no_secret():
    """Mock the settings module with no endpoint secret."""
    with patch("pi_lawyer.config.settings.settings") as mock_settings:
        # Create a mock CopilotKitSettings
        mock_copilotkit = MagicMock()
        mock_copilotkit.endpoint_secret = ""

        # Assign the mock CopilotKitSettings to the mock settings
        mock_settings.copilotkit = mock_copilotkit
        mock_settings.debug = False

        yield mock_settings


@pytest.fixture
def mock_request_state_authenticated():
    """Mock the request state with cpk_authenticated=True."""
    with patch("fastapi.Request") as mock_request:
        # Create a mock request state
        mock_request.state.cpk_authenticated = True

        yield mock_request


@pytest.fixture
def mock_request_state_auth_disabled():
    """Mock the request state with cpk_auth_disabled=True."""
    with patch("fastapi.Request") as mock_request:
        # Create a mock request state
        mock_request.state.cpk_auth_disabled = True

        yield mock_request


def test_verify_endpoint_key_valid(mock_settings):
    """Test that the endpoint key verification succeeds with a valid key."""
    response = client.get(
        "/test-auth",
        headers={"X-CPK-Endpoint-Key": "test-secret-123456789"}
    )
    assert response.status_code == 200
    assert response.json() == {"authenticated": True}


def test_verify_endpoint_key_invalid(mock_settings):
    """Test that the endpoint key verification fails with an invalid key."""
    response = client.get(
        "/test-auth",
        headers={"X-CPK-Endpoint-Key": "invalid-secret"}
    )
    assert response.status_code == 403
    assert response.json() == {"detail": "Invalid CopilotKit endpoint key"}


def test_verify_endpoint_key_missing(mock_settings):
    """Test that the endpoint key verification fails with a missing key."""
    response = client.get("/test-auth")
    assert response.status_code == 403
    assert response.json() == {"detail": "CopilotKit endpoint key required"}


def test_verify_endpoint_key_debug_mode(mock_settings_debug):
    """Test that the endpoint key verification is disabled in debug mode with default secret."""
    response = client.get("/test-auth")
    assert response.status_code == 200
    assert response.json() == {"authenticated": True}


def test_verify_endpoint_key_no_secret(mock_settings_no_secret):
    """Test that the endpoint key verification is disabled when no secret is set."""
    response = client.get("/test-auth")
    assert response.status_code == 200
    assert response.json() == {"authenticated": True}


def test_verify_endpoint_key_already_authenticated():
    """Test that the endpoint key verification is skipped if already authenticated."""
    # This test uses a custom middleware to set the request state
    app_with_middleware = FastAPI()

    @app_with_middleware.middleware("http")
    async def set_authenticated_middleware(request: Request, call_next):
        """Middleware to set the request state."""
        request.state.cpk_authenticated = True
        return await call_next(request)

    @app_with_middleware.get("/test-auth")
    async def test_auth_route(auth: None = Depends(verify_endpoint_key)):
        """Test route that uses the verify_endpoint_key dependency."""
        return {"authenticated": True}

    client_with_middleware = TestClient(app_with_middleware)

    response = client_with_middleware.get("/test-auth")
    assert response.status_code == 200
    assert response.json() == {"authenticated": True}


def test_verify_endpoint_key_auth_disabled():
    """Test that the endpoint key verification is skipped if authentication is disabled."""
    # This test uses a custom middleware to set the request state
    app_with_middleware = FastAPI()

    @app_with_middleware.middleware("http")
    async def set_auth_disabled_middleware(request: Request, call_next):
        """Middleware to set the request state."""
        request.state.cpk_auth_disabled = True
        return await call_next(request)

    @app_with_middleware.get("/test-auth")
    async def test_auth_route(auth: None = Depends(verify_endpoint_key)):
        """Test route that uses the verify_endpoint_key dependency."""
        return {"authenticated": True}

    client_with_middleware = TestClient(app_with_middleware)

    response = client_with_middleware.get("/test-auth")
    assert response.status_code == 200
    assert response.json() == {"authenticated": True}


def test_constant_time_comparison():
    """Test that the endpoint key verification uses constant-time comparison."""
    with patch("secrets.compare_digest") as mock_compare_digest:
        mock_compare_digest.return_value = False

        response = client.get(
            "/test-auth",
            headers={"X-CPK-Endpoint-Key": "invalid-secret"}
        )

        mock_compare_digest.assert_called_once()
        assert response.status_code == 403
