"""
Test script for AG-UI protocol error handling.

This script tests the error handling functionality for the AG-UI protocol.
"""

import json
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, Optional


class AGUIErrorType(str, Enum):
    """AG-UI Error Types."""
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    INVALID_REQUEST = "invalid_request"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    AGENT_ERROR = "agent_error"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class AGUIError(Exception):
    """AG-UI Error class."""

    def __init__(
        self,
        message: str,
        error_type: AGUIErrorType = AGUIErrorType.UNKNOWN_ERROR,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize an AG-UI Error.

        Args:
            message: The error message
            error_type: The type of error
            status_code: The HTTP status code
            details: Additional error details
        """
        self.message = message
        self.error_type = error_type
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

    def to_dict(self, request_id: str, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert the error to a dictionary.

        Args:
            request_id: The request ID
            thread_id: The thread ID (optional)

        Returns:
            A dictionary representation of the error
        """
        error_dict = {
            "error": self.message,
            "type": self.error_type,
            "status": self.status_code,
            "requestId": request_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        if thread_id:
            error_dict["threadId"] = thread_id

        if self.details:
            error_dict["details"] = self.details

        return error_dict

    def to_json(self, request_id: str, thread_id: Optional[str] = None) -> str:
        """
        Convert the error to a JSON string.

        Args:
            request_id: The request ID
            thread_id: The thread ID (optional)

        Returns:
            A JSON string representation of the error
        """
        return json.dumps(self.to_dict(request_id, thread_id))

    def to_stream_event(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert the error to a stream event.

        Args:
            thread_id: The thread ID (optional)

        Returns:
            A dictionary representation of the error as a stream event
        """
        event = {
            "type": "error",
            "error": self.message,
            "errorType": self.error_type
        }

        if thread_id:
            event["threadId"] = thread_id

        if self.details:
            event["details"] = self.details

        return event

    def to_stream_event_json(self, thread_id: Optional[str] = None) -> str:
        """
        Convert the error to a stream event JSON string.

        Args:
            thread_id: The thread ID (optional)

        Returns:
            A JSON string representation of the error as a stream event
        """
        return json.dumps(self.to_stream_event(thread_id))


def test_agui_error():
    """Test the AGUIError class."""
    print("Testing AGUIError class...")

    # Test basic error
    error = AGUIError(
        message="Test error",
        error_type=AGUIErrorType.VALIDATION_ERROR,
        status_code=400
    )

    assert error.message == "Test error"
    assert error.error_type == AGUIErrorType.VALIDATION_ERROR
    assert error.status_code == 400

    # Test error to dict
    error_dict = error.to_dict(request_id="req-123")
    assert error_dict["error"] == "Test error"
    assert error_dict["type"] == AGUIErrorType.VALIDATION_ERROR
    assert error_dict["status"] == 400
    assert error_dict["requestId"] == "req-123"
    assert "timestamp" in error_dict

    # Test error to dict with thread ID
    error_dict = error.to_dict(request_id="req-123", thread_id="thread-456")
    assert error_dict["threadId"] == "thread-456"

    # Test error to dict with details
    error = AGUIError(
        message="Test error with details",
        error_type=AGUIErrorType.INTERNAL_SERVER_ERROR,
        status_code=500,
        details={"source": "test", "code": "E123"}
    )

    error_dict = error.to_dict(request_id="req-123")
    assert error_dict["details"]["source"] == "test"
    assert error_dict["details"]["code"] == "E123"

    # Test error to stream event
    stream_event = error.to_stream_event()
    assert stream_event["type"] == "error"
    assert stream_event["error"] == "Test error with details"
    assert stream_event["errorType"] == AGUIErrorType.INTERNAL_SERVER_ERROR
    assert stream_event["details"]["source"] == "test"

    # Test error to stream event with thread ID
    stream_event = error.to_stream_event(thread_id="thread-456")
    assert stream_event["threadId"] == "thread-456"

    print("✅ AGUIError tests passed")


if __name__ == "__main__":
    test_agui_error()
