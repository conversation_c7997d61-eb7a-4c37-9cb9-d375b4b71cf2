"""
Test configuration for the endpoint key verification tests.

This module contains fixtures and configuration for the endpoint key verification tests.
"""

from unittest.mock import MagicMock, patch

import pytest


# Mock the VoyageAIEmbeddings class
@pytest.fixture(autouse=True)
def mock_voyage_embeddings():
    """Mock the VoyageAIEmbeddings class."""
    with patch("pi_lawyer.utils.voyage_embeddings.VoyageAIEmbeddings") as mock_embeddings:
        # Create a mock embeddings instance
        mock_instance = MagicMock()
        mock_instance.embed_documents.return_value = [[0.1, 0.2, 0.3]]
        mock_instance.embed_query.return_value = [0.1, 0.2, 0.3]
        
        # Configure the mock class to return the mock instance
        mock_embeddings.return_value = mock_instance
        
        yield mock_embeddings

# Mock the TaskEmbeddingService
@pytest.fixture(autouse=True)
def mock_task_embedding_service():
    """Mock the TaskEmbeddingService."""
    with patch("pi_lawyer.services.task_embedding_service.TaskEmbeddingService") as mock_service:
        # Create a mock service instance
        mock_instance = MagicMock()
        mock_instance.embed_task.return_value = [0.1, 0.2, 0.3]
        mock_instance.embed_query.return_value = [0.1, 0.2, 0.3]
        
        # Configure the mock class to return the mock instance
        mock_service.return_value = mock_instance
        
        yield mock_service

# Mock the task_embedding_service singleton
@pytest.fixture(autouse=True)
def mock_task_embedding_service_singleton():
    """Mock the task_embedding_service singleton."""
    with patch("pi_lawyer.services.task_embedding_service.task_embedding_service") as mock_singleton:
        # Configure the mock singleton
        mock_singleton.embed_task.return_value = [0.1, 0.2, 0.3]
        mock_singleton.embed_query.return_value = [0.1, 0.2, 0.3]
        
        yield mock_singleton

# Mock environment variables
@pytest.fixture(autouse=True)
def mock_environment_variables(monkeypatch):
    """Mock environment variables for testing."""
    monkeypatch.setenv("VOYAGE_API_KEY", "test-voyage-api-key")
    monkeypatch.setenv("PINECONE_API_KEY", "test-pinecone-api-key")
    monkeypatch.setenv("PINECONE_ENVIRONMENT", "test-environment")
    monkeypatch.setenv("PINECONE_INDEX_NAME", "test-index")
    monkeypatch.setenv("OPENAI_API_KEY", "test-openai-api-key")
    monkeypatch.setenv("SUPABASE_URL", "https://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-supabase-key")
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-jwt-secret")
