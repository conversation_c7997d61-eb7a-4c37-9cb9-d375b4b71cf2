"""
Test configuration for the CopilotKit endpoint tests.

This module contains fixtures and configuration for the CopilotKit endpoint tests.
"""

import sys
from unittest.mock import MagicMock

import pytest

# Disable the global fixtures that might interfere with the AG-UI protocol tests
sys.modules["src.pi_lawyer.agents.intake_agent"] = MagicMock()
sys.modules["src.pi_lawyer.agents.document_agent"] = MagicMock()
sys.modules["src.pi_lawyer.agents.research_agent"] = MagicMock()
sys.modules["src.pi_lawyer.agents.event_agent"] = MagicMock()
sys.modules["src.pi_lawyer.agents.deadline_agent"] = MagicMock()


@pytest.fixture(autouse=True)
def mock_environment_variables(monkeypatch):
    """
    Mock environment variables for testing.

    This fixture is automatically used in all tests.
    """
    # Set environment variables for testing
    monkeypatch.setenv("VOYAGE_API_KEY", "test-voyage-api-key")
    monkeypatch.setenv("PINECONE_API_KEY", "test-pinecone-api-key")
    monkeypatch.setenv("PINECONE_ENVIRONMENT", "test-environment")
    monkeypatch.setenv("PINECONE_INDEX_NAME", "test-index")
    monkeypatch.setenv("OPENAI_API_KEY", "test-openai-api-key")
    monkeypatch.setenv("SUPABASE_URL", "https://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-supabase-key")
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-jwt-secret")
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-endpoint-secret")


@pytest.fixture
def mock_env_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable."""
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-secret")


@pytest.fixture
def mock_env_no_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable to be empty."""
    monkeypatch.delenv("CPK_ENDPOINT_SECRET", raising=False)
