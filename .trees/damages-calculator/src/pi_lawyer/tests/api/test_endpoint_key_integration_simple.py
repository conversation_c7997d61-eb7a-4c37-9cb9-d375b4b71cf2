"""
Simple integration tests for the CopilotKit endpoint key verification.

This module contains simple integration tests for the CopilotKit endpoint key verification
that don't depend on the existing codebase.
"""

import os
import secrets
from unittest.mock import MagicMock

import pytest
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.testclient import TestClient

# Create a simple FastAPI app with middleware for testing
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware for endpoint key verification
@app.middleware("http")
async def verify_endpoint_key_middleware(request: Request, call_next):
    """
    Middleware to verify the CopilotKit endpoint key for secure access.

    Args:
        request: The FastAPI request object
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler
    """
    # Only check auth for /copilotkit path, but exclude health check and OPTIONS requests
    if (request.url.path.startswith("/copilotkit") and
        not request.url.path.endswith("/health") and
        request.method != "OPTIONS"):

        # Get the endpoint secret from environment variables
        endpoint_secret = os.environ.get("CPK_ENDPOINT_SECRET", "")

        # If no secret is set or in development mode with default secret, disable auth
        if not endpoint_secret or (endpoint_secret == "test-endpoint-secret-123456789" and os.environ.get("DEBUG") == "true"):
            request.state.cpk_auth_disabled = True
        else:
            # Check the header for the endpoint key
            auth_header = request.headers.get("X-CPK-Endpoint-Key")

            if not auth_header:
                return JSONResponse(
                    status_code=403,
                    content={"detail": "CopilotKit endpoint key required"}
                )

            # Use constant-time comparison to prevent timing attacks
            if not secrets.compare_digest(auth_header, endpoint_secret):
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Invalid CopilotKit endpoint key"}
                )

            # Store authentication status in request state
            request.state.cpk_authenticated = True

    # Proceed with the request
    response = await call_next(request)
    return response


# Add routes for testing
@app.post("/copilotkit")
async def copilotkit_endpoint(request: Request):
    """CopilotKit endpoint for testing."""
    return {"status": "ok"}

@app.get("/copilotkit/health")
async def copilotkit_health():
    """Health check endpoint."""
    return {"status": "healthy"}

@app.options("/copilotkit")
async def copilotkit_options():
    """Options endpoint for CORS preflight requests."""
    return {}

@app.get("/api/health")
async def api_health():
    """API health check endpoint."""
    return {"status": "ok"}


# Create a test client
client = TestClient(app)


@pytest.fixture
def mock_env_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable."""
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-secret-123456789")


@pytest.fixture
def mock_env_no_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable to be empty."""
    monkeypatch.delenv("CPK_ENDPOINT_SECRET", raising=False)


@pytest.fixture
def mock_env_debug_mode(monkeypatch):
    """Mock the DEBUG environment variable to enable debug mode."""
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-endpoint-secret-123456789")


def test_copilotkit_endpoint_valid_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is accessible with a valid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret-123456789"},
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_copilotkit_endpoint_invalid_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is not accessible with an invalid key."""
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "invalid-secret"},
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 403
    assert "Invalid CopilotKit endpoint key" in response.json()["detail"]


def test_copilotkit_endpoint_missing_key(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is not accessible with a missing key."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 403
    assert "CopilotKit endpoint key required" in response.json()["detail"]


def test_copilotkit_endpoint_no_secret(mock_env_no_endpoint_secret):
    """Test that the CopilotKit endpoint is accessible when no secret is set."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_copilotkit_endpoint_debug_mode(mock_env_debug_mode):
    """Test that the CopilotKit endpoint is accessible in debug mode with default secret."""
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []}
    )
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_copilotkit_health_endpoint():
    """Test that the CopilotKit health endpoint is accessible without authentication."""
    response = client.get("/copilotkit/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}


def test_copilotkit_options_endpoint():
    """Test that the CopilotKit options endpoint is accessible without authentication."""
    response = client.options("/copilotkit")
    assert response.status_code == 200


def test_non_copilotkit_endpoint():
    """Test that non-CopilotKit endpoints are not affected by the middleware."""
    response = client.get("/api/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


# Async tests for the integration tests
@pytest.mark.asyncio
async def test_copilotkit_endpoint_valid_key_async(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is accessible with a valid key asynchronously."""
    # Create a mock request
    mock_request = MagicMock()
    mock_request.headers = {"X-CPK-Endpoint-Key": "test-secret-123456789"}
    mock_request.url.path = "/copilotkit"

    # Create a mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "ok"})

    # Test the middleware directly
    try:
        # Call the middleware function
        response = await verify_endpoint_key_middleware(mock_request, mock_call_next)
        # If no exception is raised, the test passes
        assert response.status_code == 200
        assert response.body == b'{"status":"ok"}'
    except HTTPException:
        # If an exception is raised, the test fails
        assert False, "Middleware raised an exception with valid key"


@pytest.mark.asyncio
async def test_copilotkit_endpoint_invalid_key_async(mock_env_endpoint_secret):
    """Test that the CopilotKit endpoint is not accessible with an invalid key asynchronously."""
    # Create a mock request
    mock_request = MagicMock()
    mock_request.headers = {"X-CPK-Endpoint-Key": "invalid-secret"}
    mock_request.url.path = "/copilotkit"

    # Create a mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "ok"})

    # Call the middleware function
    response = await verify_endpoint_key_middleware(mock_request, mock_call_next)

    # The middleware should return a 403 response, not raise an exception
    assert response.status_code == 403
    assert "Invalid CopilotKit endpoint key" in response.body.decode('utf-8')


@pytest.mark.asyncio
async def test_copilotkit_health_endpoint_async():
    """Test that the CopilotKit health endpoint is accessible without authentication asynchronously."""
    # Create a mock request
    mock_request = MagicMock()
    mock_request.url.path = "/copilotkit/health"

    # Create a mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "healthy"})

    # Test the middleware directly
    try:
        # Call the middleware function
        response = await verify_endpoint_key_middleware(mock_request, mock_call_next)
        # If no exception is raised, the test passes
        assert response.status_code == 200
        assert response.body == b'{"status":"healthy"}'
    except HTTPException:
        # If an exception is raised, the test fails
        assert False, "Middleware raised an exception for health endpoint"
