"""State management for the PI Lawyer AI system."""
from typing import Any, Dict, List, Optional

from langgraph.graph import MessagesState
from pydantic import BaseModel


class ClientInfo(BaseModel):
    """Client information model."""

    name: str
    contact: str
    injury_description: str
    incident_date: str
    opposing_parties: List[str]
    medical_providers: List[str]


class CaseInfo(BaseModel):
    """Case information model."""

    case_id: str
    client_id: str
    case_type: str
    status: str
    deadlines: List[Dict[str, Any]]
    documents: List[Dict[str, Any]]


class PILawyerState(MessagesState):
    """State management for the PI Lawyer system."""

    client: Optional[ClientInfo] = None
    case: Optional[CaseInfo] = None
    current_task: str
    task_status: str
    error: Optional[str] = None
