"""
Authentication utilities and models for the PI Lawyer API
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import Request
from pydantic import BaseModel, Field


class JWTClaims(BaseModel):
    """JWT claims extracted from a Supabase JWT token."""

    sub: str = Field(..., description="Subject (user ID)")
    email: Optional[str] = Field(None, description="User email")
    role: str = Field(..., description="User role")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    exp: datetime = Field(..., description="Expiration time")
    iat: datetime = Field(..., description="Issued at time")

    # Additional Supabase claims
    aud: Optional[str] = Field(None, description="Audience")
    iss: Optional[str] = Field(None, description="Issuer")

    # Custom claims
    permissions: Optional[List[str]] = Field(None, description="User permissions")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class UserContext(BaseModel):
    """User context extracted from JWT token."""

    user_id: str = Field(..., description="User ID")
    email: Optional[str] = Field(None, description="User email")
    role: str = Field(..., description="User role")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    is_authenticated: bool = Field(True, description="Whether the user is authenticated")


def get_auth_data(request: Request) -> Dict[str, Any]:
    """
    Extract authentication data from the request

    This function extracts user_id and tenant_id from JWT claims
    in the request headers or cookies. In development or test environments,
    it may use defaults or header values.

    Returns:
        Dict with user_id, tenant_id, and role
    """
    # First try to get from headers (useful for testing and development)
    user_id = request.headers.get("x-user-id")
    tenant_id = request.headers.get("x-tenant-id")
    role = request.headers.get("x-role", "user")

    # If not in headers, try to get from request state (set by auth middleware)
    if not user_id and hasattr(request.state, "user_id"):
        user_id = request.state.user_id

    if not tenant_id and hasattr(request.state, "tenant_id"):
        tenant_id = request.state.tenant_id

    if not role and hasattr(request.state, "role"):
        role = request.state.role

    # Fallback for development
    if not user_id:
        user_id = "00000000-0000-0000-0000-000000000000"

    if not tenant_id:
        tenant_id = "00000000-0000-0000-0000-000000000000"

    return {"user_id": user_id, "tenant_id": tenant_id, "role": role}
