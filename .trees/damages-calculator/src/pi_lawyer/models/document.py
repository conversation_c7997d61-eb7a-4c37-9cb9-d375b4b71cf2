"""
Document models for the PI Lawyer AI platform.

This module defines data structures related to document processing,
analysis, and chunking for vector embeddings.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional


@dataclass
class DocumentChunk:
    """
    Represents a chunk of text from a document that will be embedded and stored
    in the vector database.
    """

    content: str
    document_id: str
    chunk_num: int
    chunk_count: int
    metadata: Dict[str, Any]
    page_num: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the chunk to a dictionary for storage or serialization.
        """
        return {
            "content": self.content,
            "document_id": self.document_id,
            "chunk_num": self.chunk_num,
            "chunk_count": self.chunk_count,
            "page_num": self.page_num,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DocumentChunk":
        """
        Create a DocumentChunk instance from a dictionary.
        """
        return cls(
            content=data.get("content", ""),
            document_id=data.get("document_id", ""),
            chunk_num=data.get("chunk_num", 0),
            chunk_count=data.get("chunk_count", 0),
            page_num=data.get("page_num"),
            metadata=data.get("metadata", {}),
        )


@dataclass
class DocumentEmbedding:
    """
    Represents an embedding of a document chunk stored in the vector database.
    """

    vector_id: str
    document_id: str
    tenant_id: str
    chunk_num: int
    pool_namespace: str
    embedding_model: str
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the embedding to a dictionary for storage or serialization.
        """
        return {
            "vector_id": self.vector_id,
            "document_id": self.document_id,
            "tenant_id": self.tenant_id,
            "chunk_num": self.chunk_num,
            "pool_namespace": self.pool_namespace,
            "embedding_model": self.embedding_model,
            "created_at": self.created_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DocumentEmbedding":
        """
        Create a DocumentEmbedding instance from a dictionary.
        """
        return cls(
            vector_id=data.get("vector_id", ""),
            document_id=data.get("document_id", ""),
            tenant_id=data.get("tenant_id", ""),
            chunk_num=data.get("chunk_num", 0),
            pool_namespace=data.get("pool_namespace", ""),
            embedding_model=data.get("embedding_model", ""),
            created_at=datetime.fromisoformat(data.get("created_at"))
            if isinstance(data.get("created_at"), str)
            else data.get("created_at", datetime.utcnow()),
        )


@dataclass
class DocumentProcessingJob:
    """
    Represents a document processing job including embedding generation.
    """

    job_id: str
    document_id: str
    tenant_id: str
    status: str  # "queued", "processing", "completed", "failed"
    priority: str  # "high", "medium", "low"
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the job to a dictionary for storage or serialization.
        """
        return {
            "job_id": self.job_id,
            "document_id": self.document_id,
            "tenant_id": self.tenant_id,
            "status": self.status,
            "priority": self.priority,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "error_message": self.error_message,
            "result": self.result,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DocumentProcessingJob":
        """
        Create a DocumentProcessingJob instance from a dictionary.
        """
        return cls(
            job_id=data.get("job_id", ""),
            document_id=data.get("document_id", ""),
            tenant_id=data.get("tenant_id", ""),
            status=data.get("status", "queued"),
            priority=data.get("priority", "medium"),
            created_at=datetime.fromisoformat(data.get("created_at"))
            if isinstance(data.get("created_at"), str)
            else data.get("created_at", datetime.utcnow()),
            updated_at=datetime.fromisoformat(data.get("updated_at"))
            if isinstance(data.get("updated_at"), str)
            else data.get("updated_at", datetime.utcnow()),
            error_message=data.get("error_message"),
            result=data.get("result"),
        )
