"""Models for the legal deadline extraction service."""
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class LegalDeadline(BaseModel):
    """Structured representation of a legal deadline."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    description: str = Field(..., description="Purpose of the deadline")
    date: Optional[str] = Field(None, description="ISO formatted date if absolute")
    relative_terms: Optional[str] = Field(
        None, description="Relative time specification"
    )
    jurisdiction: str = Field(..., description="Legal jurisdiction")
    source: str = Field(..., description="Exact document location")
    priority: str = Field(..., description="Priority level")
    legal_basis: str = Field(..., description="Statute or rule supporting deadline")
    consequences: List[str] = Field(
        ..., description="Potential repercussions of missing"
    )
    tenant_id: Optional[str] = Field(
        None, description="Tenant ID for multi-tenant isolation"
    )
    document_id: Optional[str] = Field(None, description="Source document ID")
    case_id: Optional[str] = Field(None, description="Associated case ID")

    # Human validation fields
    validation_status: str = Field(
        default="pending", description="Validation status: pending, validated, rejected"
    )
    validated_by: Optional[str] = Field(
        None, description="User ID who validated the deadline"
    )
    validated_at: Optional[datetime] = Field(
        None, description="Timestamp when validation occurred"
    )
    validation_note: Optional[str] = Field(
        None, description="Note provided during validation"
    )
    auto_extracted: bool = Field(
        default=True, description="Whether the deadline was extracted automatically"
    )

    @validator("priority")
    def validate_priority(cls, v):
        allowed = ["critical", "high", "medium", "low"]
        if v.lower() not in allowed:
            raise ValueError(f"Priority must be one of: {', '.join(allowed)}")
        return v.lower()

    @validator("date")
    def validate_date(cls, v):
        if v is not None:
            try:
                datetime.fromisoformat(v)
            except ValueError:
                raise ValueError("Date must be in ISO format (YYYY-MM-DD)")
        return v


class DeadlineExtractionState(BaseModel):
    """State for the deadline extraction LangGraph workflow."""

    document_text: str
    document_metadata: Dict[str, Any]
    raw_llm_output: Optional[List[Dict[str, Any]]] = None
    parsed_deadlines: List[LegalDeadline] = []
    validation_errors: List[str] = []
    final_output: Optional[Dict] = None
    tenant_id: Optional[str] = None
    jurisdiction_rules: Optional[Dict[str, Any]] = None


class JurisdictionRule(BaseModel):
    """Model for jurisdiction-specific deadline rules."""

    jurisdiction: str
    rule_id: str = Field(..., description="Identifier for the rule (e.g., 'TRCP 99')")
    name: str = Field(..., description="Human-readable name for the rule")
    description: str = Field(..., description="Description of the rule")
    max_days: Optional[int] = Field(None, description="Maximum days if applicable")
    min_days: Optional[int] = Field(None, description="Minimum days if applicable")
    exclusions: Optional[List[str]] = Field(
        None, description="Days to exclude (e.g., weekends)"
    )
    holidays: Optional[List[str]] = Field(None, description="Holidays to consider")
    citations: Optional[List[str]] = Field(None, description="Legal citations")
