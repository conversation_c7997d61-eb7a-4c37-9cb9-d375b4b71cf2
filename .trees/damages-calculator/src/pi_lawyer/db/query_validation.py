"""
Database query validation utilities.

This module provides utilities to validate database queries and parameters
to prevent SQL injection and ensure query safety.
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

logger = logging.getLogger(__name__)


class QueryValidationError(Exception):
    """Exception raised when query validation fails."""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(message)
        self.field = field
        self.value = value


class QueryValidator:
    """Validator for database queries and parameters."""
    
    # Allowed table names (whitelist approach)
    ALLOWED_TENANT_TABLES = {
        'matters', 'clients', 'documents', 'deadlines', 'tasks', 'notes',
        'contacts', 'calendar_events', 'assignments', 'case_documents',
        'intake_forms', 'billing_entries', 'time_entries'
    }
    
    ALLOWED_PUBLIC_TABLES = {
        'subscription_plans', 'subscription_addons', 'features',
        'practice_areas', 'work_types', 'system_settings'
    }
    
    # Allowed column patterns
    ALLOWED_COLUMN_PATTERN = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$')
    
    # Dangerous SQL patterns to detect
    DANGEROUS_PATTERNS = [
        r'\b(DROP|DELETE|TRUNCATE|ALTER|CREATE|EXEC|EXECUTE)\b',
        r'(--|#|/\*|\*/)',
        r'\b(UNION|UNION\s+ALL)\b',
        r'\b(SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\s+=\s+.*)\b',
        r'(\bOR\s+\d+\s*=\s*\d+)',
        r'(\bAND\s+\d+\s*=\s*\d+)',
        r'(\'.*\'.*=.*\'.*\')',
        r'(\bINTO\s+OUTFILE\b)',
        r'(\bLOAD_FILE\b)',
        r'(\bINTO\s+DUMPFILE\b)',
    ]
    
    def __init__(self):
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            for pattern in self.DANGEROUS_PATTERNS
        ]
    
    def validate_table_name(self, table_name: str, schema: str = 'public') -> None:
        """
        Validate table name against whitelist.
        
        Args:
            table_name: The table name to validate
            schema: The schema name ('public' or 'tenants')
            
        Raises:
            QueryValidationError: If table name is invalid
        """
        if not isinstance(table_name, str):
            raise QueryValidationError("Table name must be a string")
        
        if not table_name:
            raise QueryValidationError("Table name cannot be empty")
        
        # Check against whitelist
        if schema == 'tenants':
            allowed_tables = self.ALLOWED_TENANT_TABLES
        else:
            allowed_tables = self.ALLOWED_PUBLIC_TABLES
        
        if table_name not in allowed_tables:
            raise QueryValidationError(
                f"Table '{table_name}' not allowed in schema '{schema}'",
                field="table_name",
                value=table_name
            )
        
        logger.debug(f"Table name '{table_name}' validated for schema '{schema}'")
    
    def validate_column_name(self, column_name: str) -> None:
        """
        Validate column name format.
        
        Args:
            column_name: The column name to validate
            
        Raises:
            QueryValidationError: If column name is invalid
        """
        if not isinstance(column_name, str):
            raise QueryValidationError("Column name must be a string")
        
        if not column_name:
            raise QueryValidationError("Column name cannot be empty")
        
        # Allow wildcard for SELECT *
        if column_name == '*':
            return
        
        # Allow comma-separated column lists
        columns = [col.strip() for col in column_name.split(',')]
        for col in columns:
            if not self.ALLOWED_COLUMN_PATTERN.match(col):
                raise QueryValidationError(
                    f"Invalid column name format: '{col}'",
                    field="column_name",
                    value=col
                )
        
        logger.debug(f"Column name '{column_name}' validated")
    
    def validate_filter_value(self, value: Any) -> Any:
        """
        Validate and sanitize filter values.
        
        Args:
            value: The filter value to validate
            
        Returns:
            The validated/sanitized value
            
        Raises:
            QueryValidationError: If value is invalid
        """
        # Handle None values
        if value is None:
            return None
        
        # Handle UUID values
        if isinstance(value, UUID):
            return str(value)
        
        # Handle string values
        if isinstance(value, str):
            # Check for dangerous patterns
            for pattern in self.compiled_patterns:
                if pattern.search(value):
                    raise QueryValidationError(
                        "Potentially dangerous pattern detected in filter value",
                        field="filter_value",
                        value=value
                    )
            
            # Limit string length
            if len(value) > 1000:
                raise QueryValidationError(
                    "Filter value too long (max 1000 characters)",
                    field="filter_value",
                    value=len(value)
                )
            
            return value
        
        # Handle numeric values
        if isinstance(value, (int, float)):
            # Check for reasonable bounds
            if isinstance(value, int) and (value < -2147483648 or value > 2147483647):
                raise QueryValidationError(
                    "Integer value out of bounds",
                    field="filter_value",
                    value=value
                )
            return value
        
        # Handle boolean values
        if isinstance(value, bool):
            return value
        
        # Handle list values (for IN clauses)
        if isinstance(value, list):
            if len(value) > 100:
                raise QueryValidationError(
                    "Too many values in list filter (max 100)",
                    field="filter_value",
                    value=len(value)
                )
            
            # Recursively validate each item
            return [self.validate_filter_value(item) for item in value]
        
        # Reject other types
        raise QueryValidationError(
            f"Unsupported filter value type: {type(value).__name__}",
            field="filter_value",
            value=type(value).__name__
        )
    
    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate filter dictionary.
        
        Args:
            filters: Dictionary of column names and values
            
        Returns:
            Validated filters dictionary
            
        Raises:
            QueryValidationError: If filters are invalid
        """
        if not isinstance(filters, dict):
            raise QueryValidationError("Filters must be a dictionary")
        
        if len(filters) > 20:
            raise QueryValidationError("Too many filter conditions (max 20)")
        
        validated_filters = {}
        
        for column, value in filters.items():
            # Validate column name
            self.validate_column_name(column)
            
            # Validate and sanitize value
            validated_value = self.validate_filter_value(value)
            validated_filters[column] = validated_value
        
        logger.debug(f"Filters validated: {list(validated_filters.keys())}")
        return validated_filters
    
    def validate_order_by(self, order_by: str) -> None:
        """
        Validate ORDER BY clause.
        
        Args:
            order_by: The ORDER BY clause to validate
            
        Raises:
            QueryValidationError: If ORDER BY clause is invalid
        """
        if not isinstance(order_by, str):
            raise QueryValidationError("ORDER BY must be a string")
        
        if not order_by:
            return
        
        # Parse ORDER BY clause
        parts = order_by.strip().split()
        if len(parts) > 2:
            raise QueryValidationError("Invalid ORDER BY format")
        
        column = parts[0]
        direction = parts[1].upper() if len(parts) == 2 else 'ASC'
        
        # Validate column name
        self.validate_column_name(column)
        
        # Validate direction
        if direction not in ('ASC', 'DESC'):
            raise QueryValidationError(
                f"Invalid ORDER BY direction: {direction}",
                field="order_direction",
                value=direction
            )
        
        logger.debug(f"ORDER BY clause validated: {order_by}")
    
    def validate_limit(self, limit: Optional[int]) -> Optional[int]:
        """
        Validate LIMIT value.
        
        Args:
            limit: The limit value to validate
            
        Returns:
            Validated limit value
            
        Raises:
            QueryValidationError: If limit is invalid
        """
        if limit is None:
            return None
        
        if not isinstance(limit, int):
            raise QueryValidationError("LIMIT must be an integer")
        
        if limit < 1:
            raise QueryValidationError("LIMIT must be positive")
        
        if limit > 1000:
            raise QueryValidationError("LIMIT too large (max 1000)")
        
        logger.debug(f"LIMIT validated: {limit}")
        return limit


# Global validator instance
query_validator = QueryValidator()


def validate_supabase_query(
    table_name: str,
    schema: str = 'public',
    columns: str = '*',
    filters: Optional[Dict[str, Any]] = None,
    order_by: Optional[str] = None,
    limit: Optional[int] = None
) -> Dict[str, Any]:
    """
    Validate a complete Supabase query.
    
    Args:
        table_name: The table name
        schema: The schema name
        columns: The columns to select
        filters: Filter conditions
        order_by: ORDER BY clause
        limit: LIMIT value
        
    Returns:
        Dictionary of validated query parameters
        
    Raises:
        QueryValidationError: If any parameter is invalid
    """
    # Validate all parameters
    query_validator.validate_table_name(table_name, schema)
    query_validator.validate_column_name(columns)
    
    validated_filters = {}
    if filters:
        validated_filters = query_validator.validate_filters(filters)
    
    if order_by:
        query_validator.validate_order_by(order_by)
    
    validated_limit = query_validator.validate_limit(limit)
    
    return {
        'table_name': table_name,
        'schema': schema,
        'columns': columns,
        'filters': validated_filters,
        'order_by': order_by,
        'limit': validated_limit
    }
