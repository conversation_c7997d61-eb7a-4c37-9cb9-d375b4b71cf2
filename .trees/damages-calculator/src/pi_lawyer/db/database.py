"""Database client initialization and utility functions."""
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

from supabase import Client, create_client


def get_supabase_client() -> Client:
    """Initialize and return a Supabase client.

    Returns:
        Client: Initialized Supabase client

    Raises:
        ValueError: If required environment variables are not set
    """
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    if not url or not key:
        raise ValueError(
            "SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables"
        )
    return create_client(url, key)


async def get_case(
    case_id: str, user_context: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Get a case by ID with access control.

    Args:
        case_id: The UUID of the case
        user_context: User context containing role and tenant_id

    Returns:
        Optional[Dict[str, Any]]: Case data if found and accessible, None otherwise
    """
    client = get_supabase_client()
    result = client.from_("cases").select("*").eq("id", case_id).execute()
    return result.data[0] if result.data else None


async def create_case(data: Dict[str, Any], user_context: Dict[str, Any]) -> str:
    """Create a new case record.

    Args:
        data: Case data including title, description, etc
        user_context: User context containing role and tenant_id

    Returns:
        str: ID of the created case

    Raises:
        ValueError: If user does not have required role
    """
    if user_context["role"] not in ["partner", "attorney"]:
        raise ValueError("Only partners and attorneys can create cases")

    client = get_supabase_client()
    result = (
        client.from_("cases")
        .insert(
            {
                **data,
                "tenant_id": user_context["tenant_id"],
                "created_by": user_context["id"],
                "created_at": datetime.utcnow().isoformat(),
            }
        )
        .execute()
    )

    return result.data[0]["id"] if result.data else None


async def get_document(document_id: str) -> Optional[Dict[str, Any]]:
    """Get a document by ID from the public documents table.

    Args:
        document_id: The UUID of the document

    Returns:
        Optional[Dict[str, Any]]: Document data if found, None otherwise
    """
    client = get_supabase_client()
    result = client.from_("documents").select("*").eq("id", document_id).execute()
    return result.data[0] if result.data else None


async def get_document_chunks(document_id: str) -> List[Dict[str, Any]]:
    """Get chunks for a document.

    Args:
        document_id: The UUID of the document

    Returns:
        List[Dict[str, Any]]: List of document chunks with embeddings
    """
    client = get_supabase_client()
    result = client.from_("chunks").select("*").eq("document_id", document_id).execute()
    return result.data if result.data else []
