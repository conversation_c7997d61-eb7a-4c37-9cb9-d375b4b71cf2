"""Database module."""

# Import modules to make them accessible via pi_lawyer.db.*
try:
    from .database import *
    from .pinecone_client import *  # Import all Pinecone-related classes and functions
    from .supabase_client import SupabaseClient

    # Make stubs available if needed
    try:
        from .pinecone_client_stub import pinecone as pinecone_stub
    except ImportError:
        pass  # Stub files are optional
        
except ImportError as e:
    import warnings
    warnings.warn(f"Error importing database module: {e}")
