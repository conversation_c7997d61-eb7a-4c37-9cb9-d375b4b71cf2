"""
Database client for the PI Lawyer AI system.

This module provides a client for interacting with the database.
It includes functions for getting a database client and executing queries.
"""

import logging
import os
from typing import Any, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Try to import the Supabase client
try:
    from supabase import Client, create_client
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Using mock client.")
    SUPABASE_AVAILABLE = False


# Global client instance
_db_client: Optional[Any] = None


async def get_db_client() -> Any:
    """
    Get a database client.
    
    Returns:
        A database client instance
    """
    global _db_client
    
    if _db_client is not None:
        return _db_client
    
    # Get the Supabase URL and key from environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

    if not supabase_url or not supabase_key:
        logger.warning("Supabase URL or service key not set. Using mock client.")
        from unittest.mock import MagicMock
        _db_client = MagicMock()
        return _db_client
    
    if not SUPABASE_AVAILABLE:
        logger.warning("Supabase client not available. Using mock client.")
        from unittest.mock import MagicMock
        _db_client = MagicMock()
        return _db_client
    
    # Create the Supabase client
    _db_client = create_client(supabase_url, supabase_key)
    
    return _db_client


async def close_db_client() -> None:
    """Close the database client."""
    global _db_client
    
    if _db_client is not None:
        # No explicit close method for Supabase client
        _db_client = None
