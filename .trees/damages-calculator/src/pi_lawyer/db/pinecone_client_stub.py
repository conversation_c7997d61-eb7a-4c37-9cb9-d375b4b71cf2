"""
STUB FILE - NOT FOR PRODUCTION USE

This is a temporary stub implementation of the Pinecone client.
Must be replaced with actual implementation before production deployment.
Created: 2025-05-22

This stub is used to satisfy import requirements during testing.
"""

import logging
import warnings
from typing import Any, Dict, List, Optional

# Setup logging
logger = logging.getLogger(__name__)

# Emit warning when this stub is imported
warnings.warn(
    "Using stub implementation of Pinecone client - NOT SUITABLE FOR PRODUCTION",
    category=RuntimeWarning
)

# Mock pinecone module
class PineconeStub:
    """Stub implementation of Pinecone module"""
    
    def init(self, api_key: str, environment: str):
        """Stub init method"""
        logger.warning("Stub Pinecone.init called - NOT FOR PRODUCTION USE")
        return None
    
    def Index(self, name: str):
        """Stub Index method"""
        return PineconeIndexStub(name)

# Create a mock pinecone global instance
pinecone = PineconeStub()

class PineconeIndexStub:
    """Stub implementation of Pinecone Index"""
    
    def __init__(self, name: str):
        logger.warning(f"Stub PineconeIndex '{name}' initialized - NOT FOR PRODUCTION USE")
        self.name = name
    
    def upsert(self, vectors: List[Dict[str, Any]], namespace: Optional[str] = None):
        """Stub upsert method"""
        return {"upserted_count": len(vectors)}
    
    def query(self, vector: List[float], top_k: int = 10, namespace: Optional[str] = None, 
              include_metadata: bool = True, include_values: bool = False):
        """Stub query method"""
        return {
            "matches": [
                {
                    "id": f"stub-doc-{i}",
                    "score": 0.9 - (i * 0.1),
                    "metadata": {"text": f"Stub document {i}", "source": "stub-source"}
                } for i in range(min(3, top_k))
            ]
        }
    
    def delete(self, ids: List[str] = None, namespace: Optional[str] = None, 
               delete_all: bool = False, filter: Dict = None):
        """Stub delete method"""
        return {"deleted_count": len(ids) if ids else 0}
