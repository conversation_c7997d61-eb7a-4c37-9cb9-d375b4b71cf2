"""Mock authentication utilities for testing."""
import base64
import json
from typing import Dict, Optional


def is_mock_token(token: str) -> bool:
    """Check if a token is a mock token."""
    return token.startswith("mock.")


def encode_jwt_part(part: dict) -> str:
    """Encode a JWT part to base64."""
    json_str = json.dumps(part)
    return base64.urlsafe_b64encode(json_str.encode()).rstrip(b"=").decode()


def get_mock_user_from_token(token: str) -> Optional[Dict]:
    """Extract user data from mock token."""
    if not is_mock_token(token):
        return None

    try:
        # Parse mock token parts
        _, payload_b64 = token.split(".")

        # Decode payload
        payload = json.loads(
            base64.urlsafe_b64decode(
                payload_b64 + "=" * (-len(payload_b64) % 4)
            ).decode()
        )

        return {"role": payload["role"], "id": payload["sub"]}
    except Exception:
        return None
