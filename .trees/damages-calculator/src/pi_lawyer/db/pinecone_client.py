"""Pinecone client for vector search capabilities."""
from typing import Any, Dict, List, Optional

from pinecone import Pinecone


class PineconeClient:
    """Client for interacting with Pinecone vector database."""

    def __init__(
        self,
        api_key: str,
        environment: str,
        index_name: str,
        tenant_id: Optional[str] = None,
    ):
        """Initialize the Pinecone client.

        Args:
            api_key: Pinecone API key
            environment: Pinecone environment
            index_name: Name of the Pinecone index to use
            tenant_id: Optional tenant ID for multi-tenant operations
        """
        # Initialize Pinecone with the newer SDK pattern
        pc = Pinecone(api_key=api_key)
        self.index = pc.Index(index_name)
        self.tenant_id = tenant_id

    def _get_namespace(self, is_legal: bool = False) -> str:
        """Get the appropriate namespace based on context.

        Args:
            is_legal: Whether this is for legal database or tenant data

        Returns:
            Namespace string
        """
        if is_legal:
            return "legal"
        elif self.tenant_id:
            return f"tenant_{self.tenant_id}"
        else:
            raise ValueError("Tenant ID required for non-legal operations")

    async def store_legal_document(
        self, document_id: str, vector: List[float], metadata: Dict[str, Any]
    ) -> None:
        """Store a legal document vector with metadata.

        Args:
            document_id: Unique identifier for the document
            vector: Document embedding vector
            metadata: Additional document metadata
        """
        await self.index.upsert(
            vectors=[(document_id, vector, metadata)],
            namespace=self._get_namespace(is_legal=True),
        )

    async def store_case_document(
        self, document_id: str, vector: List[float], metadata: Dict[str, Any]
    ) -> None:
        """Store a case document vector with metadata.

        Args:
            document_id: Unique identifier for the document
            vector: Document embedding vector
            metadata: Additional document metadata
        """
        await self.index.upsert(
            vectors=[(document_id, vector, metadata)],
            namespace=self._get_namespace(is_legal=False),
        )

    async def search_legal_database(
        self, query_vector: List[float], top_k: int = 5, filter: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Search the legal database using vector similarity.

        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filter: Optional metadata filters

        Returns:
            List of similar documents with scores
        """
        results = await self.index.query(
            vector=query_vector,
            top_k=top_k,
            filter=filter,
            namespace=self._get_namespace(is_legal=True),
            include_metadata=True,
        )
        return results.matches

    async def search_case_documents(
        self, query_vector: List[float], top_k: int = 5, filter: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Search case documents using vector similarity.

        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filter: Optional metadata filters

        Returns:
            List of similar documents with scores
        """
        results = await self.index.query(
            vector=query_vector,
            top_k=top_k,
            filter=filter,
            namespace=self._get_namespace(is_legal=False),
            include_metadata=True,
        )
        return results.matches

    async def delete_document(self, document_id: str, is_legal: bool = False) -> None:
        """Delete a document from the index.

        Args:
            document_id: ID of the document to delete
            is_legal: Whether this is a legal document or tenant document
        """
        await self.index.delete(
            ids=[document_id], namespace=self._get_namespace(is_legal=is_legal)
        )
