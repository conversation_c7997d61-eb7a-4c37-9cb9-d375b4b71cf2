"""
Stub Agents

This module provides stub implementations of agents that haven't been implemented yet.
These stubs are used for testing and development purposes.
"""

import logging
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig

from pi_lawyer.agents.base_agent import BaseAgent
from pi_lawyer.agents.config import AgentConfig, get_agent_config

# Set up logging
logger = logging.getLogger(__name__)


class TodoAgent(BaseAgent):
    """
    A stub agent that represents an agent that hasn't been implemented yet.

    This agent simply returns a message indicating that it's not yet implemented.
    It's used for testing and development purposes.
    """

    def __init__(self, name: str, agent_type: str = "todo", config: Optional[AgentConfig] = None):
        """
        Initialize the TodoAgent.

        Args:
            name: The name of the agent
            agent_type: The type of the agent
            config: Agent configuration
        """
        # Use provided config or create a default one
        if config is None:
            config = get_agent_config(name)
            if config is None:
                config = AgentConfig(
                    name=name,
                    agent_type=agent_type,
                    description=f"Stub agent for {name}",
                    version="0.1.0"
                )

        super().__init__(config)
        logger.info(f"TodoAgent '{name}' initialized")

    async def initialize(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Initialize the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info(f"Initializing TodoAgent '{self.name}'")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            return {
                "messages": [],
                "memory": {}
            }

        return state

    async def execute(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info(f"Executing TodoAgent '{self.name}'")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            new_state = {
                "messages": [],
                "memory": {},
                "next": "FINISH"
            }

            # Add a message indicating that the agent is not yet implemented
            new_state["messages"].append({
                "type": "ai",
                "content": f"The {self.name} is not yet implemented. This is a placeholder."
            })

            return new_state

        # Handle the case where state is a dictionary
        if isinstance(state, dict):
            # Add a message indicating that the agent is not yet implemented
            if "messages" in state:
                state["messages"].append({
                    "type": "ai",
                    "content": f"The {self.name} is not yet implemented. This is a placeholder."
                })

            # Set next to FINISH to end the conversation
            state["next"] = "FINISH"

            return state

        # Default case: return a minimal state
        return {
            "messages": [{
                "type": "ai",
                "content": f"The {self.name} is not yet implemented. This is a placeholder."
            }],
            "memory": {},
            "next": "FINISH"
        }

    async def cleanup(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Clean up the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info(f"Cleaning up TodoAgent '{self.name}'")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            return {
                "messages": [],
                "memory": {},
                "next": "FINISH"
            }

        return state
