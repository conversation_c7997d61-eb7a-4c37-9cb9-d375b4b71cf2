"""
Supervisor Agent Package

This package provides the Supervisor Agent, which is responsible for:
1. Classifying user intent in ≤ 1 LLM call
2. Dispatching to the correct node
3. Maintaining tenant isolation
4. Failing gracefully
5. Selecting appropriate LLM models at runtime

The Supervisor Agent serves as the entry point for the LangGraph system,
routing requests to the appropriate specialized agent or queuing async jobs.

Usage:
    from pi_lawyer.agents.insights.supervisor import SupervisorAgent, route_to_agent, model_select_node

    # Create a supervisor agent
    agent = SupervisorAgent()

    # Use the router in a StateGraph
    sg = StateGraph(AiLexState)

    # Add the model select node
    sg.add_node("model_select", model_select_node)

    # Add the supervisor agent
    sg.add_node("supervisorAgent", agent)

    # Add an edge from model select to supervisor
    sg.add_edge("model_select", "supervisorAgent")

    # Add the router
    sg.add_router(route_to_agent, {
        "supervisorAgent": "supervisorAgent",
        "intakeAgent": "intakeAgent",
        # ... other agents
    })

    # Set the entry point to model select
    sg.set_entry_point("model_select")
"""

from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.nodes import model_select_node
from pi_lawyer.agents.insights.supervisor.router import get_agent_args, route_to_agent

__all__ = ["SupervisorAgent", "route_to_agent", "get_agent_args", "model_select_node"]
