"""
Supervisor Agent Nodes

This module provides node implementations for the Supervisor Agent's LangGraph.
"""

import logging
import os
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

from src.pi_lawyer.shared.core.llm import LLMProvider, get_llm_registry

# Set up logging
logger = logging.getLogger(__name__)


async def model_select_node(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Select the appropriate LLM model based on registry settings.
    
    This node is consulted by the Supervisor before any LLM call to determine
    which model to use. It checks the LLM registry for default models and
    updates the state accordingly.
    
    Args:
        state: Current state
        config: Runnable configuration
        
    Returns:
        Updated state with model selection information
    """
    logger.debug("Executing model_select_node")
    
    # Initialize llm_config in state if not present
    if "llm_config" not in state:
        state["llm_config"] = {}
    
    # Get the registry
    registry = get_llm_registry()
    
    # Get provider from state or use default
    provider = state.get("llm_config", {}).get("provider", "openai")
    
    # Convert to string if it's an enum
    if isinstance(provider, LLMProvider):
        provider = provider.value
    
    # Get the default model for the provider
    default_model = registry.get_default_model(provider)
    
    if default_model:
        logger.info(f"Using default model {default_model} for provider {provider}")
        
        # Update the state with the selected model
        state["llm_config"]["model"] = default_model
        state["llm_config"]["provider"] = provider
        
        # Store model selection in memory for tracking
        if "memory" in state:
            if "model_selections" not in state["memory"]:
                state["memory"]["model_selections"] = []
            
            state["memory"]["model_selections"].append({
                "provider": provider,
                "model": default_model,
                "timestamp": config.get("timestamp", None),
                "node": "model_select_node"
            })
    else:
        logger.warning(f"No default model found for provider {provider}")
        
        # Use a fallback model
        fallback_model = os.getenv("FALLBACK_LLM_MODEL", "gpt-3.5-turbo")
        fallback_provider = os.getenv("FALLBACK_LLM_PROVIDER", "openai")
        
        logger.info(f"Using fallback model {fallback_model} for provider {fallback_provider}")
        
        # Update the state with the fallback model
        state["llm_config"]["model"] = fallback_model
        state["llm_config"]["provider"] = fallback_provider
        
        # Store model selection in memory for tracking
        if "memory" in state:
            if "model_selections" not in state["memory"]:
                state["memory"]["model_selections"] = []
            
            state["memory"]["model_selections"].append({
                "provider": fallback_provider,
                "model": fallback_model,
                "timestamp": config.get("timestamp", None),
                "node": "model_select_node",
                "fallback": True
            })
    
    return state
