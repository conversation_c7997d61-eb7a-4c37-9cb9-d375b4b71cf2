"""
Verify Insight Agents

This script verifies that the insight agents can be registered with a graph.
"""

import os
import sys
from unittest.mock import Magic<PERSON>ock

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import the modules
import daily
import document
import swarm


def verify_document_agent():
    """Verify that the document agent can be registered with a graph."""
    # Create a mock StateGraph
    mock_graph = MagicMock()
    
    # Mock the add_node method
    mock_graph.add_node = MagicMock()
    
    # Call the register function
    node_ids = document.register(mock_graph)
    
    # Verify that add_node was called for each node
    assert mock_graph.add_node.call_count == 2
    
    # Verify that the correct node IDs were returned
    assert "collect_document_context" in node_ids
    assert "draft_document_insights" in node_ids
    
    # Verify that the nodes were added with the correct functions
    mock_graph.add_node.assert_any_call("collect_document_context", document.collect_context)
    mock_graph.add_node.assert_any_call("draft_document_insights", document.draft_doc)
    
    print("Document agent verification passed!")

def verify_daily_agent():
    """Verify that the daily agent can be registered with a graph."""
    # Create a mock StateGraph
    mock_graph = MagicMock()
    
    # Mock the add_node method
    mock_graph.add_node = MagicMock()
    
    # Call the register function
    node_ids = daily.register(mock_graph)
    
    # Verify that add_node was called for each node
    assert mock_graph.add_node.call_count == 2
    
    # Verify that the correct node IDs were returned
    assert "gather_daily_events" in node_ids
    assert "summarize_daily_activities" in node_ids
    
    # Verify that the nodes were added with the correct functions
    mock_graph.add_node.assert_any_call("gather_daily_events", daily.gather_events)
    mock_graph.add_node.assert_any_call("summarize_daily_activities", daily.summarize)
    
    print("Daily agent verification passed!")

def verify_swarm_agent():
    """Verify that the swarm agent can be registered with a graph."""
    # Create a mock StateGraph
    mock_graph = MagicMock()
    
    # Mock the add_node method
    mock_graph.add_node = MagicMock()
    
    # Call the register function
    node_ids = swarm.register(mock_graph)
    
    # Verify that add_node was called for each node
    assert mock_graph.add_node.call_count == 1
    
    # Verify that the correct node IDs were returned
    assert "spawn_child_agents" in node_ids
    
    # Verify that the nodes were added with the correct functions
    mock_graph.add_node.assert_any_call("spawn_child_agents", swarm.spawn_children)
    
    print("Swarm agent verification passed!")

if __name__ == "__main__":
    verify_document_agent()
    verify_daily_agent()
    verify_swarm_agent()
    print("All verifications passed!")
