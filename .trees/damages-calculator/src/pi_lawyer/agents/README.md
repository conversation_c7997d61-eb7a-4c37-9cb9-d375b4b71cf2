# AiLex Agent System (Legacy)

⚠️ **DEPRECATED**: This directory contains legacy agent stubs and configuration.

**Current Agent Implementations** are located in `backend/agents/`:
- **Interactive Agents** (`backend/agents/interactive/`): Quick response agents (< 5 seconds)
  - Research Agent: Legal research and case law search
  - Intake Agent: Multi-practice client intake (PI, Family Law, Criminal Defense)
  - Calendar CRUD Agent: Calendar and scheduling management
  - Task CRUD Agent: Task and deadline management
  - Master Router: Request routing and agent orchestration
- **Insights Agents** (`backend/agents/insights/`): Long-running analysis agents
  - Document Agent: Legal document generation and templates
  - Deadline Insights Agent: Deadline analysis and conflict detection
  - Supervisor Agent: Multi-agent coordination and oversight
- **Matter Client Agent** (`backend/agents/matter_client/`): Matter-specific operations

All agents are fully implemented and integrated with the FastAPI runtime at `src/pi_lawyer/api/runtime.py`.

## Research Agent

### Overview

The Research Agent is a sophisticated GraphRAG-powered search and synthesis system designed for solo attorneys and small law firms. It can search across public legal sources and private case documents, returning cited, trustworthy legal information.

### Key Features

- **Automatic Mode Classification**: AI-powered classification of queries to determine appropriate search scope (public law, case-specific, or hybrid).
- **Jurisdiction & Practice Area Compliance**: Results are scoped to the tenant's subscription jurisdiction(s) and practice area(s).
- **Public & Private Retrieval**: Can search both public legal databases (statutes, cases) and private case documents.
- **GraphRAG Architecture**: Combines vector search and graph-based navigation for superior retrieval.
- **Citation Integrity**: Answers include proper citation of sources.
- **CopilotKit Integration**: Seamlessly works with frontend via CopilotKit Cloud.

### How Jurisdiction & Practice Area Filtering Works

The Research Agent dynamically filters results based on the tenant's subscription:

1. **Subscription Lookup**: When a query is received, the agent looks up the tenant's active subscription in the `tenants.tenant_subscriptions` table
2. **Metadata Extraction**: Retrieves `jurisdiction` (state) and `practice_areas` from the subscription metadata
3. **Filter Application**: All searches to Pinecone and Neo4j include filters for:
   - The specific jurisdiction (e.g., "texas", "florida")
   - The subscribed practice areas (e.g., "personal_injury", "criminal_defense", "family_law")
4. **Type Safety**: The system validates all practice areas against allowed literals to ensure integrity
5. **Fallback Safety**: If subscription lookup fails, defaults to "texas" and "personal_injury"

This ensures that attorneys only see legal information relevant to:
- The state(s) where they practice
- The practice area(s) they've subscribed to

### Technical Implementation

- **`state.py`**: Defines the state management and data structures
- **`graph.py`**: Implements the LangGraph workflow with nodes for classification, retrieval, and synthesis
- **`research_copilot_handler.py`**: Handles CopilotKit requests, tenant subscription lookup, and response formatting

### Pinecone Configuration (Important)

When working with the Research Agent and Pinecone, there are specific configuration requirements:

#### Direct URL Access

The Pinecone index must be accessed through its direct URL rather than through the standard list_indexes/Index pattern. This is critical for proper functionality.

```python
# ❌ This standard approach doesn't work with the index
pinecone.init(api_key=PINECONE_API_KEY, environment=PINECONE_ENVIRONMENT)
index = pinecone.Index(PINECONE_INDEX_NAME)  # Fails to find index

# ✅ This direct URL approach works correctly
direct_host = "https://texas-laws-voyage3large-4ye958w.svc.aped-4627-b74a.pinecone.io"
index = pinecone.Index(
    index_name=PINECONE_INDEX_NAME,
    host=direct_host
)
```

#### Environment Variables

Add these to your `.env` file:

```
PINECONE_API_KEY=your_api_key_here
PINECONE_ENVIRONMENT=us-east-1-aws
PINECONE_INDEX_NAME=texas-laws-voyage3large
PINECONE_INDEX_URL=https://texas-laws-voyage3large-4ye958w.svc.aped-4627-b74a.pinecone.io
```

#### Namespaces for Jurisdictions

The index contains the following namespaces that align with different jurisdictions:

- `tx`, `tx-case`, `tx-statute`: Texas law (default jurisdiction)
- `ca-case`, `ca-statute`: California law
- `ny-case`, `ny-statute`: New York law
- `fed-case`, `fed-statute`: Federal law

The Research Agent uses these namespaces for filtering based on the tenant's subscription jurisdiction.

### CopilotKit Cloud Integration

The agent is designed to be accessed via CopilotKit Cloud:

1. Start the FastAPI backend: `uvicorn server:app --port 8000`
2. Create a CopilotKit Cloud tunnel: `npx copilotkit@latest dev --port 8000`
3. Use the CopilotKit component in your frontend with `agent="research_agent"`

### Subscription Configuration

For the Research Agent to properly filter by jurisdiction and practice areas, ensure:

1. The `tenants.tenant_subscriptions` table includes proper subscription metadata:
```json
{
  "jurisdiction": "texas", // Or another state abbreviation
  "practice_areas": ["personal_injury", "criminal_defense", "family_law"] // Based on subscription tier
}
```

2. When updating subscriptions, use the `SubscriptionService.updateSubscription` method which properly sets these metadata fields.

### Developing New Agents

When implementing new agents:

1. Create a directory structure like `agents/agent_name/`
2. Implement `state.py` for the agent's state management
3. Implement `graph.py` with the LangGraph workflow
4. Create a handler class (`agent_name_copilot_handler.py`)
5. Register the agent in `api/copilotkit_route.py`

## Supervisor Agent

The Supervisor Agent (currently in development) will eventually orchestrate the routing between specialized agents.
