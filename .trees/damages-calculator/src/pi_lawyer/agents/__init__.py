"""
Agents Package

This package provides the agent implementations for the AiLex system.
"""

# Import interactive agents
try:
    from pi_lawyer.agents.interactive import *
except ImportError:
    pass

# Import insight agents
try:
    from pi_lawyer.agents.insights import *
except ImportError:
    pass

# Import graph
# Import stubs
from pi_lawyer.agents._stubs import TodoAgent
from pi_lawyer.agents.graph import FinishAgent, build_graph

__all__ = [
    "build_graph",
    "FinishAgent",
    "TodoAgent",
]
