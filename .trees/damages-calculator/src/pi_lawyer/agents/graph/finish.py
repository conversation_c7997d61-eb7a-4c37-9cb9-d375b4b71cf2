"""
Finish Agent

This module provides a simple FinishAgent that marks the end of a conversation.
It's used as the final node in the StateGraph to ensure clean checkpointing.
"""

import logging
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig

from pi_lawyer.agents.base_agent import BaseAgent
from pi_lawyer.agents.config import AgentConfig, get_agent_config

# Set up logging
logger = logging.getLogger(__name__)


class FinishAgent(BaseAgent):
    """
    A simple agent that marks the end of a conversation.

    This agent is used as the final node in the StateGraph to ensure
    clean checkpointing and proper AG-UI protocol compliance.
    """

    def __init__(self, config: Optional[AgentConfig] = None):
        """
        Initialize the FinishAgent.

        Args:
            config: Agent configuration
        """
        # Use provided config or create a default one
        if config is None:
            config = get_agent_config("FinishAgent")
            if config is None:
                config = AgentConfig(
                    name="finish",
                    agent_type="system",
                    description="Agent that marks the end of a conversation",
                    version="1.0.0"
                )

        super().__init__(config)
        logger.info("FinishAgent initialized")

    async def initialize(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Initialize the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Initializing FinishAgent")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            return {
                "messages": [],
                "memory": {}
            }

        return state

    async def execute(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Executing FinishAgent")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            return {
                "messages": [],
                "memory": {},
                "finished": True,
                "last_node": "finish"
            }

        # Mark the conversation as finished
        state["finished"] = True
        state["last_node"] = "finish"

        return state

    async def cleanup(self, state: Any, config: RunnableConfig) -> Dict[str, Any]:
        """
        Clean up the agent.

        Args:
            state: Current state or a string representing the next node
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up FinishAgent")

        # Handle the case where state is a string
        if isinstance(state, str):
            # Create a new state dictionary
            return {
                "messages": [],
                "memory": {},
                "finished": True,
                "last_node": "finish"
            }

        return state
