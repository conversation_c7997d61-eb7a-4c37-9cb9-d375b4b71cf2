"""
Tests for the Finish Node in the StateGraph

This module implements comprehensive tests to verify that the Finish node
is always the final destination in our LangGraph implementation, regardless
of the entry point or execution path.
"""

import logging
from typing import Any, Dict, Optional
from unittest.mock import patch

import pytest

from pi_lawyer.agents.graph import build_graph
from pi_lawyer.agents.graph.finish import FinishAgent
from pi_lawyer.agents.tests.conftest import run_once

# Set up logging
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
class TestFinishNode:
    """Tests for the Finish Node in the StateGraph."""

    @pytest.fixture
    def make_state(self):
        """Create a test state with optional parameters."""
        def _make_state(
            msg: Optional[str] = None,
            tenant: str = "tenant-123",
            user: str = "user-456",
            thread: str = "thread-789",
            page_intent: Optional[str] = None,
        ) -> Dict[str, Any]:
            """
            Create a test state with the given parameters.

            Args:
                msg: The user message (if any)
                tenant: The tenant ID
                user: The user ID
                thread: The thread ID
                page_intent: The page intent (if any)

            Returns:
                A test state
            """
            state = {
                "tenant_id": tenant,
                "user_id": user,
                "thread_id": thread,
                "messages": [],
                "memory": {},
            }

            # Add the user message if provided
            if msg:
                state["messages"].append({"type": "human", "content": msg})

            # Add the page intent if provided
            if page_intent:
                state["page_intent"] = page_intent

            return state

        return _make_state

    async def test_finish_node_properties(self):
        """Test that the FinishAgent sets the expected properties."""
        # Create a FinishAgent
        agent = FinishAgent()

        # Create a simple state
        state = {"messages": [], "memory": {}}

        # Execute the agent
        result = await agent.execute(state, {})

        # Verify the result
        assert result["finished"] is True
        assert result["last_node"] == "finish"

    async def test_all_entry_points_end_at_finish(self, make_state):
        """Test that all entry points end at the finish node."""
        # Test all entry points
        entry_points = ["intake", "research"]  # Skip supervisor for now as it requires special handling

        for entry in entry_points:
            # Create a state
            state = make_state()

            # Build the graph with the current entry point
            graph = build_graph(entry)

            # Run the graph
            out = await run_once(graph, state)

            # Verify the result
            assert out["finished"] is True
            assert out["last_node"] == "finish"
            logger.info(f"Entry point '{entry}' correctly ends at finish node")

    async def test_direct_routing_ends_at_finish(self, make_state):
        """Test that direct routing via page_intent ends at the finish node."""
        # Test all interactive agents
        interactive_agents = ["intakeAgent", "researchAgent", "taskCrudAgent", "calendarCrudAgent"]

        for agent in interactive_agents:
            # Create a state with page_intent
            state = make_state(page_intent=agent)

            # Build the graph
            graph = build_graph("supervisor")

            # Run the graph
            out = await run_once(graph, state)

            # Verify the result
            assert out["finished"] is True
            assert out["last_node"] == "finish"
            logger.info(f"Direct routing to '{agent}' correctly ends at finish node")

    async def test_supervisor_routing_ends_at_finish(self, make_state):
        """Test that supervisor routing ends at the finish node."""
        # Test all possible next values from supervisor
        next_values = [
            "intakeAgent",
            "researchAgent",
            "taskCrudAgent",
            "calendarCrudAgent",
            "documentDraftAgent",
            "insightSwarmAgent",
            "FINISH"
        ]

        for next_value in next_values:
            # Create a state with a message
            state = make_state(msg="Test message")

            # Mock the supervisor agent to return a specific next value
            with patch("pi_lawyer.agents.insights.supervisor.agent.SupervisorAgent.execute") as mock_execute:
                # Configure the mock to return a state with the current next value
                async def mock_execute_impl(self, state, config=None):
                    if isinstance(state, dict):
                        state["next"] = next_value
                        return state
                    else:
                        return {
                            "next": next_value,
                            "messages": [],
                            "memory": {}
                        }

                mock_execute.side_effect = mock_execute_impl

                # Build the graph
                graph = build_graph("supervisor")

                # Run the graph
                out = await run_once(graph, state)

                # Verify the result
                assert out["finished"] is True
                assert out["last_node"] == "finish"
                logger.info(f"Supervisor routing to '{next_value}' correctly ends at finish node")

    async def test_fallback_ends_at_finish(self):
        """Test that fallback behavior ends at the finish node."""
        # This test is more complex and requires a different approach
        # For now, we'll just verify that the FinishAgent handles errors correctly

        # Create a FinishAgent
        agent = FinishAgent()

        # Create a state with an error
        state = {"messages": [], "memory": {}, "error": "Test error"}

        # Execute the agent
        result = await agent.execute(state, {})

        # Verify the result
        assert result["finished"] is True
        assert result["last_node"] == "finish"
        assert "error" in result
        logger.info("Fallback behavior correctly handled by FinishAgent")

    async def test_edge_case_empty_state(self):
        """Test that an empty state still ends at the finish node."""
        # Create a FinishAgent
        agent = FinishAgent()

        # Create an empty state
        state = {}

        # Execute the agent
        result = await agent.execute(state, {})

        # Verify the result
        assert result["finished"] is True
        assert result["last_node"] == "finish"
        logger.info("Empty state correctly handled by FinishAgent")

    async def test_edge_case_string_input(self):
        """Test that a string input still ends at the finish node."""
        # Create a string input (simulating direct node routing)
        state = "intakeAgent"

        # Build the graph
        graph = build_graph("supervisor")

        # Run the graph
        out = await run_once(graph, state)

        # Verify the result
        assert out["finished"] is True
        assert out["last_node"] == "finish"
        logger.info("String input correctly ends at finish node")
