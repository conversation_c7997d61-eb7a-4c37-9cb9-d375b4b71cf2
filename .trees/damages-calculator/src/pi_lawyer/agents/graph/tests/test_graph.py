"""
Tests for the StateGraph

This module implements tests for the LangGraph StateGraph.
"""

import logging
from typing import Any, Dict, Optional
from unittest.mock import patch

import pytest

from pi_lawyer.agents.graph import build_graph
from pi_lawyer.agents.tests.conftest import run_once

# Set up logging
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
class TestStateGraph:
    """Tests for the StateGraph."""

    @pytest.fixture
    def make_state(self):
        """Create a test state with optional parameters."""
        def _make_state(
            msg: Optional[str] = None,
            tenant: str = "tenant-123",
            user: str = "user-456",
            thread: str = "thread-789",
            page_intent: Optional[str] = None,
        ) -> Dict[str, Any]:
            """
            Create a test state with the given parameters.

            Args:
                msg: The user message (if any)
                tenant: The tenant ID
                user: The user ID
                thread: The thread ID
                page_intent: The page intent (if any)

            Returns:
                A test state
            """
            state = {
                "tenant_id": tenant,
                "user_id": user,
                "thread_id": thread,
                "messages": [],
                "memory": {},
            }

            # Add the user message if provided
            if msg:
                state["messages"].append({"type": "human", "content": msg})

            # Add the page intent if provided
            if page_intent:
                state["page_intent"] = page_intent

            return state

        return _make_state

    async def test_router_direct(self, make_state):
        """Test direct routing via page_intent."""
        # Create a state with page_intent
        state = make_state(page_intent="intakeAgent")

        # Build the graph
        graph = build_graph("supervisor")

        # Run the graph
        out = await run_once(graph, state)

        # Verify the result
        assert out["last_node"] == "finish"
        assert any("not yet implemented" in m["content"] for m in out["messages"] if m["type"] == "ai")

    async def test_supervisor_to_async(self, make_state):
        """Test routing from supervisor to async agent."""
        # Create a state with a message
        state = make_state(msg="Draft a contract")

        # Mock the supervisor agent to return a specific next value
        with patch("pi_lawyer.agents.insights.supervisor.agent.SupervisorAgent.execute") as mock_execute:
            # Configure the mock to return a state with next="documentDraftAgent"
            async def mock_execute_impl(self, state, config=None):
                if isinstance(state, dict):
                    state["next"] = "documentDraftAgent"
                    state["async_job_id"] = "job-123"
                    return state
                else:
                    return {
                        "next": "documentDraftAgent",
                        "async_job_id": "job-123",
                        "messages": [],
                        "memory": {}
                    }

            mock_execute.side_effect = mock_execute_impl

            # Build the graph
            graph = build_graph("supervisor")

            # Run the graph
            out = await run_once(graph, state)

            # Verify the result
            assert out["last_node"] == "finish"
            assert "async_job_id" in out
            assert out["async_job_id"] == "job-123"

    async def test_entry_point_intake(self, make_state):
        """Test using the intake entry point."""
        # Create a state
        state = make_state()

        # Build the graph with intake entry point
        graph = build_graph("intake")

        # Run the graph
        out = await run_once(graph, state)

        # Verify the result
        assert out["last_node"] == "finish"
        assert any("not yet implemented" in m["content"] for m in out["messages"] if m["type"] == "ai")

    async def test_entry_point_research(self, make_state):
        """Test using the research entry point."""
        # Create a state
        state = make_state()

        # Build the graph with research entry point
        graph = build_graph("research")

        # Run the graph
        out = await run_once(graph, state)

        # Verify the result
        assert out["last_node"] == "finish"
        assert any("not yet implemented" in m["content"] for m in out["messages"] if m["type"] == "ai")

    async def test_supervisor_finish(self, make_state):
        """Test supervisor setting next to FINISH."""
        # Create a state with a message
        state = make_state(msg="Goodbye")

        # Mock the supervisor agent to return a specific next value
        with patch("pi_lawyer.agents.insights.supervisor.agent.SupervisorAgent.execute") as mock_execute:
            # Configure the mock to return a state with next="FINISH"
            async def mock_execute_impl(self, state, config=None):
                if isinstance(state, dict):
                    state["next"] = "FINISH"
                    return state
                else:
                    return {
                        "next": "FINISH",
                        "messages": [],
                        "memory": {}
                    }

            mock_execute.side_effect = mock_execute_impl

            # Build the graph
            graph = build_graph("supervisor")

            # Run the graph
            out = await run_once(graph, state)

            # Verify the result
            assert out["last_node"] == "finish"
            assert out.get("finished", False) is True
