"""
StateGraph Builder (Updated)

This module provides functionality for building the LangGraph StateGraph.
It wires together the various agents and defines the flow between them.
"""

import logging
from typing import Any, Dict

from langgraph.graph import StateGraph

from pi_lawyer.agents._stubs import TodoAgent
from pi_lawyer.agents.graph.finish import FinishAgent
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.router import route_to_agent

# Import the intake agent components
try:
    from pi_lawyer.agents.interactive.intake import (
        IntakeAgent,
        check_conflicts,
        collect_case_details,
        collect_personal_info,
        initial_contact,
        intake_client_router,
        intake_staff_router,
        save_client_info,
        summarize_and_confirm,
    )
except ImportError:
    # Use placeholder if not available
    IntakeAgent = None
    intake_client_router = None
    intake_staff_router = None
    initial_contact = None
    collect_personal_info = None
    collect_case_details = None
    check_conflicts = None
    summarize_and_confirm = None
    save_client_info = None

# Set up logging
logger = logging.getLogger(__name__)

# Entry point mapping
ENTRY_MAP = {
    "supervisor": "routerNode",
    "intake": "intakeAgent",
    "intakeClient": "intakeClientRouter",
    "intakeStaff": "intakeStaffRouter",
    "research": "researchAgent",
}


def build_graph(entry: str = "supervisor") -> Any:
    """
    Build and compile the StateGraph.

    Args:
        entry: The entry point for the graph (supervisor, intake, research)

    Returns:
        The compiled StateGraph
    """
    logger.info(f"Building StateGraph with entry point: {entry}")

    # Create the StateGraph
    # Note: We're using Dict[str, Any] as a placeholder for AiLexState
    # This will be replaced with the actual AiLexState type when it's available
    sg = StateGraph(Dict[str, Any])

    # ► 1. Add nodes

    # Add the router node (a simple function)
    sg.add_node("routerNode", route_to_agent)

    # Add the supervisor agent
    sg.add_node("supervisorAgent", SupervisorAgent())

    # Add intake routers and agent
    if IntakeAgent is not None:
        # Add intake routers
        sg.add_node("intakeClientRouter", intake_client_router)
        sg.add_node("intakeStaffRouter", intake_staff_router)
        
        # Add intake agent
        sg.add_node("intakeAgent", IntakeAgent())
        
        # Add intake task nodes
        sg.add_node("initial_contact", initial_contact)
        sg.add_node("collect_personal_info", collect_personal_info)
        sg.add_node("collect_case_details", collect_case_details)
        sg.add_node("check_conflicts", check_conflicts)
        sg.add_node("summarize_and_confirm", summarize_and_confirm)
        sg.add_node("save_client_info", save_client_info)
    else:
        # Use placeholder if not available
        sg.add_node("intakeClientRouter", TodoAgent("intakeClientRouter", "interactive"))
        sg.add_node("intakeStaffRouter", TodoAgent("intakeStaffRouter", "interactive"))
        sg.add_node("intakeAgent", TodoAgent("intakeAgent", "interactive"))

    # Add other interactive agents (using TodoAgent as placeholders for now)
    sg.add_node("researchAgent", TodoAgent("researchAgent", "interactive"))
    sg.add_node("taskCrudAgent", TodoAgent("taskCrudAgent", "interactive"))
    sg.add_node("calendarCrudAgent", TodoAgent("calendarCrudAgent", "interactive"))

    # Add insight agents (using TodoAgent as placeholders for now)
    sg.add_node("documentDraftAgent", TodoAgent("documentDraftAgent", "insights"))
    sg.add_node("insightSwarmAgent", TodoAgent("insightSwarmAgent", "insights"))

    # Add the finish agent
    sg.add_node("finish", FinishAgent())

    # ► 2. Wire edges from router
    # Use add_conditional_edges instead of add_router
    # Handle the default case separately
    sg.add_conditional_edges(
        "routerNode",
        lambda state: route_to_agent(state) or "supervisorAgent",  # Default to supervisorAgent if None
        {
            "supervisorAgent": "supervisorAgent",
            "intakeAgent": "intakeAgent",
            "researchAgent": "researchAgent",
            "taskCrudAgent": "taskCrudAgent",
            "calendarCrudAgent": "calendarCrudAgent",
        }
    )

    # ► 3. Wire edges from supervisor's JSON output
    sg.add_conditional_edges(
        "supervisorAgent",
        lambda state: state.get("next") or "finish",  # Default to finish if None
        {
            "intakeAgent": "intakeAgent",
            "researchAgent": "researchAgent",
            "taskCrudAgent": "taskCrudAgent",
            "calendarCrudAgent": "calendarCrudAgent",
            "documentDraftAgent": "documentDraftAgent",
            "insightSwarmAgent": "insightSwarmAgent",
            "FINISH": "finish",
        }
    )

    # ► 4. Wire intake router edges
    sg.add_edge("intakeClientRouter", "intakeAgent")
    sg.add_edge("intakeStaffRouter", "intakeAgent")

    # ► 5. Wire intake agent edges
    if IntakeAgent is not None:
        sg.add_conditional_edges(
            "intakeAgent",
            lambda state: state.get("next") or "FINISH",
            {
                "initial_contact": "initial_contact",
                "collect_personal_info": "collect_personal_info",
                "collect_case_details": "collect_case_details",
                "check_conflicts": "check_conflicts",
                "summarize_and_confirm": "summarize_and_confirm",
                "save_client_info": "save_client_info",
                "FINISH": "finish",
            }
        )

        # Wire intake task node edges back to intake agent
        for node in [
            "initial_contact",
            "collect_personal_info",
            "collect_case_details",
            "check_conflicts",
            "summarize_and_confirm",
            "save_client_info",
        ]:
            sg.add_edge(node, "intakeAgent")
    else:
        # If intake agent is not available, just route to finish
        sg.add_edge("intakeAgent", "finish")

    # ► 6. All leaf nodes → finish
    for node in [
        "researchAgent",
        "taskCrudAgent",
        "calendarCrudAgent",
        "documentDraftAgent",
        "insightSwarmAgent",
    ]:
        sg.add_edge(node, "finish")

    # ► 7. Entry point
    entry_node = ENTRY_MAP.get(entry, "routerNode")
    logger.info(f"Setting entry point to: {entry_node}")
    sg.set_entry_point(entry_node)

    # Compile the graph
    compiled_graph = sg.compile()
    logger.info("StateGraph compiled successfully")

    return compiled_graph
