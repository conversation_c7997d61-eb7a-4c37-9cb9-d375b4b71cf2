"""
StateGraph Builder

This module provides functionality for building the LangGraph StateGraph.
It wires together the various agents and defines the flow between them.
"""

import logging
from typing import Any, Dict

from langgraph.graph import StateGraph

from pi_lawyer.agents._stubs import TodoAgent
from pi_lawyer.agents.graph.finish import FinishAgent
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.router import route_to_agent

# Set up logging
logger = logging.getLogger(__name__)

# Import the Task CRUD Agent
try:
    from backend.agents.interactive.task_crud import TaskCrudAgent, task_crud_router
    TASK_CRUD_AVAILABLE = True
except ImportError:
    TASK_CRUD_AVAILABLE = False
    logger.warning("Task CRUD Agent not available, using placeholder")

# Import the Calendar CRUD Agent
try:
    from backend.agents.interactive.calendar_crud import (
        CalendarCrudAgent,
        calendar_crud_router,
        check_free_busy,
        create_event,
        delete_event,
        read_event,
        update_event,
    )
    from backend.agents.interactive.calendar_crud import (
        parse_date as calendar_parse_date,
    )
    CALENDAR_CRUD_AVAILABLE = True
except ImportError:
    CALENDAR_CRUD_AVAILABLE = False
    logger.warning("Calendar CRUD Agent not available, using placeholder")

# Entry point mapping
ENTRY_MAP = {
    "supervisor": "routerNode",
    "intake": "intakeAgent",
    "research": "researchAgent",
    "task": "taskCrudRouter" if TASK_CRUD_AVAILABLE else "taskCrudAgent",
    "calendar": "calendarCrudRouter" if CALENDAR_CRUD_AVAILABLE else "calendarCrudAgent",
}


def build_graph(entry: str = "supervisor") -> Any:
    """
    Build and compile the StateGraph.

    Args:
        entry: The entry point for the graph (supervisor, intake, research)

    Returns:
        The compiled StateGraph
    """
    logger.info(f"Building StateGraph with entry point: {entry}")

    # Create the StateGraph
    # Note: We're using Dict[str, Any] as a placeholder for AiLexState
    # This will be replaced with the actual AiLexState type when it's available
    sg = StateGraph(Dict[str, Any])

    # ► 1. Add nodes

    # Add the router node (a simple function)
    sg.add_node("routerNode", route_to_agent)

    # Add the supervisor agent
    sg.add_node("supervisorAgent", SupervisorAgent())

    # Add interactive agents (using TodoAgent as placeholders for now)
    sg.add_node("intakeAgent", TodoAgent("intakeAgent", "interactive"))
    sg.add_node("researchAgent", TodoAgent("researchAgent", "interactive"))

    # Add Task CRUD Agent (use actual implementation if available, otherwise use placeholder)
    if TASK_CRUD_AVAILABLE:
        # Add the Task CRUD Agent and its nodes
        sg.add_node("taskCrudAgent", TaskCrudAgent())
        sg.add_node("taskCrudRouter", task_crud_router)
        sg.add_node("create_task", TaskCrudAgent().nodes["create_task"])
        sg.add_node("read_task", TaskCrudAgent().nodes["read_task"])
        sg.add_node("update_task", TaskCrudAgent().nodes["update_task"])
        sg.add_node("delete_task", TaskCrudAgent().nodes["delete_task"])
        sg.add_node("parse_date", TaskCrudAgent().nodes["parse_date"])
    else:
        # Use placeholder if not available
        sg.add_node("taskCrudAgent", TodoAgent("taskCrudAgent", "interactive"))

    # Add Calendar CRUD Agent (use actual implementation if available, otherwise use placeholder)
    if CALENDAR_CRUD_AVAILABLE:
        # Add the Calendar CRUD Agent and its nodes
        sg.add_node("calendarCrudAgent", CalendarCrudAgent())
        sg.add_node("calendarCrudRouter", calendar_crud_router)
        sg.add_node("create_event", create_event)
        sg.add_node("read_event", read_event)
        sg.add_node("update_event", update_event)
        sg.add_node("delete_event", delete_event)
        sg.add_node("check_free_busy", check_free_busy)
        sg.add_node("calendar_parse_date", calendar_parse_date)
    else:
        # Use placeholder if not available
        sg.add_node("calendarCrudAgent", TodoAgent("calendarCrudAgent", "interactive"))

    # Add insight agents (using TodoAgent as placeholders for now)
    sg.add_node("documentDraftAgent", TodoAgent("documentDraftAgent", "insights"))
    sg.add_node("insightSwarmAgent", TodoAgent("insightSwarmAgent", "insights"))

    # Add the finish agent
    sg.add_node("finish", FinishAgent())

    # ► 2. Wire edges from router
    # Use add_conditional_edges instead of add_router
    # Handle the default case separately
    sg.add_conditional_edges(
        "routerNode",
        lambda state: route_to_agent(state) or "supervisorAgent",  # Default to supervisorAgent if None
        {
            "supervisorAgent": "supervisorAgent",
            "intakeAgent": "intakeAgent",
            "researchAgent": "researchAgent",
            "taskCrudAgent": "taskCrudAgent",
            "calendarCrudAgent": "calendarCrudAgent",
        }
    )

    # ► 3. Wire edges from supervisor's Command output
    sg.add_conditional_edges(
        "supervisorAgent",
        lambda state: state.get("__command__", {}).get("goto", "finish"),  # Default to finish if None
        {
            "intakeAgent": "intakeAgent",
            "researchAgent": "researchAgent",
            "taskCrudAgent": "taskCrudAgent" if not TASK_CRUD_AVAILABLE else "taskCrudRouter",
            "calendarCrudAgent": "calendarCrudAgent" if not CALENDAR_CRUD_AVAILABLE else "calendarCrudRouter",
            "documentDraftAgent": "documentDraftAgent",
            "insightSwarmAgent": "insightSwarmAgent",
            "FINISH": "finish",
        }
    )

    # ► 3.1. Wire edges for Task CRUD Agent if available
    if TASK_CRUD_AVAILABLE:
        # Add conditional edges from taskCrudRouter to task nodes
        sg.add_conditional_edges(
            "taskCrudRouter",
            lambda state: state.get("next", "taskCrudAgent"),  # Default to taskCrudAgent if None
            {
                "create_task": "create_task",
                "read_task": "read_task",
                "update_task": "update_task",
                "delete_task": "delete_task",
                "parse_date": "parse_date",
                "FINISH": "finish",
            }
        )

        # Add conditional edges from task nodes
        for node in ["create_task", "read_task", "update_task", "delete_task", "parse_date"]:
            sg.add_conditional_edges(
                node,
                lambda state, node=node: state.get("next", "FINISH"),  # Default to FINISH if None
                {
                    "create_task": "create_task",
                    "read_task": "read_task",
                    "update_task": "update_task",
                    "delete_task": "delete_task",
                    "parse_date": "parse_date",
                    "taskCrudRouter": "taskCrudRouter",
                    "FINISH": "finish",
                }
            )

    # ► 3.2. Wire edges for Calendar CRUD Agent if available
    if CALENDAR_CRUD_AVAILABLE:
        # Add conditional edges from calendarCrudRouter to calendar nodes
        sg.add_conditional_edges(
            "calendarCrudRouter",
            lambda state: state.get("next", "calendarCrudAgent"),  # Default to calendarCrudAgent if None
            {
                "create_event": "create_event",
                "read_event": "read_event",
                "update_event": "update_event",
                "delete_event": "delete_event",
                "check_free_busy": "check_free_busy",
                "calendar_parse_date": "calendar_parse_date",
                "FINISH": "finish",
            }
        )

        # Add conditional edges from calendar nodes
        for node in ["create_event", "read_event", "update_event", "delete_event", "check_free_busy", "calendar_parse_date"]:
            sg.add_conditional_edges(
                node,
                lambda state, node=node: state.get("next", "FINISH"),  # Default to FINISH if None
                {
                    "create_event": "create_event",
                    "read_event": "read_event",
                    "update_event": "update_event",
                    "delete_event": "delete_event",
                    "check_free_busy": "check_free_busy",
                    "calendar_parse_date": "calendar_parse_date",
                    "calendarCrudRouter": "calendarCrudRouter",
                    "FINISH": "finish",
                }
            )

    # ► 4. All leaf nodes → finish
    leaf_nodes = [
        "intakeAgent",
        "researchAgent",
        "documentDraftAgent",
        "insightSwarmAgent",
    ]

    # Add Task CRUD Agent nodes if not available (otherwise they're handled by conditional edges)
    if not TASK_CRUD_AVAILABLE:
        leaf_nodes.append("taskCrudAgent")

    # Add Calendar CRUD Agent nodes if not available (otherwise they're handled by conditional edges)
    if not CALENDAR_CRUD_AVAILABLE:
        leaf_nodes.append("calendarCrudAgent")

    for node in leaf_nodes:
        sg.add_edge(node, "finish")

    # ► 5. Entry point
    entry_node = ENTRY_MAP.get(entry, "routerNode")
    logger.info(f"Setting entry point to: {entry_node}")
    sg.set_entry_point(entry_node)

    # Compile the graph
    compiled_graph = sg.compile()
    logger.info("StateGraph compiled successfully")

    return compiled_graph
