"""
Graph Package

This package provides functionality for building and using the LangGraph StateGraph.
It wires together the various agents and defines the flow between them.

Usage:
    from pi_lawyer.agents.graph import build_graph
    
    # Build the graph with the default entry point (supervisor)
    graph = build_graph()
    
    # Or with a specific entry point
    graph = build_graph(entry="intake")
    
    # Execute the graph
    result = await graph.ainvoke({"messages": [{"type": "human", "content": "Hello"}]})
"""

from pi_lawyer.agents.graph.builder import build_graph
from pi_lawyer.agents.graph.finish import FinishAgent

__all__ = ["build_graph", "FinishAgent"]
