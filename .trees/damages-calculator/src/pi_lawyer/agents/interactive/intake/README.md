# Intake Agent

The Intake Agent is a dual-mode agent that collects client information and creates new cases. It supports both client-facing and staff-facing intake flows, with mode-specific behavior.

## Architecture

The Intake Agent follows a modular architecture with the following components:

- **IntakeAgent**: The main agent class that extends `BaseAgent` and provides the core functionality
- **Router Nodes**: Entry points for client and staff intake flows
- **Task Nodes**: Specialized nodes for each step of the intake process
- **Practice Area Templates**: YAML templates for different practice areas
- **Mode-specific Configuration**: Separate prompts and tool configurations for client and staff modes

## Directory Structure

```
intake/
├─ client_flow/
│   ├─ prompt.md          # Client-facing prompt
│   └─ config.py          # Client-specific tool configuration
├─ staff_flow/
│   ├─ prompt.md          # Staff-facing prompt
│   └─ config.py          # Staff-specific tool configuration
├─ agent.py               # IntakeAgent implementation
├─ routers.py             # Router node implementations
└─ tasks.py               # Task node implementations
```

## Intake Process Flow

The intake process follows these steps:

1. **Initial Contact**: Welcome the user and ask for their name
2. **Collect Personal Info**: Collect contact information (email, phone)
3. **Collect Case Details**: Collect details about the case (description, type, practice area)
4. **Check Conflicts**: Check for conflicts of interest
5. **Summarize and Confirm**: Summarize collected information and confirm with the user
6. **Save Client Info**: Save client information to the database

## Dual-Mode Support

The Intake Agent supports two modes:

### Client Mode

- Designed for client-facing intake through the client portal
- Uses a friendly, empathetic tone
- Limited to a subset of tools for security
- Walks through all steps of the intake process

### Staff Mode

- Designed for staff-facing intake through the admin interface
- Uses a professional, concise tone
- Access to additional tools for efficiency
- Can skip steps when information is already available

## Practice Area Templates

The Intake Agent uses YAML templates to define the required fields for different practice areas:

- **Personal Injury**: Injury details, incident date, medical providers, etc.
- **Criminal Defense**: Charge details, court date, prior record, etc.
- **Family Law**: Case type, marriage/separation dates, children, etc.

New practice areas can be added by creating a new YAML template without changing the code.

## Usage

### Client Intake

```python
from pi_lawyer.agents.graph import build_graph

# Build the graph with the client intake entry point
graph = build_graph(entry="intakeClient")

# Initial state
state = {
    "messages": [{"type": "human", "content": "I need to submit a new case"}],
    "client": {},
    "case": {},
    "memory": {},
}
config = {"configurable": {"thread_id": "123", "tenant_id": "456", "user_id": "789"}}

# Execute the graph
result = await graph.ainvoke(state, config)
```

### Staff Intake

```python
from pi_lawyer.agents.graph import build_graph

# Build the graph with the staff intake entry point
graph = build_graph(entry="intakeStaff")

# Initial state with pre-filled data
state = {
    "messages": [{"type": "human", "content": "I'm submitting a new case for John Doe"}],
    "client": {"name": "John Doe", "email": "<EMAIL>", "phone": "************"},
    "case": {"description": "Car accident", "case_type": "auto_accident", "practice_area": "personal_injury"},
    "memory": {},
}
config = {"configurable": {"thread_id": "123", "tenant_id": "456", "user_id": "789"}}

# Execute the graph
result = await graph.ainvoke(state, config)
```

## Testing

The Intake Agent includes comprehensive tests:

- **Unit Tests**: Test individual components (agent, routers, tasks)
- **Integration Tests**: Test the flow from start to finish
- **Template Tests**: Test the practice area templates
- **Mode-specific Tests**: Test the client and staff modes

Run the tests with:

```bash
pytest src/pi_lawyer/tests/agents/interactive/intake/
```

## Future Enhancements

- **Multi-language Support**: Add support for additional languages
- **Advanced NLP**: Improve entity extraction for better information collection
- **Custom Workflows**: Allow tenants to define custom intake workflows
- **Integration with External Systems**: Add support for integration with external CRM systems
