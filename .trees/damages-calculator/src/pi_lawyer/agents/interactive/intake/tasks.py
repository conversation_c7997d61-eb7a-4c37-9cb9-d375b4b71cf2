"""
Intake Agent Task Nodes

This module provides task-specific nodes for the Intake Agent, which handle
different steps of the intake process.

Usage:
    from pi_lawyer.agents.interactive.intake.tasks import (
        initial_contact,
        collect_personal_info,
        collect_case_details,
        check_conflicts,
        summarize_and_confirm,
        save_client_info
    )

    # Add task nodes to the StateGraph
    sg.add_node("initial_contact", initial_contact)
    sg.add_node("collect_personal_info", collect_personal_info)
    # ...
"""

import logging
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

from pi_lawyer.shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)


async def initial_contact(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Handle initial contact with the client.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing initial_contact node")

    # Add a welcome message if this is the first interaction
    if len(state.get("messages", [])) <= 2:  # Only system message and user message
        state["messages"].append({
            "type": "ai",
            "content": "Welcome to our law firm's intake process. I'll help collect information about your case. "
                       "To get started, could you please tell me your full name?"
        })
    else:
        # Try to extract name from the last message
        last_message = None
        for message in reversed(state.get("messages", [])):
            if message.get("type") == "human":
                last_message = message
                break

        content = last_message.get("content", "") if last_message else ""

        # Simple name extraction (would use NER in production)
        if content and len(content.split()) >= 2:
            # Assume the message contains a name
            if "client" not in state:
                state["client"] = {}
            state["client"]["name"] = content.strip()
            state["messages"].append({
                "type": "ai",
                "content": f"Thank you, {state['client']['name']}. Could you please provide your contact information "
                           f"(email and phone number)?"
            })
            state["next"] = "collect_personal_info"
        else:
            # Ask again for the name
            state["messages"].append({
                "type": "ai",
                "content": "I need your full name to proceed with the intake process. Could you please provide your "
                           "first and last name?"
            })

    return state


async def collect_personal_info(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Collect personal information from the client.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing collect_personal_info node")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Check if we have contact info
    if "@" in content and ("phone" in content.lower() or any(c.isdigit() for c in content)):
        # Extract email and phone (simplified)
        email = None
        phone = None

        for word in content.split():
            if "@" in word:
                email = word.strip(",.;:")
            if word.replace("-", "").replace("(", "").replace(")", "").isdigit() and len(word) >= 10:
                phone = word

        state["client"]["email"] = email
        state["client"]["phone"] = phone

        # Move to next task
        state["messages"].append({
            "type": "ai",
            "content": "Thank you for your contact information. Now, could you briefly describe what happened "
                       "and the nature of your injury or legal issue?"
        })
        state["next"] = "collect_case_details"
    else:
        # Ask again for contact info
        state["messages"].append({
            "type": "ai",
            "content": "I need your contact information to proceed. Please provide your email address and phone number."
        })

    return state


async def collect_case_details(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Collect case details from the client.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing collect_case_details node")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    if content and len(content) > 20:  # Arbitrary threshold for a meaningful description
        state["case"]["description"] = content

        # Try to determine case type (simplified)
        case_type = "unknown"
        if "car" in content.lower() or "accident" in content.lower() or "crash" in content.lower():
            case_type = "auto_accident"
        elif "slip" in content.lower() or "fall" in content.lower():
            case_type = "slip_fall"
        elif "work" in content.lower() or "job" in content.lower():
            case_type = "workplace"
        elif "medical" in content.lower() or "doctor" in content.lower() or "hospital" in content.lower():
            case_type = "medical_malpractice"

        state["case"]["case_type"] = case_type
        state["case"]["practice_area"] = "personal_injury"

        # Ask for incident date
        state["messages"].append({
            "type": "ai",
            "content": "Thank you for sharing those details. When did this incident occur? Please provide the date."
        })

        # We would continue collecting more details here
        # For brevity, we'll move to conflict check
        state["next"] = "check_conflicts"
    else:
        # Ask again for case details
        state["messages"].append({
            "type": "ai",
            "content": "I need more details about your case to proceed. Please describe what happened and the "
                       "nature of your injury or legal issue in a few sentences."
        })

    return state


async def check_conflicts(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Check for conflicts of interest.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing check_conflicts node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")

    # Add a message indicating we're checking for conflicts
    state["messages"].append({
        "type": "ai",
        "content": "I'm checking our records for any potential conflicts of interest..."
    })

    # Get client name
    client_name = state.get("client", {}).get("name", "")

    if client_name:
        # Use the tool executor to check for conflicts
        tool_executor = get_tool_executor()

        try:
            conflicts = await tool_executor.execute_tool(
                tool_name="check_conflicts",
                tool_args={"client_name": client_name},
                tenant_id=tenant_id
            )

            if conflicts and len(conflicts) > 0:
                # Conflicts found
                state["messages"].append({
                    "type": "ai",
                    "content": "I've identified a potential conflict of interest. One of our attorneys will need to "
                               "review this before we can proceed. Someone from our office will contact you soon."
                })
                state["task_status"] = "conflict_found"
                state["next"] = "FINISH"
            else:
                # No conflicts
                state["messages"].append({
                    "type": "ai",
                    "content": "No conflicts of interest were found. Let's continue with your intake."
                })
                state["next"] = "summarize_and_confirm"
        except Exception as e:
            logger.error(f"Error checking conflicts: {str(e)}")
            state["messages"].append({
                "type": "ai",
                "content": "I encountered an issue while checking for conflicts. Let's continue with your intake, "
                           "and our team will verify this information later."
            })
            state["next"] = "summarize_and_confirm"
    else:
        # Missing client name
        state["messages"].append({
            "type": "ai",
            "content": "I need your name to check for conflicts of interest. Could you please provide your full name?"
        })
        state["next"] = "initial_contact"

    return state


async def summarize_and_confirm(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Summarize collected information and confirm with the client.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing summarize_and_confirm node")

    # Create a summary of collected information
    client = state.get("client", {})
    case = state.get("case", {})

    summary = "Here's a summary of the information you've provided:\n\n"

    if client.get("name"):
        summary += f"Name: {client.get('name')}\n"
    if client.get("email"):
        summary += f"Email: {client.get('email')}\n"
    if client.get("phone"):
        summary += f"Phone: {client.get('phone')}\n"

    summary += "\nCase Information:\n"
    if case.get("description"):
        summary += f"Description: {case.get('description')}\n"
    if case.get("case_type"):
        case_type_display = case.get("case_type", "").replace("_", " ").title()
        summary += f"Case Type: {case_type_display}\n"

    summary += "\nIs this information correct? If yes, I'll submit your intake form. If not, please let me know what needs to be corrected."

    state["messages"].append({
        "type": "ai",
        "content": summary
    })

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Check if the user confirmed
    if content and ("yes" in content.lower() or "correct" in content.lower() or "submit" in content.lower()):
        state["next"] = "save_client_info"

    return state


async def save_client_info(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Save client information to the database.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing save_client_info node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")
    user_id = configurable.get("user_id", "unknown")

    # Get client and case information
    client = state.get("client", {})
    case = state.get("case", {})

    if client.get("name") and case.get("description"):
        # Use the tool executor to save client info
        tool_executor = get_tool_executor()

        try:
            # Create client record
            client_data = {
                "tenant_id": tenant_id,
                "first_name": client.get("name", "").split()[0] if client.get("name") else "",
                "last_name": " ".join(client.get("name", "").split()[1:]) if client.get("name") and len(client.get("name", "").split()) > 1 else "",
                "email": client.get("email"),
                "phone_primary": client.get("phone"),
                "client_type": "individual",
                "created_by": user_id
            }

            client_result = await tool_executor.execute_tool(
                tool_name="create_client",
                tool_args={"client_data": client_data},
                tenant_id=tenant_id
            )

            if client_result and "id" in client_result:
                # Create case record
                case_data = {
                    "tenant_id": tenant_id,
                    "client_id": client_result["id"],
                    "title": f"{client.get('name')} - {case.get('case_type', 'Personal Injury')}",
                    "description": case.get("description"),
                    "case_type": case.get("case_type"),
                    "practice_area": case.get("practice_area", "personal_injury"),
                    "status": "pending",
                    "created_by": user_id
                }

                case_result = await tool_executor.execute_tool(
                    tool_name="create_case",
                    tool_args={"case_data": case_data},
                    tenant_id=tenant_id
                )

                if case_result and "id" in case_result:
                    # Success
                    state["messages"].append({
                        "type": "ai",
                        "content": "Thank you! Your intake information has been submitted successfully. One of our attorneys "
                                   "will review your case and contact you soon. Your case reference number is "
                                   f"{case_result['id']}."
                    })

                    # Store the IDs in state
                    state["client"]["id"] = client_result["id"]
                    state["case"]["id"] = case_result["id"]

                    # Set next to FINISH
                    state["next"] = "FINISH"
                else:
                    # Failed to create case
                    state["messages"].append({
                        "type": "ai",
                        "content": "I encountered an issue while creating your case. Please try again later or contact "
                                   "our office directly."
                    })
            else:
                # Failed to create client
                state["messages"].append({
                    "type": "ai",
                    "content": "I encountered an issue while saving your information. Please try again later or contact "
                               "our office directly."
                })
        except Exception as e:
            logger.error(f"Error saving client info: {str(e)}")
            state["messages"].append({
                "type": "ai",
                "content": "I encountered an error while saving your information. Please try again later or contact "
                           "our office directly."
            })
    else:
        # Missing required information
        state["messages"].append({
            "type": "ai",
            "content": "I'm missing some required information. Let's go back and make sure we have everything we need."
        })
        state["next"] = "initial_contact"

    return state
