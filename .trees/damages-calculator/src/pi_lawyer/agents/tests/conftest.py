"""
Pytest Configuration

This module provides fixtures and utilities for testing agents.
"""

import logging
from typing import Any, Dict, Optional

import pytest

# Set up logging
logger = logging.getLogger(__name__)


async def run_once(graph: Any, state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run a graph once until it finishes.
    
    This function executes a graph with the given state and returns the final state.
    It also adds a 'last_node' field to the state with the name of the last node executed.
    
    Args:
        graph: The compiled StateGraph to execute
        state: The initial state
        
    Returns:
        The final state with an additional 'last_node' field
    """
    logger.info("Running graph once")
    
    # Execute the graph
    try:
        result = await graph.ainvoke(state)
        
        # If the result doesn't have a last_node field, add it
        if "last_node" not in result:
            result["last_node"] = "finish"
        
        logger.info(f"Graph execution completed with last node: {result.get('last_node')}")
        return result
    
    except Exception as e:
        logger.error(f"Error executing graph: {str(e)}")
        raise


@pytest.fixture
def make_state():
    """
    Create a test state with optional parameters.
    
    Returns:
        A function that creates a test state
    """
    def _make_state(
        msg: Optional[str] = None,
        tenant: str = "tenant-123",
        user: str = "user-456",
        thread: str = "thread-789",
        page_intent: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a test state with the given parameters.
        
        Args:
            msg: The user message (if any)
            tenant: The tenant ID
            user: The user ID
            thread: The thread ID
            page_intent: The page intent (if any)
            
        Returns:
            A test state
        """
        state = {
            "tenant_id": tenant,
            "user_id": user,
            "thread_id": thread,
            "messages": [],
            "memory": {},
        }
        
        # Add the user message if provided
        if msg:
            state["messages"].append({"type": "human", "content": msg})
        
        # Add the page intent if provided
        if page_intent:
            state["page_intent"] = page_intent
        
        return state
    
    return _make_state
