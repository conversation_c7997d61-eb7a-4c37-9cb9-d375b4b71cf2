from copilotkit import CopilotKitSDK, LangGraphAgent
from fastapi import FastAP<PERSON>
from langgraph.graph import StateGraph
from pydantic import BaseModel

app = FastAPI()


class AgentState(BaseModel):
    messages: list = []
    current_step: str = "start"


# Define your workflow
workflow = StateGraph(AgentState)
workflow.add_node(
    "start", lambda x: {"messages": x.messages, "current_step": "process"}
)
workflow.add_node(
    "process", lambda x: {"messages": x.messages + ["Processed"], "current_step": "end"}
)
workflow.set_entry_point("start")

# Initialize CopilotKit SDK
sdk = CopilotKitSDK(
    agents=[
        LangGraphAgent(
            name="legal_assistant",
            description="AI legal assistant for managing cases and documents",
            graph=workflow.compile(),
        )
    ]
)

# Add the CopilotKit endpoint
sdk.add_fastapi_endpoint(app, "/copilotkit")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
