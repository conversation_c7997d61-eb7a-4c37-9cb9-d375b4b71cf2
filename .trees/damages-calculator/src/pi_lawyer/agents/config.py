"""
Agent Configuration System

This module provides a configuration system for LangGraph agents in the PI Lawyer AI system.
It uses Pydantic models for validation and supports both global and tenant-specific configurations.

Key Features:
- Pydantic models for configuration validation
- Support for global and tenant-specific configurations
- Runtime configuration changes
- Default configurations for different agent types
- Configuration loading from environment variables and files
- Configuration caching for performance

Usage:
    from pi_lawyer.agents.config import AgentConfig, get_agent_config

    # Get configuration for a specific agent
    config = get_agent_config("research_agent")

    # Create a custom configuration
    custom_config = AgentConfig(
        name="custom_agent",
        agent_type="research",
        description="Custom research agent",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.7
        )
    )

    # Get tenant-specific configuration
    tenant_config = get_agent_config("research_agent", tenant_id="tenant-123")
"""

import json
import logging
import os
from functools import lru_cache
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, field_validator

# Set up logging
logger = logging.getLogger(__name__)

# Define provider types
ProviderType = Literal["openai", "anthropic", "google", "azure", "voyage", "local"]


class LLMConfig(BaseModel):
    """
    Configuration for a language model.
    """

    provider: ProviderType = Field(
        default="openai",
        description="LLM provider"
    )
    model: str = Field(
        default="gpt-4",
        description="Model name"
    )
    temperature: float = Field(
        default=0.7,
        description="Temperature for sampling",
        ge=0.0,
        le=1.0
    )
    max_tokens: Optional[int] = Field(
        default=None,
        description="Maximum number of tokens to generate"
    )
    top_p: Optional[float] = Field(
        default=None,
        description="Top-p sampling parameter",
        ge=0.0,
        le=1.0
    )
    frequency_penalty: Optional[float] = Field(
        default=None,
        description="Frequency penalty parameter",
        ge=0.0,
        le=2.0
    )
    presence_penalty: Optional[float] = Field(
        default=None,
        description="Presence penalty parameter",
        ge=0.0,
        le=2.0
    )
    stop_sequences: Optional[List[str]] = Field(
        default=None,
        description="Sequences that trigger the model to stop generating"
    )
    timeout: Optional[int] = Field(
        default=None,
        description="Timeout in seconds for the API call"
    )
    api_key_env_var: Optional[str] = Field(
        default=None,
        description="Environment variable name for the API key"
    )
    api_base_url: Optional[str] = Field(
        default=None,
        description="Base URL for the API"
    )

    @property
    def api_key(self) -> Optional[str]:
        """Get the API key from the environment variable."""
        if self.api_key_env_var:
            return os.environ.get(self.api_key_env_var)

        # Try to get the API key from the default environment variable
        if self.provider == "openai":
            return os.environ.get("OPENAI_API_KEY")
        elif self.provider == "anthropic":
            return os.environ.get("ANTHROPIC_API_KEY")
        elif self.provider == "google":
            return os.environ.get("GOOGLE_API_KEY")
        elif self.provider == "azure":
            return os.environ.get("AZURE_OPENAI_API_KEY")
        elif self.provider == "voyage":
            return os.environ.get("VOYAGE_API_KEY")

        return None


class ToolConfig(BaseModel):
    """
    Configuration for a tool.
    """

    name: str = Field(
        description="Tool name"
    )
    enabled: bool = Field(
        default=True,
        description="Whether the tool is enabled"
    )
    description: Optional[str] = Field(
        default=None,
        description="Tool description"
    )
    parameters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Tool parameters"
    )


class AgentConfig(BaseModel):
    """
    Configuration for an agent.
    """

    name: str = Field(
        description="Agent name"
    )
    agent_type: str = Field(
        description="Agent type (e.g., research, intake, document)"
    )
    description: Optional[str] = Field(
        default=None,
        description="Agent description"
    )
    version: str = Field(
        default="1.0.0",
        description="Agent version"
    )
    llm_config: LLMConfig = Field(
        default_factory=LLMConfig,
        description="LLM configuration"
    )
    tools: List[ToolConfig] = Field(
        default_factory=list,
        description="Tool configurations"
    )
    system_prompt: Optional[str] = Field(
        default=None,
        description="System prompt for the agent"
    )
    max_iterations: int = Field(
        default=10,
        description="Maximum number of iterations for the agent"
    )
    max_execution_time: Optional[int] = Field(
        default=None,
        description="Maximum execution time in seconds"
    )
    streaming: bool = Field(
        default=True,
        description="Whether to stream responses"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )

    @field_validator("tools")
    @classmethod
    def validate_tools(cls, tools):
        """Validate that tool names are unique."""
        tool_names = [tool.name for tool in tools]
        if len(tool_names) != len(set(tool_names)):
            raise ValueError("Tool names must be unique")
        return tools

    def get_tool_config(self, tool_name: str) -> Optional[ToolConfig]:
        """Get configuration for a specific tool."""
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        return None

    def enable_tool(self, tool_name: str) -> bool:
        """Enable a tool."""
        for tool in self.tools:
            if tool.name == tool_name:
                tool.enabled = True
                return True
        return False

    def disable_tool(self, tool_name: str) -> bool:
        """Disable a tool."""
        for tool in self.tools:
            if tool.name == tool_name:
                tool.enabled = False
                return True
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert the configuration to a dictionary."""
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentConfig":
        """Create a configuration from a dictionary."""
        return cls(**data)

    @classmethod
    def from_json(cls, json_str: str) -> "AgentConfig":
        """Create a configuration from a JSON string."""
        return cls.from_dict(json.loads(json_str))

    def to_json(self) -> str:
        """Convert the configuration to a JSON string."""
        return json.dumps(self.to_dict())


# Default configurations for different agent types
DEFAULT_CONFIGS = {
    "research": AgentConfig(
        name="research_agent",
        agent_type="research",
        description="Legal research agent for finding relevant laws and cases",
        system_prompt="You are a legal research assistant specialized in finding relevant laws, statutes, and case precedents.",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.3
        ),
        tools=[
            ToolConfig(name="search_documents", description="Search for legal documents"),
            ToolConfig(name="enqueue_async_job", description="Enqueue an async job for complex research")
        ]
    ),
    "intake": AgentConfig(
        name="intake_agent",
        agent_type="intake",
        description="Legal intake agent for personal injury cases",
        system_prompt="You are a legal intake assistant specialized in gathering information for personal injury cases.",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.5
        ),
        tools=[
            ToolConfig(name="query_database", description="Query the database for client information"),
            ToolConfig(name="insert_database", description="Insert client information into the database"),
            ToolConfig(name="update_database", description="Update client information in the database"),
            ToolConfig(name="create_task", description="Create a task for the legal team")
        ]
    ),
    "document": AgentConfig(
        name="document_agent",
        agent_type="document",
        description="Legal document drafting agent for personal injury cases",
        system_prompt="You are a legal document drafting assistant specialized in creating documents for personal injury cases.",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.2
        ),
        tools=[
            ToolConfig(name="search_documents", description="Search for document templates"),
            ToolConfig(name="upload_document", description="Upload a completed document")
        ]
    ),
    "deadline": AgentConfig(
        name="deadline_agent",
        agent_type="deadline",
        description="Legal deadline extraction agent for identifying important deadlines in legal documents",
        system_prompt="You are a legal deadline extraction assistant specialized in identifying important deadlines in legal documents.",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.1
        ),
        tools=[
            ToolConfig(name="create_calendar_event", description="Create a calendar event for a deadline"),
            ToolConfig(name="create_task", description="Create a task for a deadline")
        ]
    ),
    "supervisor": AgentConfig(
        name="supervisor_agent",
        agent_type="supervisor",
        description="Legal assistant supervisor that routes to specialized agents",
        system_prompt="You are a legal assistant supervisor that routes user requests to specialized agents.",
        llm_config=LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.7
        ),
        tools=[]  # Supervisor has access to all tools
    )
}


# Global configuration cache
_config_cache: Dict[str, Dict[str, AgentConfig]] = {}


@lru_cache(maxsize=128)
def get_agent_config(agent_name: str, tenant_id: Optional[str] = None) -> AgentConfig:
    """
    Get configuration for a specific agent.

    Args:
        agent_name: Agent name or type
        tenant_id: Tenant ID for tenant-specific configuration

    Returns:
        Agent configuration
    """
    # Check if we have a tenant-specific configuration
    if tenant_id and tenant_id in _config_cache and agent_name in _config_cache[tenant_id]:
        return _config_cache[tenant_id][agent_name]

    # Check if we have a global configuration
    if "global" in _config_cache and agent_name in _config_cache["global"]:
        return _config_cache["global"][agent_name]

    # Check if we have a default configuration
    if agent_name in DEFAULT_CONFIGS:
        return DEFAULT_CONFIGS[agent_name]

    # Check if agent_name is an agent type
    for config in DEFAULT_CONFIGS.values():
        if config.agent_type == agent_name:
            return config

    # Create a default configuration
    logger.warning(f"No configuration found for agent '{agent_name}', creating a default configuration")
    return AgentConfig(
        name=agent_name,
        agent_type="custom",
        description=f"Custom agent '{agent_name}'",
        llm_config=LLMConfig()
    )


def set_agent_config(config: AgentConfig, tenant_id: Optional[str] = None) -> None:
    """
    Set configuration for a specific agent.

    Args:
        config: Agent configuration
        tenant_id: Tenant ID for tenant-specific configuration
    """
    tenant_id = tenant_id or "global"

    if tenant_id not in _config_cache:
        _config_cache[tenant_id] = {}

    _config_cache[tenant_id][config.name] = config

    # Clear the LRU cache to ensure the new configuration is used
    get_agent_config.cache_clear()

    logger.info(f"Set configuration for agent '{config.name}' (tenant: {tenant_id})")


def load_agent_configs_from_file(file_path: str, tenant_id: Optional[str] = None) -> Dict[str, AgentConfig]:
    """
    Load agent configurations from a JSON file.

    Args:
        file_path: Path to the JSON file
        tenant_id: Tenant ID for tenant-specific configuration

    Returns:
        Dictionary of agent configurations
    """
    try:
        with open(file_path, "r") as f:
            data = json.load(f)

        configs = {}
        for name, config_data in data.items():
            config = AgentConfig.from_dict(config_data)
            configs[name] = config
            set_agent_config(config, tenant_id)

        logger.info(f"Loaded {len(configs)} agent configurations from '{file_path}'")
        return configs
    except Exception as e:
        logger.error(f"Failed to load agent configurations from '{file_path}': {e}")
        return {}
