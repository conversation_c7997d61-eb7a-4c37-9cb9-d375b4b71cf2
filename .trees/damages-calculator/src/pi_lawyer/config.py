"""Configuration management for the PI Lawyer AI system."""
import os
from functools import lru_cache
from typing import Any, Dict

from dotenv import load_dotenv
from openai import Async<PERSON>penA<PERSON>


def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables."""
    load_dotenv()

    required_vars = [
        "OPENAI_API_KEY",
        "PINECONE_API_KEY",
        "PINECONE_ENVIRONMENT",
        "PINECONE_INDEX_NAME",
        "SUPABASE_URL",
        "SUPABASE_KEY",
    ]

    missing = [var for var in required_vars if not os.getenv(var)]
    if missing:
        raise ValueError(
            f"Missing required environment variables: {', '.join(missing)}"
        )

    return {
        "openai": {
            "api_key": os.getenv("OPENAI_API_KEY"),
            "models": {
                "default": os.getenv("OPENAI_MODEL", "gpt-4-0125-preview"),
                "embedding": os.getenv(
                    "OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"
                ),
                # Specific models for different nodes
                "query_generation": "gpt-4-0125-preview",  # Fast, good enough for queries
                "analysis": "gpt-4-0125-preview",  # Most capable for deep analysis
                "small_talk": "gpt-3.5-turbo-0125",  # Fast, good for chat
                "classification": "gpt-4-0125-preview",  # Accurate for routing
            },
        },
        "pinecone": {
            "api_key": os.getenv("PINECONE_API_KEY"),
            "environment": os.getenv("PINECONE_ENVIRONMENT"),
            "index_name": os.getenv("PINECONE_INDEX_NAME"),
        },
        "supabase": {
            "url": os.getenv("SUPABASE_URL"),
            "key": os.getenv("SUPABASE_KEY"),
        },
    }


@lru_cache()
def get_config() -> Dict[str, Any]:
    """Get cached configuration."""
    return load_config()


@lru_cache()
def get_openai_client() -> AsyncOpenAI:
    """Get cached OpenAI client."""
    config = get_config()
    return AsyncOpenAI(api_key=config["openai"]["api_key"])


def get_model_for_node(node_name: str) -> str:
    """Get the appropriate model for a specific node."""
    config = get_config()
    models = config["openai"]["models"]
    return models.get(node_name, models["default"])
