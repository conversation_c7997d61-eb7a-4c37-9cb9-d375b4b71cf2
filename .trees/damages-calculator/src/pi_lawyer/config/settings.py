"""
Settings module for the PI Lawyer AI system.

This module defines the application settings with enhanced validation,
type conversion, and configuration reporting. It loads environment variables
and provides default values for development and testing.
"""

import logging
from typing import List, Optional

from pydantic import BaseModel, Field, validator

from . import BaseSettings, JWTAlgorithm, LogLevel, _get_env_value, _get_required_env_value

logger = logging.getLogger("pi_lawyer.config.settings")


class OpenAISettings(BaseModel):
    """OpenAI API settings."""

    api_key: str = Field(
        default=_get_env_value("OPENAI_API_KEY", "sk-test123456789"),
        description="OpenAI API key"
    )
    model: str = Field(
        default=_get_env_value("OPENAI_MODEL", "gpt-4-1106-preview"),
        description="Default OpenAI model"
    )
    embedding_model: str = Field(
        default=_get_env_value("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"),
        description="OpenAI embedding model"
    )

    @validator("api_key")
    def validate_api_key(cls, v):
        """Validate OpenAI API key format."""
        if not v.startswith("sk-") and not cls._is_test_value(v):
            logger.warning("OpenAI API key should start with 'sk-'")
        return v

    @classmethod
    def _is_test_value(cls, value: str) -> bool:
        """Check if a value is a test value."""
        test_indicators = ["test", "example", "placeholder", "dummy"]
        return any(indicator in value.lower() for indicator in test_indicators)


class PineconeSettings(BaseModel):
    """Pinecone API settings."""

    api_key: str = Field(
        default=_get_env_value("PINECONE_API_KEY", "pcsk-test123456789"),
        description="Pinecone API key"
    )
    environment: str = Field(
        default=_get_env_value("PINECONE_ENVIRONMENT", "us-east-1-aws"),
        description="Pinecone environment"
    )
    index_name: str = Field(
        default=_get_env_value("PINECONE_INDEX_NAME", "test-index"),
        description="Pinecone index name"
    )

    @validator("api_key")
    def validate_api_key(cls, v):
        """Validate Pinecone API key format."""
        if not v.startswith("pcsk-") and not cls._is_test_value(v):
            logger.warning("Pinecone API key should start with 'pcsk-'")
        return v

    @classmethod
    def _is_test_value(cls, value: str) -> bool:
        """Check if a value is a test value."""
        test_indicators = ["test", "example", "placeholder", "dummy"]
        return any(indicator in value.lower() for indicator in test_indicators)


class SupabaseSettings(BaseModel):
    """Supabase API settings."""

    url: str = Field(
        default=_get_env_value("SUPABASE_URL", "https://test-project.supabase.co"),
        description="Supabase URL"
    )
    key: str = Field(
        default=_get_env_value("SUPABASE_SERVICE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test123456789"),
        description="Supabase service role key"
    )
    jwt_secret: str = Field(
        default=_get_required_env_value("SUPABASE_JWT_SECRET", "JWT secret"),
        description="Supabase JWT secret"
    )

    @validator("url")
    def validate_url(cls, v):
        """Validate Supabase URL format."""
        if not v.startswith("https://") and not cls._is_test_value(v):
            logger.warning("Supabase URL should start with 'https://'")
        return v

    @validator("jwt_secret")
    def validate_jwt_secret(cls, v):
        """Validate JWT secret length."""
        if len(v) < 32 and not cls._is_test_value(v):
            logger.warning("JWT secret should be at least 32 characters long")
        return v

    @classmethod
    def _is_test_value(cls, value: str) -> bool:
        """Check if a value is a test value."""
        test_indicators = ["test", "example", "placeholder", "dummy"]
        return any(indicator in value.lower() for indicator in test_indicators)


class JWTSettings(BaseModel):
    """JWT settings."""

    algorithm: JWTAlgorithm = Field(
        default=JWTAlgorithm(_get_env_value("JWT_ALGORITHM", "HS256", var_type=str)),
        description="JWT algorithm"
    )
    expiration_minutes: int = Field(
        default=_get_env_value("JWT_EXPIRATION_MINUTES", 60, var_type=int),
        description="JWT expiration time in minutes"
    )


class DatabaseSettings(BaseModel):
    """Database settings."""

    host: str = Field(
        default=_get_env_value("DB_HOST", "localhost"),
        description="Database host"
    )
    port: int = Field(
        default=_get_env_value("DB_PORT", 5432, var_type=int),
        description="Database port"
    )
    name: str = Field(
        default=_get_env_value("DB_NAME", "test_db"),
        description="Database name"
    )
    user: str = Field(
        default=_get_env_value("DB_USER", "test_user"),
        description="Database user"
    )
    password: str = Field(
        default=_get_env_value("DB_PASSWORD", "test_password"),
        description="Database password"
    )

    def get_database_url(self) -> str:
        """Get the database URL for SQLAlchemy."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class SecuritySettings(BaseModel):
    """Security settings."""

    enable_csp: bool = Field(
        default=_get_env_value("ENABLE_CSP", True, var_type=bool),
        description="Enable Content Security Policy"
    )
    content_security_policy: str = Field(
        default=_get_env_value(
            "CONTENT_SECURITY_POLICY",
            (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "font-src 'self'; "
                "connect-src 'self' https://*.supabase.co wss://*.supabase.co; "
                "frame-ancestors 'none'; "
                "form-action 'self'; "
                "base-uri 'self'; "
                "object-src 'none'"
            )
        ),
        description="Content Security Policy"
    )
    x_content_type_options: str = Field(
        default=_get_env_value("X_CONTENT_TYPE_OPTIONS", "nosniff"),
        description="X-Content-Type-Options header"
    )
    x_xss_protection: str = Field(
        default=_get_env_value("X_XSS_PROTECTION", "1; mode=block"),
        description="X-XSS-Protection header"
    )
    x_frame_options: str = Field(
        default=_get_env_value("X_FRAME_OPTIONS", "DENY"),
        description="X-Frame-Options header"
    )
    referrer_policy: str = Field(
        default=_get_env_value("REFERRER_POLICY", "strict-origin-when-cross-origin"),
        description="Referrer-Policy header"
    )
    permissions_policy: str = Field(
        default=_get_env_value("PERMISSIONS_POLICY", "camera=(), microphone=(), geolocation=()"),
        description="Permissions-Policy header"
    )


class RateLimitSettings(BaseModel):
    """Enhanced rate limiting settings for production security."""

    enable: bool = Field(
        default=_get_env_value("ENABLE_RATE_LIMIT", True, var_type=bool),
        description="Enable rate limiting"
    )

    # General API rate limiting
    limit: int = Field(
        default=_get_env_value("RATE_LIMIT", 100, var_type=int),
        description="General rate limit (requests per window)"
    )
    window: int = Field(
        default=_get_env_value("RATE_LIMIT_WINDOW", 60, var_type=int),
        description="Rate limit window in seconds"
    )

    # Authentication endpoint rate limiting (stricter)
    auth_limit: int = Field(
        default=_get_env_value("RATE_LIMIT_AUTH", 10, var_type=int),
        description="Authentication endpoint rate limit (requests per window)"
    )
    auth_window: int = Field(
        default=_get_env_value("RATE_LIMIT_AUTH_WINDOW", 300, var_type=int),
        description="Authentication rate limit window in seconds (5 minutes)"
    )

    # File upload rate limiting
    upload_limit: int = Field(
        default=_get_env_value("RATE_LIMIT_UPLOAD", 20, var_type=int),
        description="File upload rate limit (requests per window)"
    )
    upload_window: int = Field(
        default=_get_env_value("RATE_LIMIT_UPLOAD_WINDOW", 300, var_type=int),
        description="File upload rate limit window in seconds"
    )

    # AI/LLM endpoint rate limiting
    ai_limit: int = Field(
        default=_get_env_value("RATE_LIMIT_AI", 30, var_type=int),
        description="AI/LLM endpoint rate limit (requests per window)"
    )
    ai_window: int = Field(
        default=_get_env_value("RATE_LIMIT_AI_WINDOW", 60, var_type=int),
        description="AI/LLM rate limit window in seconds"
    )

    # Admin endpoint rate limiting
    admin_limit: int = Field(
        default=_get_env_value("RATE_LIMIT_ADMIN", 200, var_type=int),
        description="Admin endpoint rate limit (requests per window)"
    )
    admin_window: int = Field(
        default=_get_env_value("RATE_LIMIT_ADMIN_WINDOW", 60, var_type=int),
        description="Admin rate limit window in seconds"
    )


class CopilotKitSettings(BaseModel):
    """CopilotKit settings."""

    endpoint_secret: str = Field(
        default=_get_env_value("CPK_ENDPOINT_SECRET", "test-endpoint-secret-123456789"),
        description="CopilotKit endpoint secret for authenticating requests to the CopilotKit endpoint"
    )
    api_key: Optional[str] = Field(
        default=_get_env_value("COPILOTKIT_API_KEY", None),
        description="CopilotKit API key for accessing CopilotKit Cloud services"
    )
    cloud_agent_id_supervisor: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_SUPERVISOR", None),
        description="CopilotKit Cloud agent ID for supervisor agent"
    )
    cloud_agent_id_intake: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_INTAKE", None),
        description="CopilotKit Cloud agent ID for intake agent"
    )

    @validator("endpoint_secret")
    def validate_endpoint_secret(cls, v, values, **kwargs):
        """Validate the CopilotKit endpoint secret."""
        # Check if the endpoint secret is the default value
        if v == "test-endpoint-secret-123456789":
            logger.warning(
                "Using default CopilotKit endpoint secret. "
                "This is insecure and should only be used in development."
            )

        # Check if the endpoint secret is too short
        if v and len(v) < 16:
            logger.warning(
                "CopilotKit endpoint secret is too short. "
                "It should be at least 16 characters long for security."
            )

        return v
    cloud_agent_id_document: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_DOCUMENT", None),
        description="CopilotKit Cloud agent ID for document agent"
    )
    cloud_agent_id_research: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_RESEARCH", None),
        description="CopilotKit Cloud agent ID for research agent"
    )
    cloud_agent_id_deadline: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_DEADLINE", None),
        description="CopilotKit Cloud agent ID for deadline agent"
    )
    cloud_agent_id_event: Optional[str] = Field(
        default=_get_env_value("CLOUD_AGENT_ID_EVENT", None),
        description="CopilotKit Cloud agent ID for event agent"
    )


class MonitoringSettings(BaseModel):
    """Monitoring and tracing settings."""

    langsmith_api_key: Optional[str] = Field(
        default=_get_env_value("LANGSMITH_API_KEY", None),
        description="LangSmith API key"
    )
    langsmith_project: Optional[str] = Field(
        default=_get_env_value("LANGSMITH_PROJECT", None),
        description="LangSmith project name"
    )
    voyage_api_key: Optional[str] = Field(
        default=_get_env_value("VOYAGE_API_KEY", None),
        description="Voyage AI API key"
    )


class AGUISettings(BaseModel):
    """AG-UI protocol settings."""

    enabled: bool = Field(
        default=_get_env_value("NEXT_PUBLIC_AGUI_ENABLED", False, var_type=bool),
        description="Enable AG-UI protocol"
    )


class Settings(BaseSettings):
    """Application settings."""

    # Application settings
    debug: bool = _get_env_value("DEBUG", False, var_type=bool)
    log_level: LogLevel = _get_env_value("LOG_LEVEL", LogLevel.INFO, var_type=LogLevel)
    cors_origins: List[str] = _get_env_value("CORS_ORIGINS", ["http://localhost:3000"], var_type=List[str])
    port: int = _get_env_value("PORT", 8000, var_type=int)
    host: str = _get_env_value("HOST", "0.0.0.0")

    # API settings
    openai: OpenAISettings = OpenAISettings()
    pinecone: PineconeSettings = PineconeSettings()
    supabase: SupabaseSettings = SupabaseSettings()

    # Authentication settings
    jwt: JWTSettings = JWTSettings()

    # Database settings
    database: DatabaseSettings = DatabaseSettings()

    # Security settings
    security: SecuritySettings = SecuritySettings()

    # Rate limiting settings
    rate_limit: RateLimitSettings = RateLimitSettings()

    # CopilotKit settings
    copilotkit: CopilotKitSettings = CopilotKitSettings()

    # Monitoring settings
    monitoring: MonitoringSettings = MonitoringSettings()

    # AG-UI settings
    agui: AGUISettings = AGUISettings()

    def validate(self) -> List[str]:
        """
        Validate settings.

        Returns:
            List[str]: List of validation errors
        """
        errors = []

        # Validate port range
        if not 1 <= self.port <= 65535:
            errors.append(f"Port must be between 1 and 65535, got {self.port}")

        # Validate rate limit settings
        if self.rate_limit.enable and self.rate_limit.limit <= 0:
            errors.append(f"Rate limit must be positive, got {self.rate_limit.limit}")

        if self.rate_limit.enable and self.rate_limit.window <= 0:
            errors.append(f"Rate limit window must be positive, got {self.rate_limit.window}")

        return errors


# Create and validate settings
settings = Settings()

# Log configuration report (without sensitive values)
logger.info("Configuration loaded successfully")
logger.debug(f"Configuration: {settings.get_config_report()}")

# Export settings instance
__all__ = ["settings"]
