"""
Security Configuration Validator for Backend

Comprehensive validation system for all security-critical configuration settings
in the FastAPI backend application. This module provides validation, monitoring,
and health checking for security configurations to prevent misconfigurations
that could lead to security vulnerabilities.
"""

import os
import re
from enum import Enum
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SecurityConfigCategory(Enum):
    """Security Configuration Categories"""
    AUTHENTICATION = "authentication"
    DATABASE = "database"
    API_KEYS = "api_keys"
    SECURITY_HEADERS = "security_headers"
    CORS = "cors"
    JWT = "jwt"
    ENVIRONMENT = "environment"
    RATE_LIMITING = "rate_limiting"
    MONITORING = "monitoring"
    EXTERNAL_SERVICES = "external_services"


class ValidationSeverity(Enum):
    """Validation Severity Levels"""
    CRITICAL = "critical"    # Production blocking
    HIGH = "high"           # Security risk
    MEDIUM = "medium"       # Best practice violation
    LOW = "low"            # Recommendation
    INFO = "info"          # Informational


@dataclass
class ConfigValidationResult:
    """Configuration Validation Result"""
    key: str
    category: SecurityConfigCategory
    severity: ValidationSeverity
    is_valid: bool
    message: str
    recommendation: Optional[str] = None
    value: Optional[str] = None  # Masked for sensitive values
    required: bool = True


@dataclass
class SecurityConfigAudit:
    """Security Configuration Audit Report"""
    timestamp: str
    environment: str
    overall_status: str  # 'healthy' | 'warning' | 'critical'
    total_checks: int
    passed_checks: int
    failed_checks: int
    critical_issues: int
    high_issues: int
    medium_issues: int
    low_issues: int
    results: List[ConfigValidationResult]
    summary: Dict[str, Dict[str, int]]


class SecurityConfigValidator:
    """Security Configuration Validator"""
    
    # Security-Critical Configuration Registry
    SECURITY_CONFIG_REGISTRY = {
        SecurityConfigCategory.AUTHENTICATION: [
            {
                'key': 'SUPABASE_URL',
                'required': True,
                'description': 'Supabase project URL for authentication'
            },
            {
                'key': 'SUPABASE_SERVICE_KEY',
                'required': True,
                'description': 'Supabase service role key for server operations'
            },
            {
                'key': 'SUPABASE_JWT_SECRET',
                'required': True,
                'description': 'JWT secret for token validation'
            }
        ],
        
        SecurityConfigCategory.DATABASE: [
            {
                'key': 'DATABASE_URL',
                'required': True,
                'description': 'Database connection URL'
            },
            {
                'key': 'DB_HOST',
                'required': False,
                'description': 'Database host (if not using DATABASE_URL)'
            },
            {
                'key': 'DB_PORT',
                'required': False,
                'description': 'Database port'
            },
            {
                'key': 'DB_NAME',
                'required': False,
                'description': 'Database name'
            },
            {
                'key': 'DB_USER',
                'required': False,
                'description': 'Database username'
            },
            {
                'key': 'DB_PASSWORD',
                'required': False,
                'description': 'Database password'
            }
        ],
        
        SecurityConfigCategory.API_KEYS: [
            {
                'key': 'OPENAI_API_KEY',
                'required': True,
                'description': 'OpenAI API key for AI services'
            },
            {
                'key': 'VOYAGE_API_KEY',
                'required': True,
                'description': 'Voyage API key for embeddings'
            },
            {
                'key': 'PINECONE_API_KEY',
                'required': True,
                'description': 'Pinecone API key for vector database'
            },
            {
                'key': 'PINECONE_ENVIRONMENT',
                'required': True,
                'description': 'Pinecone environment'
            },
            {
                'key': 'PINECONE_INDEX_NAME',
                'required': True,
                'description': 'Pinecone index name'
            },
            {
                'key': 'CPK_ENDPOINT_SECRET',
                'required': True,
                'description': 'CopilotKit endpoint secret'
            },
            {
                'key': 'LANGSMITH_API_KEY',
                'required': False,
                'description': 'LangSmith API key for monitoring'
            }
        ],
        
        SecurityConfigCategory.SECURITY_HEADERS: [
            {
                'key': 'X_CONTENT_TYPE_OPTIONS',
                'required': False,
                'description': 'X-Content-Type-Options header value'
            },
            {
                'key': 'X_XSS_PROTECTION',
                'required': False,
                'description': 'X-XSS-Protection header value'
            },
            {
                'key': 'X_FRAME_OPTIONS',
                'required': False,
                'description': 'X-Frame-Options header value'
            },
            {
                'key': 'REFERRER_POLICY',
                'required': False,
                'description': 'Referrer-Policy header value'
            },
            {
                'key': 'PERMISSIONS_POLICY',
                'required': False,
                'description': 'Permissions-Policy header value'
            }
        ],
        
        SecurityConfigCategory.RATE_LIMITING: [
            {
                'key': 'ENABLE_RATE_LIMIT',
                'required': False,
                'description': 'Enable rate limiting'
            },
            {
                'key': 'RATE_LIMIT',
                'required': False,
                'description': 'Rate limit value'
            },
            {
                'key': 'RATE_LIMIT_WINDOW',
                'required': False,
                'description': 'Rate limit window'
            }
        ],
        
        SecurityConfigCategory.CORS: [
            {
                'key': 'CORS_ORIGINS',
                'required': False,
                'description': 'CORS allowed origins'
            }
        ],
        
        SecurityConfigCategory.MONITORING: [
            {
                'key': 'LOG_LEVEL',
                'required': False,
                'description': 'Application log level'
            }
        ]
    }
    
    @staticmethod
    def mask_sensitive_value(key: str, value: str) -> str:
        """Mask sensitive values for logging and display"""
        sensitive_patterns = ['key', 'secret', 'password', 'token', 'jwt', 'url']
        
        is_sensitive = any(pattern in key.lower() for pattern in sensitive_patterns)
        
        if not is_sensitive:
            return value
        
        if len(value) <= 8:
            return '****'
        
        return f"{value[:4]}...{value[-4:]}"
    
    @staticmethod
    def get_env_value(key: str) -> Optional[str]:
        """Get environment variable value safely"""
        return os.getenv(key)
    
    @staticmethod
    def create_validation_result(
        key: str,
        category: SecurityConfigCategory,
        severity: ValidationSeverity,
        is_valid: bool,
        message: str,
        required: bool,
        recommendation: Optional[str] = None,
        value: Optional[str] = None
    ) -> ConfigValidationResult:
        """Create validation result"""
        return ConfigValidationResult(
            key=key,
            category=category,
            severity=severity,
            is_valid=is_valid,
            message=message,
            recommendation=recommendation,
            value=SecurityConfigValidator.mask_sensitive_value(key, value) if value else None,
            required=required
        )
    
    @classmethod
    def validate_supabase_url(cls, value: Optional[str]) -> ConfigValidationResult:
        """Validate Supabase URL"""
        key = 'SUPABASE_URL'
        
        if not value:
            return cls.create_validation_result(
                key, SecurityConfigCategory.AUTHENTICATION, ValidationSeverity.CRITICAL,
                False, 'Supabase URL is required for authentication', True,
                'Set SUPABASE_URL to your Supabase project URL'
            )
        
        if not value.startswith('https://'):
            return cls.create_validation_result(
                key, SecurityConfigCategory.AUTHENTICATION, ValidationSeverity.HIGH,
                False, 'Supabase URL must use HTTPS for security', True,
                'Ensure your Supabase URL starts with https://', value
            )
        
        if '.supabase.co' not in value:
            return cls.create_validation_result(
                key, SecurityConfigCategory.AUTHENTICATION, ValidationSeverity.MEDIUM,
                False, 'Supabase URL format appears invalid', True,
                'Verify your Supabase project URL format', value
            )
        
        return cls.create_validation_result(
            key, SecurityConfigCategory.AUTHENTICATION, ValidationSeverity.INFO,
            True, 'Supabase URL is properly configured', True, None, value
        )
    
    @classmethod
    def validate_jwt_secret(cls, value: Optional[str]) -> ConfigValidationResult:
        """Validate JWT Secret"""
        key = 'SUPABASE_JWT_SECRET'
        
        if not value:
            return cls.create_validation_result(
                key, SecurityConfigCategory.JWT, ValidationSeverity.CRITICAL,
                False, 'JWT secret is required for token validation', True,
                'Set SUPABASE_JWT_SECRET from your Supabase project settings'
            )
        
        if len(value) < 32:
            return cls.create_validation_result(
                key, SecurityConfigCategory.JWT, ValidationSeverity.HIGH,
                False, 'JWT secret is too short (minimum 32 characters recommended)', True,
                'Use a longer, more secure JWT secret', value
            )
        
        # Check for common weak patterns
        if value in ['your-jwt-secret', 'secret', 'jwt-secret']:
            return cls.create_validation_result(
                key, SecurityConfigCategory.JWT, ValidationSeverity.CRITICAL,
                False, 'JWT secret appears to be a default/example value', True,
                'Generate a secure random JWT secret', value
            )
        
        return cls.create_validation_result(
            key, SecurityConfigCategory.JWT, ValidationSeverity.INFO,
            True, 'JWT secret is properly configured', True, None, value
        )
    
    @classmethod
    def validate_api_key(cls, key: str, value: Optional[str], required: bool = True) -> ConfigValidationResult:
        """Generic API key validation"""
        if not value:
            severity = ValidationSeverity.CRITICAL if required else ValidationSeverity.INFO
            message = f'{key} is required' if required else f'{key} is not set (optional)'
            return cls.create_validation_result(
                key, SecurityConfigCategory.API_KEYS, severity,
                not required, message, required,
                f'Set {key} from your service provider' if required else None
            )
        
        if len(value) < 20:
            return cls.create_validation_result(
                key, SecurityConfigCategory.API_KEYS, ValidationSeverity.HIGH,
                False, f'{key} appears too short', required,
                f'Verify your {key}', value
            )
        
        # OpenAI specific validation
        if key == 'OPENAI_API_KEY' and not value.startswith('sk-'):
            return cls.create_validation_result(
                key, SecurityConfigCategory.API_KEYS, ValidationSeverity.HIGH,
                False, 'OpenAI API key format appears invalid (should start with sk-)', required,
                'Verify your OpenAI API key format', value
            )
        
        return cls.create_validation_result(
            key, SecurityConfigCategory.API_KEYS, ValidationSeverity.INFO,
            True, f'{key} is properly configured', required, None, value
        )
    
    @classmethod
    def perform_security_config_audit(cls) -> SecurityConfigAudit:
        """Perform comprehensive security configuration audit"""
        timestamp = datetime.now().isoformat()
        environment = os.getenv('ENVIRONMENT', 'development')
        results: List[ConfigValidationResult] = []
        
        # Initialize summary counters
        summary = {}
        for category in SecurityConfigCategory:
            summary[category.value] = {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'critical_issues': 0
            }
        
        # Validate all registered configurations
        for category, configs in cls.SECURITY_CONFIG_REGISTRY.items():
            for config in configs:
                key = config['key']
                required = config['required']
                env_value = cls.get_env_value(key)
                
                # Use specific validators or generic validation
                if key == 'SUPABASE_URL':
                    result = cls.validate_supabase_url(env_value)
                elif key == 'SUPABASE_JWT_SECRET':
                    result = cls.validate_jwt_secret(env_value)
                elif key in ['OPENAI_API_KEY', 'VOYAGE_API_KEY', 'PINECONE_API_KEY', 'CPK_ENDPOINT_SECRET', 'LANGSMITH_API_KEY']:
                    result = cls.validate_api_key(key, env_value, required)
                else:
                    # Generic validation
                    if not env_value and required:
                        result = cls.create_validation_result(
                            key, category, ValidationSeverity.CRITICAL,
                            False, f'{key} is required but not set', required,
                            f'Set {key} environment variable'
                        )
                    else:
                        result = cls.create_validation_result(
                            key, category, ValidationSeverity.INFO,
                            True, f'{key} is configured' if env_value else f'{key} is not set (optional)', required,
                            None, env_value
                        )
                
                results.append(result)
                
                # Update summary
                summary[category.value]['total'] += 1
                if result.is_valid:
                    summary[category.value]['passed'] += 1
                else:
                    summary[category.value]['failed'] += 1
                    if result.severity == ValidationSeverity.CRITICAL:
                        summary[category.value]['critical_issues'] += 1
        
        # Calculate overall statistics
        total_checks = len(results)
        passed_checks = sum(1 for r in results if r.is_valid)
        failed_checks = total_checks - passed_checks
        critical_issues = sum(1 for r in results if r.severity == ValidationSeverity.CRITICAL and not r.is_valid)
        high_issues = sum(1 for r in results if r.severity == ValidationSeverity.HIGH and not r.is_valid)
        medium_issues = sum(1 for r in results if r.severity == ValidationSeverity.MEDIUM and not r.is_valid)
        low_issues = sum(1 for r in results if r.severity == ValidationSeverity.LOW and not r.is_valid)
        
        # Determine overall status
        if critical_issues > 0:
            overall_status = 'critical'
        elif high_issues > 0 or failed_checks > total_checks * 0.2:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        return SecurityConfigAudit(
            timestamp=timestamp,
            environment=environment,
            overall_status=overall_status,
            total_checks=total_checks,
            passed_checks=passed_checks,
            failed_checks=failed_checks,
            critical_issues=critical_issues,
            high_issues=high_issues,
            medium_issues=medium_issues,
            low_issues=low_issues,
            results=results,
            summary=summary
        )
    
    @staticmethod
    def get_security_config_summary(audit: SecurityConfigAudit) -> str:
        """Get security configuration validation summary"""
        summary = f"Security Configuration Status: {audit.overall_status.upper()}\n"
        summary += f"Total Checks: {audit.total_checks} | Passed: {audit.passed_checks} | Failed: {audit.failed_checks}\n"
        
        if audit.critical_issues > 0:
            summary += f"🚨 CRITICAL ISSUES: {audit.critical_issues} - Production deployment blocked\n"
        
        if audit.high_issues > 0:
            summary += f"⚠️  HIGH PRIORITY ISSUES: {audit.high_issues} - Security risks identified\n"
        
        if audit.overall_status == 'healthy':
            summary += "✅ All security-critical configurations are properly validated\n"
        
        return summary


# Convenience function for easy import
def perform_backend_security_audit() -> SecurityConfigAudit:
    """Perform backend security configuration audit"""
    return SecurityConfigValidator.perform_security_config_audit()
