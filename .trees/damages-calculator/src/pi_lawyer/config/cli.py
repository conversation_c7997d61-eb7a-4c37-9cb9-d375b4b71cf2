"""
Command-line interface for configuration management.

This module provides a command-line interface for configuration management,
including configuration reporting, validation, and environment variable checking.
"""

import argparse
import logging
import sys

from .utils import (
    check_required_variables,
    get_config_report,
    validate_environment_variables,
)

logger = logging.getLogger("pi_lawyer.config.cli")


def setup_logging(level: str = "INFO") -> None:
    """
    Set up logging for the CLI.
    
    Args:
        level: Logging level
    """
    logging.basicConfig(
        level=getattr(logging, level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Configuration management CLI")
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate a configuration report",
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate configuration",
    )
    
    parser.add_argument(
        "--check-required",
        action="store_true",
        help="Check required environment variables",
    )
    
    parser.add_argument(
        "--format",
        choices=["dict", "json", "table"],
        default="table",
        help="Output format for the configuration report",
    )
    
    parser.add_argument(
        "--include-sensitive",
        action="store_true",
        help="Include masked sensitive values in the configuration report",
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Logging level",
    )
    
    return parser.parse_args()


def generate_report(format: str, include_sensitive: bool) -> None:
    """
    Generate a configuration report.
    
    Args:
        format: Output format
        include_sensitive: Whether to include sensitive values
    """
    report = get_config_report(include_sensitive, format)
    
    if format == "dict":
        print(report)
    else:
        print(report)


def validate_config() -> bool:
    """
    Validate configuration.
    
    Returns:
        bool: True if validation passed, False otherwise
    """
    errors = validate_environment_variables()
    
    if errors:
        logger.error("Configuration validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False
    
    logger.info("Configuration validation passed")
    return True


def check_required() -> bool:
    """
    Check required environment variables.
    
    Returns:
        bool: True if all required variables are set, False otherwise
    """
    required_vars = [
        "OPENAI_API_KEY",
        "PINECONE_API_KEY",
        "PINECONE_ENVIRONMENT",
        "PINECONE_INDEX_NAME",
        "SUPABASE_URL",
        "SUPABASE_KEY",
    ]
    
    missing = check_required_variables(required_vars)
    
    if missing:
        logger.error("Missing required environment variables:")
        for var in missing:
            logger.error(f"  - {var}")
        return False
    
    logger.info("All required environment variables are set")
    return True


def main() -> int:
    """
    Main entry point for the CLI.
    
    Returns:
        int: Exit code
    """
    args = parse_args()
    setup_logging(args.log_level)
    
    if args.report:
        generate_report(args.format, args.include_sensitive)
    
    if args.validate:
        if not validate_config():
            return 1
    
    if args.check_required:
        if not check_required():
            return 1
    
    if not (args.report or args.validate or args.check_required):
        # If no action specified, show help
        generate_report("table", False)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
