"""
Utility functions for configuration management.

This module provides utility functions for configuration management,
including configuration reporting, validation, and type conversion.
"""

import json
import logging
import os
from typing import Any, Dict, List

from pydantic import BaseModel

from .settings import settings

logger = logging.getLogger("pi_lawyer.config.utils")


def get_config_report(include_sensitive: bool = False, format: str = "dict") -> Any:
    """
    Generate a configuration report.
    
    Args:
        include_sensitive: Whether to include sensitive values
        format: Output format (dict, json, or table)
        
    Returns:
        Configuration report in the specified format
    """
    report = settings.get_config_report(include_sensitive)
    
    if format == "dict":
        return report
    elif format == "json":
        return json.dumps(report, indent=2, default=str)
    elif format == "table":
        return _format_as_table(report)
    else:
        raise ValueError(f"Unsupported format: {format}")


def _format_as_table(data: Dict[str, Any], prefix: str = "") -> str:
    """
    Format a dictionary as a table.
    
    Args:
        data: The dictionary to format
        prefix: Prefix for nested keys
        
    Returns:
        str: The formatted table
    """
    lines = []
    max_key_length = max(len(f"{prefix}{key}") for key in data.keys()) if data else 0
    
    for key, value in sorted(data.items()):
        full_key = f"{prefix}{key}"
        
        if isinstance(value, dict):
            lines.append(f"{full_key}:")
            lines.append(_format_as_table(value, prefix=f"  {prefix}"))
        elif isinstance(value, (list, tuple)):
            if not value:
                lines.append(f"{full_key.ljust(max_key_length)} | []")
            else:
                lines.append(f"{full_key}:")
                for i, item in enumerate(value):
                    lines.append(f"  {prefix}{i}: {item}")
        elif isinstance(value, BaseModel):
            lines.append(f"{full_key}:")
            lines.append(_format_as_table(value.dict(), prefix=f"  {prefix}"))
        else:
            lines.append(f"{full_key.ljust(max_key_length)} | {value}")
    
    return "\n".join(lines)


def validate_environment_variables() -> List[str]:
    """
    Validate all environment variables.
    
    Returns:
        List[str]: List of validation errors
    """
    return settings.validate()


def check_required_variables(required_vars: List[str]) -> List[str]:
    """
    Check if required environment variables are set.
    
    Args:
        required_vars: List of required environment variables
        
    Returns:
        List[str]: List of missing variables
    """
    return [var for var in required_vars if not os.getenv(var)]


def get_missing_variables(required_vars: List[str]) -> List[str]:
    """
    Get a list of missing environment variables.
    
    Args:
        required_vars: List of required environment variables
        
    Returns:
        List[str]: List of missing variables
    """
    return check_required_variables(required_vars)


def get_database_url() -> str:
    """
    Get the database URL for SQLAlchemy.
    
    Returns:
        str: The database URL
    """
    return settings.database.get_database_url()


def get_environment_type() -> str:
    """
    Get the current environment type.
    
    Returns:
        str: The environment type (development, testing, staging, production)
    """
    return os.getenv("APP_ENV", "development")


def is_development() -> bool:
    """
    Check if the current environment is development.
    
    Returns:
        bool: True if the current environment is development
    """
    return get_environment_type() == "development"


def is_testing() -> bool:
    """
    Check if the current environment is testing.
    
    Returns:
        bool: True if the current environment is testing
    """
    return get_environment_type() == "testing"


def is_staging() -> bool:
    """
    Check if the current environment is staging.
    
    Returns:
        bool: True if the current environment is staging
    """
    return get_environment_type() == "staging"


def is_production() -> bool:
    """
    Check if the current environment is production.
    
    Returns:
        bool: True if the current environment is production
    """
    return get_environment_type() == "production"
