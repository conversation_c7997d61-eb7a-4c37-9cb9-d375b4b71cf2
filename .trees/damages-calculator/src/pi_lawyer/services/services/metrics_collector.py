"""
Metrics collection service for application monitoring and observability.

This service provides a unified interface for collecting and exposing
metrics across the application, with support for Prometheus-style
metrics export.
"""

import logging
import threading
from typing import Dict, Optional

# Attempt to import prometheus_client, but don't fail if it's not available
try:
    import prometheus_client
    from prometheus_client import Counter, Gauge, Histogram, Summary

    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    Service for collecting and exposing application metrics.

    Provides a unified interface for tracking counters, gauges, histograms,
    and summaries, with automatic export to Prometheus if available.
    """

    def __init__(self, prefix: str = "pi_lawyer"):
        """
        Initialize the metrics collector.

        Args:
            prefix: Namespace prefix for all metrics
        """
        self.prefix = prefix
        self.metrics_lock = threading.Lock()
        self.counters = {}
        self.gauges = {}
        self.histograms = {}
        self.summaries = {}

        logger.info(f"MetricsCollector initialized with prefix '{prefix}'")
        logger.info(
            f"Prometheus support: {'enabled' if PROMETHEUS_AVAILABLE else 'disabled'}"
        )

    def _format_metric_name(self, name: str) -> str:
        """Format a metric name with the prefix."""
        return f"{self.prefix}_{name}"

    def _format_tags(self, tags: Optional[Dict[str, str]]) -> Dict[str, str]:
        """Format tags dictionary, ensuring all values are strings."""
        if not tags:
            return {}
        return {k: str(v) for k, v in tags.items()}

    def increment(
        self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None
    ) -> None:
        """
        Increment a counter metric.

        Args:
            name: Name of the counter
            value: Amount to increment by
            tags: Optional tags to associate with this data point
        """
        formatted_name = self._format_metric_name(name)
        formatted_tags = self._format_tags(tags)

        with self.metrics_lock:
            if PROMETHEUS_AVAILABLE:
                # Use Prometheus Counter
                if formatted_name not in self.counters:
                    self.counters[formatted_name] = Counter(
                        formatted_name,
                        f"{formatted_name} counter",
                        list(formatted_tags.keys()) if formatted_tags else [],
                    )

                if formatted_tags:
                    self.counters[formatted_name].labels(**formatted_tags).inc(value)
                else:
                    self.counters[formatted_name].inc(value)
            else:
                # Basic in-memory tracking
                counter_key = (
                    formatted_name,
                    tuple(sorted(formatted_tags.items())) if formatted_tags else (),
                )
                if counter_key not in self.counters:
                    self.counters[counter_key] = 0
                self.counters[counter_key] += value

        logger.debug(
            f"Incremented counter {formatted_name} by {value}",
            extra={
                "metric_name": formatted_name,
                "value": value,
                "tags": formatted_tags,
            },
        )

    def gauge(
        self, name: str, value: float, tags: Optional[Dict[str, str]] = None
    ) -> None:
        """
        Set a gauge metric value.

        Args:
            name: Name of the gauge
            value: Current value to set
            tags: Optional tags to associate with this data point
        """
        formatted_name = self._format_metric_name(name)
        formatted_tags = self._format_tags(tags)

        with self.metrics_lock:
            if PROMETHEUS_AVAILABLE:
                # Use Prometheus Gauge
                if formatted_name not in self.gauges:
                    self.gauges[formatted_name] = Gauge(
                        formatted_name,
                        f"{formatted_name} gauge",
                        list(formatted_tags.keys()) if formatted_tags else [],
                    )

                if formatted_tags:
                    self.gauges[formatted_name].labels(**formatted_tags).set(value)
                else:
                    self.gauges[formatted_name].set(value)
            else:
                # Basic in-memory tracking
                gauge_key = (
                    formatted_name,
                    tuple(sorted(formatted_tags.items())) if formatted_tags else (),
                )
                self.gauges[gauge_key] = value

        logger.debug(
            f"Set gauge {formatted_name} to {value}",
            extra={
                "metric_name": formatted_name,
                "value": value,
                "tags": formatted_tags,
            },
        )

    def observe(
        self,
        name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
        bucket_type: str = "histogram",
    ) -> None:
        """
        Record an observation for a histogram or summary metric.

        Args:
            name: Name of the metric
            value: Value to record
            tags: Optional tags to associate with this data point
            bucket_type: Type of observation bucket ('histogram' or 'summary')
        """
        formatted_name = self._format_metric_name(name)
        formatted_tags = self._format_tags(tags)

        with self.metrics_lock:
            if PROMETHEUS_AVAILABLE:
                if bucket_type == "histogram":
                    # Use Prometheus Histogram
                    if formatted_name not in self.histograms:
                        self.histograms[formatted_name] = Histogram(
                            formatted_name,
                            f"{formatted_name} histogram",
                            list(formatted_tags.keys()) if formatted_tags else [],
                        )

                    if formatted_tags:
                        self.histograms[formatted_name].labels(
                            **formatted_tags
                        ).observe(value)
                    else:
                        self.histograms[formatted_name].observe(value)
                else:
                    # Use Prometheus Summary
                    if formatted_name not in self.summaries:
                        self.summaries[formatted_name] = Summary(
                            formatted_name,
                            f"{formatted_name} summary",
                            list(formatted_tags.keys()) if formatted_tags else [],
                        )

                    if formatted_tags:
                        self.summaries[formatted_name].labels(**formatted_tags).observe(
                            value
                        )
                    else:
                        self.summaries[formatted_name].observe(value)
            else:
                # Basic in-memory tracking (just store the last value)
                if bucket_type == "histogram":
                    hist_key = (
                        formatted_name,
                        tuple(sorted(formatted_tags.items())) if formatted_tags else (),
                    )
                    if hist_key not in self.histograms:
                        self.histograms[hist_key] = []
                    self.histograms[hist_key].append(value)
                    # Keep only the last 1000 observations to avoid memory issues
                    if len(self.histograms[hist_key]) > 1000:
                        self.histograms[hist_key] = self.histograms[hist_key][-1000:]
                else:
                    summ_key = (
                        formatted_name,
                        tuple(sorted(formatted_tags.items())) if formatted_tags else (),
                    )
                    if summ_key not in self.summaries:
                        self.summaries[summ_key] = []
                    self.summaries[summ_key].append(value)
                    # Keep only the last 1000 observations to avoid memory issues
                    if len(self.summaries[summ_key]) > 1000:
                        self.summaries[summ_key] = self.summaries[summ_key][-1000:]

        logger.debug(
            f"Recorded {bucket_type} observation {formatted_name}: {value}",
            extra={
                "metric_name": formatted_name,
                "value": value,
                "tags": formatted_tags,
                "type": bucket_type,
            },
        )

    def export_prometheus(self) -> str:
        """
        Export all metrics in Prometheus format.

        Returns:
            String with Prometheus-formatted metrics
        """
        if PROMETHEUS_AVAILABLE:
            return prometheus_client.generate_latest().decode("utf-8")
        else:
            # Simple text-based format similar to Prometheus
            lines = []

            # Export counters
            for key, value in self.counters.items():
                if isinstance(key, tuple):
                    name, tags_tuple = key
                    tags_str = "{" + ",".join(f'{k}="{v}"' for k, v in tags_tuple) + "}"
                    lines.append(f"# TYPE {name} counter")
                    lines.append(f"{name}{tags_str} {value}")
                else:
                    lines.append(f"# TYPE {key} counter")
                    lines.append(f"{key} {value}")

            # Export gauges
            for key, value in self.gauges.items():
                if isinstance(key, tuple):
                    name, tags_tuple = key
                    tags_str = "{" + ",".join(f'{k}="{v}"' for k, v in tags_tuple) + "}"
                    lines.append(f"# TYPE {name} gauge")
                    lines.append(f"{name}{tags_str} {value}")
                else:
                    lines.append(f"# TYPE {key} gauge")
                    lines.append(f"{key} {value}")

            # Export histograms (simplified as just count/sum)
            for key, values in self.histograms.items():
                if isinstance(key, tuple):
                    name, tags_tuple = key
                    tags_str = "{" + ",".join(f'{k}="{v}"' for k, v in tags_tuple) + "}"
                    lines.append(f"# TYPE {name} histogram")
                    lines.append(f"{name}_count{tags_str} {len(values)}")
                    lines.append(f"{name}_sum{tags_str} {sum(values)}")
                else:
                    lines.append(f"# TYPE {key} histogram")
                    lines.append(f"{key}_count {len(values)}")
                    lines.append(f"{key}_sum {sum(values)}")

            # Export summaries (simplified as just count/sum)
            for key, values in self.summaries.items():
                if isinstance(key, tuple):
                    name, tags_tuple = key
                    tags_str = "{" + ",".join(f'{k}="{v}"' for k, v in tags_tuple) + "}"
                    lines.append(f"# TYPE {name} summary")
                    lines.append(f"{name}_count{tags_str} {len(values)}")
                    lines.append(f"{name}_sum{tags_str} {sum(values)}")
                else:
                    lines.append(f"# TYPE {key} summary")
                    lines.append(f"{key}_count {len(values)}")
                    lines.append(f"{key}_sum {sum(values)}")

            return "\n".join(lines)

    def reset(self) -> None:
        """Reset all metrics to initial state."""
        with self.metrics_lock:
            if PROMETHEUS_AVAILABLE:
                # For Prometheus, we need to clear registry and recreate collectors
                for counter in self.counters.values():
                    if hasattr(counter, "_metrics"):
                        counter._metrics.clear()

                for gauge in self.gauges.values():
                    if hasattr(gauge, "_metrics"):
                        gauge._metrics.clear()

                for hist in self.histograms.values():
                    if hasattr(hist, "_metrics"):
                        hist._metrics.clear()

                for summ in self.summaries.values():
                    if hasattr(summ, "_metrics"):
                        summ._metrics.clear()
            else:
                # For in-memory, just clear the dictionaries
                self.counters.clear()
                self.gauges.clear()
                self.histograms.clear()
                self.summaries.clear()

        logger.info("All metrics have been reset")


# Create a singleton instance
_metrics_instance = None


def get_metrics_collector(prefix: str = "pi_lawyer") -> MetricsCollector:
    """
    Get the global MetricsCollector instance.

    Args:
        prefix: Namespace prefix for metrics

    Returns:
        Singleton instance of MetricsCollector
    """
    global _metrics_instance

    if _metrics_instance is None:
        _metrics_instance = MetricsCollector(prefix=prefix)

    return _metrics_instance
