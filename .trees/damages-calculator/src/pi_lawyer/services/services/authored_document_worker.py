"""
Authored Document Worker

This worker handles the asynchronous processing of authored documents for embedding,
allowing them to be searchable alongside uploaded documents.
"""

import asyncio
import logging
import os
import time
import uuid
from typing import Any, Dict

from pi_lawyer.utils.error_classifier import ErrorClassifier

from .authored_document_embedding_service import get_authored_document_embedding_service
from .document_processing_queue import DocumentProcessingQueue
from .document_processing_transaction import TransactionError
from .locking_service import DocumentLockingService

logger = logging.getLogger(__name__)


class AuthoredDocumentWorker:
    """
    Worker for processing authored documents from the queue and
    generating embeddings for them.
    """

    def __init__(
        self,
        worker_id: str = None,
        poll_interval: int = 5,
        max_retries: int = 3,
        queue_type: str = "redis",
    ):
        """
        Initialize the authored document worker.

        Args:
            worker_id: Unique identifier for this worker
            poll_interval: Seconds to wait between polling the queue
            max_retries: Maximum number of retry attempts for failed jobs
            queue_type: Type of queue to use ("redis" or "memory")
        """
        self.worker_id = worker_id or f"authored-doc-worker-{uuid.uuid4().hex[:8]}"
        self.poll_interval = poll_interval
        self.max_retries = max_retries
        self.running = False

        # Initialize services
        self.queue_service = DocumentProcessingQueue(
            queue_name="authored_documents", queue_type=queue_type
        )
        self.embedding_service = get_authored_document_embedding_service()
        self.lock_service = DocumentLockingService()

    def start(self):
        """Start the worker to process jobs from the queue."""
        self.running = True
        logger.info(f"Authored document worker {self.worker_id} started")

        while self.running:
            try:
                # Poll for the next job with tenant isolation
                job = self.queue_service.dequeue_job()

                if not job:
                    # No job available, wait for a bit
                    time.sleep(self.poll_interval)
                    continue

                # Extract job information
                job_id = job["id"]
                authored_document_id = job["authored_document_id"]
                tenant_id = job["tenant_id"]
                retry_count = job.get("retry_count", 0)

                logger.info(
                    f"Processing authored document job: {job_id} for document {authored_document_id}"
                )

                # Acquire a lock on the document to prevent concurrent processing
                lock_owner_id = f"{self.worker_id}_{uuid.uuid4().hex[:8]}"
                document_lock_name = f"authored_document_{authored_document_id}"

                if not self.lock_service.acquire_lock(
                    document_lock_name, lock_owner_id, 600
                ):  # 10 minute timeout
                    logger.warning(
                        f"Failed to acquire lock for authored document {authored_document_id}, will retry later"
                    )
                    # Put the job back in the queue for later processing
                    self.queue_service.requeue_job(job_id)
                    time.sleep(1)  # Small delay before next attempt
                    continue

                # Update job status
                self.queue_service.update_job_status(
                    job_id,
                    {
                        "message": "Starting authored document embedding",
                        "progress": 10,
                        "worker_id": self.worker_id,
                    },
                )

                try:
                    # Process the authored document
                    self._process_authored_document(job)

                except TransactionError as te:
                    # Handle transaction-specific errors
                    logger.error(f"Transaction error in job {job_id}: {str(te)}")

                    can_retry = retry_count < self.max_retries
                    should_retry = "retry" in str(te).lower()

                    # Mark job as failed (with retry if appropriate)
                    self.queue_service.fail_job(
                        job_id=job_id,
                        error=f"Transaction error: {str(te)}",
                        retry=should_retry and can_retry,
                    )

                except Exception as e:
                    # Handle general processing errors
                    error_message = str(e)
                    error_type = type(e).__name__
                    logger.error(
                        f"Error processing authored document {authored_document_id}: {error_message}",
                        exc_info=True,
                    )

                    # Determine if we should retry
                    can_retry = retry_count < self.max_retries
                    should_retry = not (
                        "not found" in error_message.lower()
                        or "permission" in error_message.lower()
                        or "access denied" in error_message.lower()
                    )

                    # Log with error classification
                    ErrorClassifier.log_error_with_classification(
                        e,
                        {
                            "job_id": job_id,
                            "authored_document_id": authored_document_id,
                            "tenant_id": tenant_id,
                            "retry_count": retry_count,
                            "max_retries": self.max_retries,
                            "can_retry": can_retry and should_retry,
                        },
                    )

                    # Mark job as failed (with retry if appropriate)
                    self.queue_service.fail_job(
                        job_id=job_id,
                        error=f"{error_type}: {error_message}",
                        retry=should_retry and can_retry,
                    )
                finally:
                    # Always release the document lock
                    self.lock_service.release_lock(document_lock_name, lock_owner_id)

            except Exception as e:
                logger.error(
                    f"Unexpected error in worker loop: {str(e)}", exc_info=True
                )
                # Sleep briefly to avoid tight error loops
                time.sleep(1)

        logger.info(f"Authored document worker {self.worker_id} stopped")

    def stop(self):
        """Stop the worker gracefully."""
        self.running = False

    def _process_authored_document(self, job: Dict[str, Any]):
        """
        Process an authored document job.

        Args:
            job: Job data dictionary
        """
        job_id = job["id"]
        authored_document_id = job["authored_document_id"]
        tenant_id = job["tenant_id"]
        case_id = job.get("case_id")
        client_id = job.get("client_id")
        user_id = job.get("user_id")
        document_version = job.get("version", 1)
        document_status = job.get("document_status", "draft")
        metadata = job.get("metadata", {})

        # Update job status
        self.queue_service.update_job_status(
            job_id,
            {"message": "Processing authored document for embedding", "progress": 30},
        )

        # Process the authored document
        result = asyncio.run(
            self.embedding_service.process_authored_document(
                authored_document_id=authored_document_id,
                tenant_id=tenant_id,
                case_id=case_id,
                client_id=client_id,
                user_id=user_id,
                document_version=document_version,
                document_status=document_status,
                metadata=metadata,
            )
        )

        # Mark job as completed
        self.queue_service.complete_job(
            job_id,
            {
                "embedding_status": result["status"],
                "chunk_count": result.get("chunk_count", 0),
                "transaction_id": result.get("transaction_id"),
                "transaction_status": "committed",
            },
        )

        logger.info(
            f"Job {job_id} for authored document {authored_document_id} completed successfully"
        )


def main():
    """Entry point for running worker as a standalone process."""
    worker_id = os.getenv("WORKER_ID", f"authored-doc-worker-{os.getpid()}")
    worker = AuthoredDocumentWorker(worker_id)

    logger.info(f"Starting authored document worker {worker_id}")
    worker.start()


if __name__ == "__main__":
    main()
