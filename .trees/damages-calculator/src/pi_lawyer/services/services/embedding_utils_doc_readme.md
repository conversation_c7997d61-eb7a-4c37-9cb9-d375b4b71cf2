# Document Embedding Utilities

This document provides an overview of the utility functions used for handling document embedding workflows in the application.

## Functions

### `queue_authored_document_for_embedding`

**Description**: Queues an authored document for embedding and updates its metadata.

**Parameters**:
- `authored_document_id` (str): ID of the authored document to embed.
- `tenant_id` (str): ID of the tenant owning the document.
- `user_id` (str): ID of the user who triggered the embedding.
- `document_data` (Dict[str, Any]): Document data including case_id, client_id, status, metadata, etc.
- `trigger_reason` (str): Reason for triggering the embedding (default: "Manual trigger").
- `source` (str): Source of the embedding request (default: "api").
- `priority` (int): Processing priority (default: 1).
- `update_embedding_status` (bool): Whether to update the document's embedding_status field (default: True).
- `supabase_client`: Optional Supabase client instance.

**Returns**: Dictionary with embedding job information.

---

### `should_embed_document`

**Description**: Determines if a document should be embedded based on its status and metadata.

**Parameters**:
- `document_data` (Dict[str, Any]): Document data including status and metadata.
- `metadata_update` (Optional[Dict[str, Any]]): Optional new metadata that is being applied to the document.

**Returns**: Tuple of (should_embed, reason) indicating whether the document should be embedded and the reason why.
