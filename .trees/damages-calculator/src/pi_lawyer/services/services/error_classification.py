"""
Error classification system for document processing.

This module provides tools to categorize errors and determine appropriate
retry strategies based on the error type.
"""

import logging
from enum import Enum
from typing import Any, Dict

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Categories of errors for retry policy decisions."""

    TRANSIENT = "transient"  # Temporary errors that should be retried
    RATE_LIMIT = "rate_limit"  # API rate limit errors
    VALIDATION = "validation"  # Data validation errors
    AUTHORIZATION = "authorization"  # Auth/permission errors
    RESOURCE = "resource"  # Resource not found
    SERVER = "server"  # Server errors
    CLIENT = "client"  # Client errors
    UNKNOWN = "unknown"  # Unclassified errors


class ErrorClassifier:
    """Classifies errors to determine retry policies."""

    @staticmethod
    def classify_error(error: Exception) -> ErrorCategory:
        """
        Classify an error into a category for retry decisions.

        Args:
            error: The exception to classify

        Returns:
            ErrorCategory indicating the type of error
        """
        error_type = type(error).__name__
        error_msg = str(error).lower()

        # OpenAI API errors
        if "openai" in error_type.lower():
            if "rate limit" in error_msg or "too many requests" in error_msg:
                return ErrorCategory.RATE_LIMIT
            if "invalid" in error_msg or "bad request" in error_msg:
                return ErrorCategory.VALIDATION
            if "unauthorized" in error_msg or "authentication" in error_msg:
                return ErrorCategory.AUTHORIZATION
            if "not found" in error_msg:
                return ErrorCategory.RESOURCE
            if "server error" in error_msg or any(
                code in error_msg for code in ["500", "502", "503", "504"]
            ):
                return ErrorCategory.SERVER
            if "timeout" in error_msg or "connection" in error_msg:
                return ErrorCategory.TRANSIENT

        # Pinecone errors
        if "pinecone" in error_type.lower():
            if "rate" in error_msg or "quota" in error_msg or "limit" in error_msg:
                return ErrorCategory.RATE_LIMIT
            if "timeout" in error_msg or "connection" in error_msg:
                return ErrorCategory.TRANSIENT
            if "invalid" in error_msg or "not found" in error_msg:
                return ErrorCategory.VALIDATION
            if "unauthorized" in error_msg or "forbidden" in error_msg:
                return ErrorCategory.AUTHORIZATION

        # Supabase/database errors
        if any(
            db_term in error_type.lower()
            for db_term in ["sql", "db", "database", "supabase"]
        ):
            if "connection" in error_msg or "timeout" in error_msg:
                return ErrorCategory.TRANSIENT
            if "constraint" in error_msg or "duplicate" in error_msg:
                return ErrorCategory.VALIDATION
            if "permission" in error_msg or "access" in error_msg:
                return ErrorCategory.AUTHORIZATION

        # General connection errors
        if any(
            net_term in error_type.lower()
            for net_term in ["timeout", "connection", "network", "socket"]
        ):
            return ErrorCategory.TRANSIENT

        # Default classification
        return ErrorCategory.UNKNOWN

    @staticmethod
    def should_retry(error: Exception) -> bool:
        """
        Determine if an error should be retried.

        Args:
            error: The exception to evaluate

        Returns:
            True if the error should be retried, False otherwise
        """
        category = ErrorClassifier.classify_error(error)
        return category in [
            ErrorCategory.TRANSIENT,
            ErrorCategory.RATE_LIMIT,
            ErrorCategory.SERVER,
        ]

    @staticmethod
    def get_retry_delay(error: Exception, retry_count: int) -> int:
        """
        Get recommended retry delay in seconds based on error type and retry count.

        Args:
            error: The exception that occurred
            retry_count: Number of previous retry attempts

        Returns:
            Recommended delay in seconds before next retry
        """
        category = ErrorClassifier.classify_error(error)
        base_delay = 5

        if category == ErrorCategory.RATE_LIMIT:
            # Longer delays for rate limits - exponential with added buffer
            return base_delay * (2**retry_count) + 30
        elif category == ErrorCategory.TRANSIENT:
            # Standard exponential backoff
            return base_delay * (2**retry_count)
        elif category == ErrorCategory.SERVER:
            # Medium delays for server errors
            return base_delay * (2**retry_count) + 10
        else:
            # Default delay
            return base_delay * retry_count

    @staticmethod
    def log_error_with_classification(
        error: Exception, context: Dict[str, Any] = None
    ) -> None:
        """
        Log an error with its classification for better error tracking.

        Args:
            error: The exception to log
            context: Additional context information about when the error occurred
        """
        category = ErrorClassifier.classify_error(error)
        error_type = type(error).__name__
        error_msg = str(error)

        log_data = {
            "error_type": error_type,
            "error_message": error_msg,
            "error_category": category.value,
            "should_retry": ErrorClassifier.should_retry(error),
        }

        if context:
            log_data.update(context)

        context_str = (
            ", ".join(f"{k}={v}" for k, v in context.items()) if context else ""
        )
        logger.error(
            f"Error [{category.value}]: {error_type} - {error_msg} {context_str}",
            extra=log_data,
        )
