"""
Document processing queue service for asynchronous document analysis.
"""

import asyncio
import logging
import os
import queue
import threading
import time
import traceback
import uuid
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from .circuit_breaker import CircuitOpenError, with_circuit_breaker
from .document_analysis_service import DocumentAnalysisService
from .document_classifier_service import DocumentClassifierService
from .document_parser_service import DocumentParserService
from .document_processing_transaction import (
    DocumentProcessingTransaction,
    TransactionError,
)
from .error_classification import ErrorClassifier
from .tenant_document_embedding_service import TenantDocumentEmbeddingService

# Optional imports based on configuration
try:
    from .redis_lock_service import LockAcquisitionError, get_redis_lock_service
    from .redis_queue_service import get_redis_queue_service

    REDIS_AVAILABLE = True
except (ImportError, ModuleNotFoundError):
    REDIS_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentProcessingQueue:
    """
    Service to manage asynchronous document processing tasks.
    Implements a worker thread pool pattern for background processing.

    This service can operate in two modes:
    1. In-memory queue (legacy) - Uses Python's PriorityQueue with worker threads
    2. Redis-based persistent queue - Uses Redis for distributed, persistent queuing
    """

    def __init__(
        self,
        num_workers: int = None,
        use_redis: bool = None,
        queue_name: str = "documents",
    ):
        """
        Initialize the document processing queue.

        Args:
            num_workers: Number of worker threads to spawn for parallel processing.
                         If None, uses the PROCESSING_WORKERS environment variable (default: 2).
            use_redis: Whether to use Redis for queueing. If None, determined by QUEUE_BACKEND env var.
        """
        # Determine queue backend to use
        self.use_redis = use_redis
        if self.use_redis is None:
            queue_backend = os.getenv("QUEUE_BACKEND", "").lower()
            self.use_redis = queue_backend == "redis" and REDIS_AVAILABLE

        # Set the queue name (for Redis mode)
        self.queue_name = queue_name

        # Set up the appropriate queue backend
        if self.use_redis and REDIS_AVAILABLE:
            logger.info(
                f"Using Redis-based persistent queue for {self.queue_name} processing"
            )
            self.redis_queue = get_redis_queue_service(queue_name=self.queue_name)

            # In Redis mode, we still provide in-memory job status caching for efficiency
            self.job_status = {}
            self.status_lock = threading.Lock()

            # Lock service for coordination
            self.lock_service = get_redis_lock_service(namespace=self.queue_name)

            # We don't need worker threads for Redis mode (separate worker processes handle this)
            self.num_workers = 0
            self.workers = []
            self.is_running = True  # Always "running" in Redis mode
        else:
            # Legacy in-memory queue mode
            logger.info(f"Using in-memory queue for {self.queue_name} processing")
            self.queue = queue.PriorityQueue()
            self.num_workers = (
                num_workers
                if num_workers is not None
                else int(os.getenv("PROCESSING_WORKERS", "2"))
            )
            self.workers = []
            self.is_running = False
            self.job_status = {}  # Track status of jobs by ID
            self.status_lock = threading.Lock()

        # Initialize services
        self.classifier = DocumentClassifierService()
        self.analyzer = DocumentAnalysisService()
        self.parser = DocumentParserService()
        self.embedding_service = TenantDocumentEmbeddingService()
        self.transaction_manager = DocumentProcessingTransaction()

        # Retry configuration
        self.max_retries = int(os.getenv("PROCESSING_MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("PROCESSING_RETRY_DELAY", "5"))  # seconds

    def start(self):
        """Start the queue processing workers."""
        if self.is_running:
            logger.warning("Document processing queue is already running")
            return

        # Redis mode doesn't need worker threads (handled by separate processes)
        if self.use_redis:
            self.is_running = True
            logger.info(
                "Redis queue mode active - worker processes should be started separately"
            )
            return

        # Legacy in-memory queue mode with worker threads
        self.is_running = True

        # Start worker threads
        for i in range(self.num_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                daemon=True,  # Make workers daemon threads so they exit when main thread exits
            )
            worker.start()
            self.workers.append(worker)

        logger.info(f"Started {self.num_workers} document processing workers")

    def stop(self):
        """Stop the queue processing."""
        if not self.use_redis:
            self.is_running = False

            # Wait for workers to finish (with timeout)
            for worker in self.workers:
                worker.join(timeout=2.0)

            logger.info("Document processing queue stopped")
        else:
            logger.info(
                "Document processing queue in Redis mode - workers managed separately"
            )

    def enqueue_document(
        self,
        document_id: str,
        file_path: str,
        file_name: str,
        file_type: str,
        tenant_id: str,
        user_id: str,
        priority: int = 1,
        metadata: Optional[Dict[str, Any]] = None,
        callback: Optional[Callable] = None,
    ) -> str:
        """
        Add a document to the processing queue.

        Args:
            document_id: Unique identifier for the document
            file_path: Path to the document file
            file_name: Original filename
            file_type: MIME type of the file
            tenant_id: ID of the tenant owning the document
            user_id: ID of the user who uploaded the document
            priority: Processing priority (lower number = higher priority)
            metadata: Optional metadata about the document
            callback: Optional callback function to call when processing completes

        Returns:
            job_id: Unique identifier for the queued job
        """
        # Create job object (common fields for both queue backends)
        job_data = {
            "document_id": document_id,
            "file_path": file_path,
            "file_name": file_name,
            "file_type": file_type,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat(),
        }

        # Handle queue submission based on backend
        if not self.use_redis:
            # In-memory queue can store callback functions
            job_id = str(uuid.uuid4())
            job_data["id"] = job_id
            job_data["callback"] = callback

            # Track job status
            with self.status_lock:
                self.job_status[job_id] = {
                    "status": "queued",
                    "document_id": document_id,
                    "created_at": job_data["created_at"],
                    "updated_at": job_data["created_at"],
                    "progress": 0,
                    "message": "Waiting in queue",
                }

            # Add to in-memory queue with priority
            self.queue.put((priority, job_data))

        else:
            # Redis-based queue (can't store callback functions)
            if callback:
                logger.warning(
                    "Callbacks not supported in Redis queue mode - will be ignored"
                )

            # Use Redis queue service to enqueue job
            job_id = self.redis_queue.enqueue(job_data, priority)

            # Cache job status locally for quicker access
            with self.status_lock:
                self.job_status[job_id] = {
                    "status": "queued",
                    "document_id": document_id,
                    "created_at": job_data["created_at"],
                    "updated_at": job_data["created_at"],
                    "progress": 0,
                    "message": "Waiting in queue",
                }

        logger.info(
            f"Enqueued document {document_id} with job ID {job_id} (priority: {priority})"
        )
        return job_id

    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of a job.

        Args:
            job_id: ID of the job to check

        Returns:
            Dictionary with job status information
        """
        # Try the in-memory cache first for better performance
        with self.status_lock:
            status = self.job_status.get(job_id)

        # If not in memory cache (or Redis mode), check Redis
        if not status and self.use_redis:
            redis_status = self.redis_queue.get_job_status(job_id)
            if redis_status:
                # Update local cache for future queries
                with self.status_lock:
                    self.job_status[job_id] = redis_status
                return redis_status

        if not status:
            return {"status": "not_found", "message": f"Job ID {job_id} not found"}

        return status

    def get_document_jobs(self, document_id: str) -> List[Dict[str, Any]]:
        """
        Get all jobs associated with a document.

        Args:
            document_id: ID of the document

        Returns:
            List of job status dictionaries
        """
        if self.use_redis:
            # Use Redis to find all jobs for this document
            document_jobs = self.redis_queue.get_document_jobs(document_id)

            # Update local cache
            with self.status_lock:
                for job in document_jobs:
                    job_id = job.get("id")
                    if job_id:
                        self.job_status[job_id] = job

            return document_jobs

        # Legacy in-memory mode
        jobs = []
        with self.status_lock:
            for job_id, status in self.job_status.items():
                if status.get("document_id") == document_id:
                    jobs.append({**status, "job_id": job_id})

        return jobs

    def _worker_loop(self, worker_id: int):
        """
        Main worker loop that processes documents from the queue.

        Args:
            worker_id: Identifier for this worker thread
        """
        logger.info(f"Document processing worker {worker_id} started")

        while self.is_running:
            try:
                # Get job from queue with 1 second timeout
                try:
                    priority, job = self.queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                job_id = job["id"]
                document_id = job["document_id"]

                # Update status to processing
                self._update_job_status(
                    job_id,
                    {
                        "status": "processing",
                        "message": "Document processing started",
                        "progress": 10,
                        "started_at": datetime.now().isoformat(),
                    },
                )

                logger.info(
                    f"Worker {worker_id} processing document {document_id} (job: {job_id})"
                )

                # Process document with transaction-based error handling and retries
                document_id = job["document_id"]
                tenant_id = job["tenant_id"]
                retry_count = 0
                transaction_id = None

                # Create a circuit breaker for API-bound operations
                circuit_breaker = self._get_circuit_breaker(f"worker_{worker_id}")

                while retry_count <= self.max_retries:
                    transaction_id = None
                    try:
                        # Start a new transaction for this processing job
                        transaction_id = asyncio.run(
                            self.transaction_manager.start(document_id, tenant_id)
                        )
                        logger.info(
                            f"Started transaction {transaction_id} for job {job_id} (document {document_id})"
                        )

                        self._update_job_status(
                            job_id,
                            {
                                "transaction_id": transaction_id,
                                "message": "Processing started with transaction management",
                                "retry_count": retry_count if retry_count > 0 else None,
                            },
                        )

                        # Step 1: Classify document with circuit breaker protection
                        self._update_job_status(
                            job_id, {"message": "Classifying document", "progress": 20}
                        )

                        # Wrap the classifier call with circuit breaker protection
                        try:
                            classification = self._process_with_circuit_breaker(
                                lambda: asyncio.run(
                                    self.classifier.classify_document(
                                        job["file_path"],
                                        job["file_name"],
                                        job["file_type"],
                                        job["metadata"],
                                    )
                                ),
                                circuit_name=f"classifier_{tenant_id}",
                            )
                        except CircuitOpenError as circuit_err:
                            logger.error(
                                f"Circuit open for classifier service: {str(circuit_err)}"
                            )
                            raise TransactionError(
                                f"Classification service unavailable: {str(circuit_err)}"
                            )

                        # Register the classification result with the transaction
                        asyncio.run(
                            self.transaction_manager.register_resource(
                                transaction_id=transaction_id,
                                resource_type="classification",
                                resource_id=f"{document_id}_classification",
                                location="memory",
                                metadata={
                                    "document_type": classification["document_type"],
                                    "analysis_type": classification["analysis_type"],
                                    "processing_path": classification.get(
                                        "processing_path", "simple"
                                    ),
                                },
                            )
                        )

                        self._update_job_status(
                            job_id,
                            {
                                "message": f"Document classified as {classification['document_type']}",
                                "progress": 30,
                                "classification": classification,
                            },
                        )

                        # Determine processing path
                        processing_path = job["metadata"].get(
                            "processing_path", "simple"
                        )

                        # If complex path, perform document parsing with Gemini
                        parsing_result = None
                        if processing_path == "complex":
                            try:
                                from supabase import Client, create_client

                                supabase: Client = create_client(
                                    os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
                                    os.getenv(
                                        "SUPABASE_SERVICE_KEY",
                                        os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", ""),
                                    ),
                                )

                                # Update status for parsing
                                self._update_job_status(
                                    job_id,
                                    {
                                        "message": "Parsing document with AI to extract structured information",
                                        "progress": 35,
                                    },
                                )

                                # Update document status in database
                                supabase.table("tenants.documents").update(
                                    {"ai_parsing_status": "processing"}
                                ).eq("id", document_id).execute()

                                # Parse document with appropriate schema
                                custom_schema = job["metadata"].get(
                                    "custom_schema", None
                                )
                                parsing_result = self._process_with_circuit_breaker(
                                    lambda: self.parser.parse_document(
                                        job["file_path"],
                                        classification["document_type"],
                                        custom_schema,
                                    ),
                                    circuit_name=f"parser_{tenant_id}",
                                )

                                # Save parsing result to database
                                supabase.table("tenants.documents").update(
                                    {
                                        "ai_parsing_status": "complete",
                                        "ai_parsing_result": parsing_result,
                                        "parsed_at": datetime.now().isoformat(),
                                    }
                                ).eq("id", document_id).execute()

                                logger.info(
                                    f"Document {document_id} successfully parsed with AI"
                                )

                            except Exception as parsing_error:
                                logger.error(
                                    f"Error parsing document with AI: {str(parsing_error)}"
                                )
                                # Update parsing status to error
                                try:
                                    supabase.table("tenants.documents").update(
                                        {
                                            "ai_parsing_status": "error",
                                            "ai_parsing_error": str(parsing_error),
                                        }
                                    ).eq("id", document_id).execute()
                                except Exception as db_error:
                                    logger.error(
                                        f"Error updating parsing status: {str(db_error)}"
                                    )

                                # Continue with normal processing despite parsing error
                                self._update_job_status(
                                    job_id,
                                    {
                                        "message": "AI parsing failed, continuing with standard analysis",
                                        "error": str(parsing_error),
                                    },
                                )

                        # Step 2: Analyze document
                        self._update_job_status(
                            job_id,
                            {
                                "message": f"Analyzing document with {classification['analysis_type']} analyzer",
                                "progress": 40,
                            },
                        )

                        # Choose analysis method based on classification
                        if classification["analysis_type"] == "medical":
                            result = self.analyzer.analyze_medical_form(
                                job["file_path"]
                            )
                        elif classification["analysis_type"] == "tasks":
                            result = self.analyzer.extract_tasks_from_document(
                                job["file_path"]
                            )
                        else:
                            result = self.analyzer.analyze_legal_document(
                                job["file_path"], classification["document_type"]
                            )

                        # Register the analysis result with the transaction
                        asyncio.run(
                            self.transaction_manager.register_resource(
                                transaction_id=transaction_id,
                                resource_type="analysis_result",
                                resource_id=f"{document_id}_analysis",
                                location="memory",
                                metadata={
                                    "document_id": document_id,
                                    "analysis_type": classification["analysis_type"],
                                },
                            )
                        )

                        self._update_job_status(
                            job_id,
                            {
                                "message": "Document analysis completed",
                                "progress": 50,
                                "analysis_completed": True,
                            },
                        )

                        # Step 3: Store analysis results in database
                        self._update_job_status(
                            job_id,
                            {"message": "Storing analysis results", "progress": 60},
                        )

                        # NOTE: Storage implementation is handled by a separate service
                        # Here we would call methods to store the analysis results and register them with the transaction

                        # Step 4: Generate embeddings for vector search with circuit breaker protection
                        self._update_job_status(
                            job_id,
                            {
                                "message": "Generating text embeddings for semantic search",
                                "progress": 70,
                            },
                        )

                        # Extract text content from the analysis result
                        if isinstance(result, dict) and "text" in result:
                            text_content = result["text"]
                        elif isinstance(result, str):
                            text_content = result
                        else:
                            # Try to convert to string if it's another format
                            text_content = str(result)

                        # Prepare document metadata for embeddings
                        doc_metadata = {
                            **job["metadata"],
                            "document_id": document_id,
                            "document_type": classification["document_type"],
                            "analysis_type": classification["analysis_type"],
                            "file_name": job["file_name"],
                            "tenant_id": job["tenant_id"],
                            "case_id": job["metadata"].get("case_id"),
                            "user_id": job["user_id"],
                            "transaction_id": transaction_id,  # Track which transaction created these embeddings
                        }

                        # Update status for embedding generation
                        self._update_job_status(
                            job_id,
                            {
                                "message": "Chunking document and generating embeddings",
                                "progress": 80,
                            },
                        )

                        # Use circuit breaker to protect embedding service call
                        try:
                            embedding_result = self._process_with_circuit_breaker(
                                lambda: asyncio.run(
                                    self.embedding_service.process_document(
                                        document_id=document_id,
                                        text_content=text_content,
                                        tenant_id=job["tenant_id"],
                                        document_metadata=doc_metadata,
                                        transaction_id=transaction_id,  # Pass transaction ID to enable rollback if needed
                                    )
                                ),
                                circuit_name=f"embedding_{tenant_id}",
                            )
                        except CircuitOpenError as circuit_err:
                            logger.error(
                                f"Circuit open for embedding service: {str(circuit_err)}"
                            )
                            raise TransactionError(
                                f"Embedding service unavailable: {str(circuit_err)}"
                            )

                        # Update status with embedding results
                        self._update_job_status(
                            job_id,
                            {
                                "message": f"Generated {embedding_result['chunk_count']} embeddings for semantic search",
                                "progress": 90,
                                "embedding_result": {
                                    "chunk_count": embedding_result["chunk_count"],
                                    "vector_count": embedding_result["vector_count"],
                                    "pool_namespace": embedding_result[
                                        "pool_namespace"
                                    ],
                                },
                            },
                        )

                        logger.info(
                            f"Document {document_id} embedding completed with {embedding_result['chunk_count']} chunks"
                        )

                        # Commit the transaction since everything succeeded
                        transaction_result = asyncio.run(
                            self.transaction_manager.commit(transaction_id)
                        )
                        logger.info(
                            f"Transaction {transaction_id} committed successfully for document {document_id}"
                        )

                        # Step 5: Complete job
                        complete_message = "Document processing completed successfully"
                        if job["metadata"].get("processing_path") == "complex":
                            complete_message = "Document processing completed successfully (complex path with AI parsing)"

                        self._update_job_status(
                            job_id,
                            {
                                "status": "completed",
                                "message": complete_message,
                                "progress": 100,
                                "completed_at": datetime.now().isoformat(),
                                "result": {
                                    "document_type": classification["document_type"],
                                    "analysis_type": classification["analysis_type"],
                                    "processing_path": job["metadata"].get(
                                        "processing_path", "simple"
                                    ),
                                    "embedding_status": "completed",
                                    "transaction_id": transaction_id,
                                    "transaction_status": "committed",
                                },
                            },
                        )

                        # Execute callback if provided
                        if job["callback"] and callable(job["callback"]):
                            try:
                                job["callback"](
                                    document_id, job_id, "completed", result
                                )
                            except Exception as cb_error:
                                logger.error(
                                    f"Error in callback for job {job_id}: {str(cb_error)}"
                                )

                        # Success! Break out of retry loop
                        break

                    except TransactionError as te:
                        # Transaction-specific errors should trigger rollback
                        logger.error(
                            f"Transaction error processing document {document_id}: {str(te)}",
                            exc_info=True,
                        )

                        if transaction_id:
                            # Attempt rollback
                            try:
                                asyncio.run(
                                    self.transaction_manager.rollback(transaction_id)
                                )
                                logger.info(
                                    f"Successfully rolled back transaction {transaction_id}"
                                )
                            except Exception as rollback_error:
                                logger.error(
                                    f"Error rolling back transaction {transaction_id}: {str(rollback_error)}",
                                    exc_info=True,
                                )

                        # Retry logic for transaction errors
                        retry_count += 1
                        if retry_count <= self.max_retries:
                            delay = self.retry_delay * retry_count
                            logger.warning(
                                f"Retrying job {job_id} in {delay} seconds (attempt {retry_count}/{self.max_retries})"
                            )
                            self._update_job_status(
                                job_id,
                                {
                                    "message": f"Retrying after transaction error: {str(te)} (attempt {retry_count}/{self.max_retries})",
                                    "retry_count": retry_count,
                                    "last_error": str(te),
                                    "transaction_status": "rolled_back",
                                },
                            )
                            time.sleep(delay)
                        else:
                            # Too many retries, mark as failed
                            self._update_job_status(
                                job_id,
                                {
                                    "status": "failed",
                                    "message": f"Failed after {self.max_retries} retries: {str(te)}",
                                    "error": str(te),
                                    "completed_at": datetime.now().isoformat(),
                                    "transaction_status": "failed",
                                },
                            )
                            logger.error(
                                f"Job {job_id} failed after {self.max_retries} retries"
                            )

                    except Exception as e:
                        # General processing errors
                        error_message = str(e)
                        stack_trace = traceback.format_exc()
                        logger.error(
                            f"Error processing document {document_id}: {error_message}"
                        )
                        logger.error(stack_trace)

                        # If we have a transaction, register the error and roll back
                        if transaction_id:
                            try:
                                error_type = type(e).__name__
                                asyncio.run(
                                    self.transaction_manager.register_error(
                                        transaction_id=transaction_id,
                                        error_type=error_type,
                                        error_message=error_message,
                                        step="document_processing",
                                    )
                                )
                                asyncio.run(
                                    self.transaction_manager.rollback(transaction_id)
                                )
                                logger.info(
                                    f"Registered error and rolled back transaction {transaction_id}"
                                )
                            except Exception as rollback_error:
                                logger.error(
                                    f"Error during error registration/rollback: {str(rollback_error)}"
                                )

                        # Retry logic for general errors
                        retry_count += 1
                        if retry_count <= self.max_retries:
                            delay = self.retry_delay * retry_count
                            logger.warning(
                                f"Retrying job {job_id} in {delay} seconds (attempt {retry_count}/{self.max_retries})"
                            )
                            self._update_job_status(
                                job_id,
                                {
                                    "message": f"Retrying after error: {error_message} (attempt {retry_count}/{self.max_retries})",
                                    "retry_count": retry_count,
                                    "last_error": error_message,
                                },
                            )
                            time.sleep(delay)
                        else:
                            # Too many retries, mark as failed
                            self._update_job_status(
                                job_id,
                                {
                                    "status": "failed",
                                    "message": f"Failed after {self.max_retries} retries",
                                    "error": error_message,
                                    "stack_trace": stack_trace,
                                    "completed_at": datetime.now().isoformat(),
                                },
                            )
                # After all retry attempts, mark the task as done
                if not self.use_redis:
                    self.queue.task_done()

            except Exception as e:
                logger.error(
                    f"Unexpected error in worker {worker_id}: {str(e)}", exc_info=True
                )
                # Sleep briefly to avoid tight error loops
                time.sleep(1)

        logger.info(f"Document processing worker {worker_id} stopped")

    def _update_job_status(self, job_id: str, update: Dict[str, Any]):
        """
        Update the status of a job.

        Args:
            job_id: ID of the job to update
            update: Dictionary with status fields to update
        """
        # Update in-memory status
        with self.status_lock:
            if job_id in self.job_status:
                status = self.job_status[job_id]
                status.update(update)
                status["updated_at"] = datetime.now().isoformat()
                self.job_status[job_id] = status

        # Update Redis job status if using Redis
        if self.use_redis:
            self.redis_queue.update_job_status(job_id, update)

    def _get_circuit_breaker(self, name: str = "default") -> CircuitBreaker:
        """
        Get or create a circuit breaker instance for the given name.

        Args:
            name: Identifier for this circuit breaker

        Returns:
            CircuitBreaker instance
        """
        from pi_lawyer.utils.circuit_breaker import CircuitBreaker

        # Configure based on service being protected
        if name.startswith("classifier"):
            # Classifier service has more tolerance for failures
            return CircuitBreaker(
                name=name,
                failure_threshold=5,
                reset_timeout=30,  # 30 seconds
                half_open_timeout=15,  # 15 seconds
                excluded_exceptions=[
                    ValueError,
                    TypeError,
                ],  # These are client errors, not service failures
            )
        elif name.startswith("embedding"):
            # Vector embedding services need more protection
            return CircuitBreaker(
                name=name,
                failure_threshold=3,
                reset_timeout=60,  # 1 minute
                half_open_timeout=20,  # 20 seconds
                excluded_exceptions=[ValueError],  # Only exclude validation errors
            )
        else:
            # Default settings for other services
            return CircuitBreaker(
                name=name,
                failure_threshold=3,
                reset_timeout=45,  # 45 seconds
                half_open_timeout=15,  # 15 seconds
            )

    def _process_with_circuit_breaker(
        self, func: Callable, *args, circuit_name: str = "default", **kwargs
    ):
        """
        Execute a processing function with circuit breaker protection.

        Args:
            func: Function to execute
            circuit_name: Name of the circuit breaker to use
            *args, **kwargs: Arguments to pass to the function

        Returns:
            Result of the function

        Raises:
            CircuitOpenError: If the circuit is open due to too many failures
            Any exceptions raised by the wrapped function
        """
        circuit_breaker = self._get_circuit_breaker(circuit_name)

        # Use the circuit breaker to protect the call
        return circuit_breaker.call(func, *args, **kwargs)

    # Add Redis queue-specific methods
    def get_queue_stats(self) -> Dict[str, int]:
        """
        Get statistics about queue state.

        Returns:
            Dictionary with queue statistics
        """
        if self.use_redis:
            return self.redis_queue.get_queue_stats()
        else:
            # For in-memory queue, just return queue size
            return {
                "active": self.queue.qsize(),
                "processing": sum(
                    1
                    for status in self.job_status.values()
                    if status.get("status") == "processing"
                ),
                "failed": sum(
                    1
                    for status in self.job_status.values()
                    if status.get("status") == "failed"
                ),
                "completed": sum(
                    1
                    for status in self.job_status.values()
                    if status.get("status") == "completed"
                ),
                "total": len(self.job_status),
            }

    def requeue_stalled_jobs(self, timeout_minutes: int = 30) -> List[str]:
        """
        Find and requeue jobs that have been processing for too long.

        Args:
            timeout_minutes: How long a job can be processing before considered stalled

        Returns:
            List of requeued job IDs
        """
        if self.use_redis:
            return self.redis_queue.requeue_stalled_jobs(timeout_minutes)
        else:
            # Not applicable for in-memory queue
            logger.warning(
                "Requeuing stalled jobs not supported in in-memory queue mode"
            )
            return []

    @with_circuit_breaker("document_processing")
    def _process_with_circuit_breaker(self, func: Callable, *args, **kwargs):
        """
        Execute a processing function with circuit breaker protection.

        Args:
            func: Function to execute
            *args, **kwargs: Arguments to pass to the function

        Returns:
            Result of the function
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Classify the error to determine retry strategy
            error_category = ErrorClassifier.classify_error(e)
            error_message = f"[{error_category.value}] {str(e)}"

            # Re-raise with improved error info
            raise type(e)(error_message) from e

    def enqueue_authored_document(
        self,
        authored_document_id: str,
        tenant_id: str,
        user_id: str,
        case_id: Optional[str] = None,
        client_id: Optional[str] = None,
        document_version: int = 1,
        document_status: str = "draft",
        priority: int = 1,
        metadata: Optional[Dict[str, Any]] = None,
        callback: Optional[Callable] = None,
    ) -> str:
        """
        Add an authored document to the processing queue for embedding.

        Args:
            authored_document_id: Unique identifier for the authored document
            tenant_id: ID of the tenant owning the document
            user_id: ID of the user who authored the document
            case_id: Optional case ID related to the document
            client_id: Optional client ID related to the document
            document_version: Version number of the document
            document_status: Status of the document (draft, final, etc.)
            priority: Processing priority (lower number = higher priority)
            metadata: Optional metadata about the document
            callback: Optional callback function to call when processing completes

        Returns:
            job_id: Unique identifier for the queued job
        """
        # Create job object (common fields for both queue backends)
        job_data = {
            "authored_document_id": authored_document_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "case_id": case_id,
            "client_id": client_id,
            "version": document_version,
            "document_status": document_status,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat(),
        }

        # Handle queue submission based on backend
        if not self.use_redis:
            # In-memory queue can store callback functions
            job_id = str(uuid.uuid4())
            job_data["id"] = job_id
            job_data["callback"] = callback

            # Track job status
            with self.status_lock:
                self.job_status[job_id] = {
                    "status": "queued",
                    "authored_document_id": authored_document_id,
                    "created_at": job_data["created_at"],
                    "updated_at": job_data["created_at"],
                    "progress": 0,
                    "message": "Waiting in queue",
                }

            # Add to in-memory queue with priority
            self.queue.put((priority, job_data))

        else:
            # Redis-based queue (can't store callback functions)
            if callback:
                logger.warning(
                    "Callbacks not supported in Redis queue mode - will be ignored"
                )

            # Enqueue job in Redis
            job_id = self.redis_queue.enqueue_job(job_data, priority=priority)

            # Cache current status
            with self.status_lock:
                self.job_status[job_id] = {
                    "status": "queued",
                    "authored_document_id": authored_document_id,
                    "created_at": job_data["created_at"],
                    "updated_at": job_data["created_at"],
                    "progress": 0,
                    "message": "Waiting in queue",
                    "tenant_id": tenant_id,  # Important for tenant isolation
                }

        logger.info(
            f"Authored document {authored_document_id} enqueued for embedding with job ID {job_id}"
        )
        return job_id


# Create singleton instances
_document_queue_instance = None
_authored_document_queue_instance = None


def get_document_processing_queue(use_redis: bool = None):
    """
    Get the global document processing queue instance.

    Args:
        use_redis: Whether to use Redis-based queue. If None, determined by environment.

    Returns:
        Singleton instance of DocumentProcessingQueue for regular documents
    """
    global _document_queue_instance
    if _document_queue_instance is None:
        _document_queue_instance = DocumentProcessingQueue(
            use_redis=use_redis, queue_name="documents"
        )
        _document_queue_instance.start()
    return _document_queue_instance


def get_authored_document_queue(use_redis: bool = None):
    """
    Get the global authored document processing queue instance.

    Args:
        use_redis: Whether to use Redis-based queue. If None, determined by environment.

    Returns:
        Singleton instance of DocumentProcessingQueue for authored documents
    """
    global _authored_document_queue_instance
    if _authored_document_queue_instance is None:
        _authored_document_queue_instance = DocumentProcessingQueue(
            use_redis=use_redis, queue_name="authored_documents"
        )
        _authored_document_queue_instance.start()
    return _authored_document_queue_instance
