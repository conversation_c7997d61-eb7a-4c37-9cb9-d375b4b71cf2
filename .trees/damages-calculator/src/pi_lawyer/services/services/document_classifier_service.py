"""
Document classifier service using Gemini to determine the optimal analysis type.

This service analyzes documents to classify their type, determine the best analysis approach,
and recommend a processing path (simple or complex) based on the document's structure and content.
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, Optional

import google.generativeai as genai

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Document types
DOCUMENT_TYPES = {
    "MEDICAL": "medical_record",
    "LEGAL": "court_filing",
    "CONTRACT": "contract",
    "GENERAL": "general",
}

# Analysis types
ANALYSIS_TYPES = {"TEXT": "text", "MEDICAL": "medical", "TASKS": "tasks"}

# Processing paths
PROCESSING_PATHS = {"SIMPLE": "simple", "COMPLEX": "complex"}

# Document types that typically require complex processing
COMPLEX_DOCUMENT_TYPES = {
    "medical_record",
    "medical_form",
    "invoice",
    "structured_form",
    "financial_statement",
}


class DocumentClassifierService:
    """Service to classify documents and determine the optimal analysis type."""

    def __init__(self):
        """Initialize the document classifier service."""
        api_key = os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY environment variable not set")

        genai.configure(api_key=api_key)

        # Get the primary Gemini model with fallback
        self.primary_model_name = "gemini-2.0-flash-lite-001"
        self.fallback_model_name = "gemini-1.5-flash"

        try:
            # Try primary model first
            self.model = genai.GenerativeModel(self.primary_model_name)
            logger.info(
                f"Initialized primary Gemini model '{self.primary_model_name}' for document classification"
            )
            self.using_fallback = False
        except Exception as primary_error:
            logger.warning(
                f"Failed to initialize primary model: {str(primary_error)}. Trying fallback..."
            )
            try:
                # Fallback to secondary model
                self.model = genai.GenerativeModel(self.fallback_model_name)
                logger.info(
                    f"Initialized fallback Gemini model '{self.fallback_model_name}' for document classification"
                )
                self.using_fallback = True
            except Exception as fallback_error:
                logger.error(
                    f"Failed to initialize fallback model: {str(fallback_error)}"
                )
                raise ValueError(
                    f"Could not initialize either primary or fallback Gemini models: {str(primary_error)} | {str(fallback_error)}"
                )

    async def classify_document(
        self,
        file_path: str,
        file_name: str,
        file_type: str,
        document_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Classify a document and determine the optimal analysis type.

        Args:
            file_path: Path to the document file
            file_name: Original filename
            file_type: MIME type of the file
            document_metadata: Optional metadata about the document (case info, etc.)

        Returns:
            Dictionary with classification results including document_type and analysis_type
        """
        try:
            # Prepare the prompt
            prompt = """
            Analyze this document and classify it into one of these document types:
            - MEDICAL: Medical records, health forms, prescriptions, medical reports
            - LEGAL: Court filings, legal motions, judgments, affidavits
            - CONTRACT: Agreements, policies, correspondence, insurance documents
            - GENERAL: General documents, letters, articles, research

            Then determine the best analysis type:
            - TEXT: General text extraction and structure preservation
            - MEDICAL: Medical form analysis with structured data extraction
            - TASKS: Extract action items, deadlines, and responsibilities

            Also determine if this document needs complex processing:
            - SIMPLE: Plain text documents without forms, tables, or special structure
            - COMPLEX: Forms, tables, structured data, or documents requiring field extraction

            Return your answer as a valid JSON object with these fields:
            {
                "document_type": "One of MEDICAL, LEGAL, CONTRACT, or GENERAL",
                "analysis_type": "One of TEXT, MEDICAL, or TASKS",
                "processing_path": "One of SIMPLE or COMPLEX",
                "confidence": "Number between 0 and 1",
                "reasoning": "Brief explanation of your classification"
            }

            Concentrate on the document content, layout, and purpose. Be definitive in your classification.
            """

            # Get document content for classification
            file_extension = Path(file_name).suffix.lower()

            # PDF files need special handling
            if file_extension == ".pdf" or file_type == "application/pdf":
                from pdf2image import convert_from_path

                # Just use the first page for classification
                images = convert_from_path(file_path, first_page=1, last_page=1)
                if images:
                    # Use the first page image for classification
                    with tempfile.NamedTemporaryFile(suffix=".png") as temp_img:
                        images[0].save(temp_img.name)
                        response = self.model.generate_content([prompt, temp_img.name])
                else:
                    logger.warning(f"Could not extract images from PDF: {file_path}")
                    # Fallback to general document type
                    return {
                        "document_type": DOCUMENT_TYPES["GENERAL"],
                        "analysis_type": ANALYSIS_TYPES["TEXT"],
                        "confidence": 0.5,
                        "reasoning": "PDF processing failed, defaulting to general text analysis",
                    }

            # Image files
            elif file_type.startswith("image/"):
                response = self.model.generate_content([prompt, file_path])

            # Text-based files
            else:
                # Read the first part of the file to classify
                with open(file_path, "rb") as f:
                    content = f.read(10240)  # Read first 10KB

                # For text files, decode and send as text
                if file_type.startswith("text/") or file_extension in [
                    ".txt",
                    ".html",
                    ".json",
                    ".xml",
                ]:
                    try:
                        text_content = content.decode("utf-8")
                        response = self.model.generate_content([prompt, text_content])
                    except UnicodeDecodeError:
                        # If we can't decode, treat as binary
                        response = self.model.generate_content(
                            [
                                prompt,
                                {
                                    "text": f"Binary file with extension {file_extension}"
                                },
                            ]
                        )
                else:
                    # For other files, just use the file name and type
                    response = self.model.generate_content(
                        [prompt, {"text": f"File name: {file_name}, Type: {file_type}"}]
                    )

            # Parse the response
            result_text = response.text

            # Extract JSON from the response
            import json
            import re

            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", result_text, re.DOTALL)
            if json_match:
                try:
                    classification = json.loads(json_match.group(0))
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from response: {result_text}")
                    # Fallback to default classification
                    return {
                        "document_type": DOCUMENT_TYPES["GENERAL"],
                        "analysis_type": ANALYSIS_TYPES["TEXT"],
                        "confidence": 0.5,
                        "reasoning": "Failed to parse classification result",
                    }
            else:
                logger.warning(f"No JSON found in response: {result_text}")
                # Fallback to general document type
                return {
                    "document_type": DOCUMENT_TYPES["GENERAL"],
                    "analysis_type": ANALYSIS_TYPES["TEXT"],
                    "confidence": 0.5,
                    "reasoning": "No classification result found",
                }

            # Map the classification to our internal types
            document_type = DOCUMENT_TYPES.get(
                classification.get("document_type", "GENERAL"),
                DOCUMENT_TYPES["GENERAL"],
            )

            analysis_type = ANALYSIS_TYPES.get(
                classification.get("analysis_type", "TEXT"), ANALYSIS_TYPES["TEXT"]
            )

            # Get processing path from classification or determine based on document type
            processing_path = PROCESSING_PATHS.get(
                classification.get("processing_path", "SIMPLE"),
                PROCESSING_PATHS["SIMPLE"],
            )

            # Use document metadata to refine the classification if available
            if document_metadata:
                # If document is associated with a medical case, prefer medical analysis
                if document_metadata.get("document_type") == "medical":
                    analysis_type = ANALYSIS_TYPES["MEDICAL"]
                    processing_path = PROCESSING_PATHS["COMPLEX"]

                # If document is associated with a case with deadlines, prefer task extraction
                if (
                    document_metadata.get("has_deadlines")
                    or document_metadata.get("urgency") == "high"
                ):
                    analysis_type = ANALYSIS_TYPES["TASKS"]

                # If user has explicitly set a processing path, respect it
                if document_metadata.get("processing_path"):
                    user_path = document_metadata.get("processing_path")
                    if user_path in ["simple", "complex"]:
                        processing_path = user_path

            # For certain document types, always recommend complex processing
            if document_type in COMPLEX_DOCUMENT_TYPES:
                processing_path = PROCESSING_PATHS["COMPLEX"]

            # Medical analysis almost always benefits from complex processing
            if analysis_type == ANALYSIS_TYPES["MEDICAL"]:
                processing_path = PROCESSING_PATHS["COMPLEX"]

            return {
                "document_type": document_type,
                "analysis_type": analysis_type,
                "processing_path": processing_path,
                "confidence": classification.get("confidence", 0.8),
                "reasoning": classification.get(
                    "reasoning", "Classification based on document content"
                ),
            }

        except Exception as e:
            logger.error(f"Error classifying document: {str(e)}")
            # Return default classification on error
            return {
                "document_type": DOCUMENT_TYPES["GENERAL"],
                "analysis_type": ANALYSIS_TYPES["TEXT"],
                "processing_path": PROCESSING_PATHS["SIMPLE"],
                "confidence": 0.5,
                "reasoning": f"Classification error: {str(e)}",
            }
