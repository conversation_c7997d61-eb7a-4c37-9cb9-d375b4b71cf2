"""
Redis-based distributed locking service.

This service provides distributed locks for worker coordination, preventing
race conditions and ensuring exclusive access to shared resources across
multiple processes or servers.
"""

import logging
import os
import time
import uuid
from functools import wraps
from typing import Optional, TypeVar

import redis

T = TypeVar("T")
logger = logging.getLogger(__name__)


class RedisLockService:
    """Service for distributed locking using Redis."""

    def __init__(self):
        """Initialize Redis lock service with configuration from environment."""
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_client = redis.from_url(
            self.redis_url,
            password=os.getenv("REDIS_PASSWORD", ""),
            decode_responses=True,
        )
        self.lock_prefix = os.getenv("REDIS_LOCK_PREFIX", "pi_lawyer_lock")
        self.default_timeout = int(os.getenv("REDIS_LOCK_TIMEOUT", "60"))  # seconds

        logger.info(f"RedisLockService initialized with prefix {self.lock_prefix}")

    def acquire_lock(
        self,
        lock_name: str,
        owner_id: str = None,
        timeout: int = None,
        blocking: bool = False,
        retry_interval: float = 0.1,
        max_retry_time: int = 10,
    ) -> bool:
        """
        Acquire a distributed lock.

        Args:
            lock_name: Unique name for the lock
            owner_id: Identifier for the lock owner (defaults to a random UUID)
            timeout: Lock expiry in seconds (defaults to REDIS_LOCK_TIMEOUT)
            blocking: Whether to block until lock is available
            retry_interval: Time in seconds between retry attempts if blocking
            max_retry_time: Maximum time in seconds to retry if blocking

        Returns:
            True if lock was acquired, False otherwise
        """
        if timeout is None:
            timeout = self.default_timeout

        if owner_id is None:
            owner_id = str(uuid.uuid4())

        lock_key = f"{self.lock_prefix}:{lock_name}"
        end_time = time.time() + max_retry_time

        while True:
            # Use NX option to only set if key doesn't exist
            result = self.redis_client.set(lock_key, owner_id, ex=timeout, nx=True)

            if result:
                logger.debug(f"Acquired lock '{lock_name}' for owner '{owner_id}'")
                return True

            if not blocking or time.time() > end_time:
                logger.debug(
                    f"Failed to acquire lock '{lock_name}' for owner '{owner_id}'"
                )
                return False

            # Wait before retrying
            time.sleep(retry_interval)

    def release_lock(self, lock_name: str, owner_id: str) -> bool:
        """
        Release a previously acquired lock.

        Args:
            lock_name: Name of the lock to release
            owner_id: Identifier for the lock owner, must match current owner

        Returns:
            True if lock was released, False if not owned by owner_id
        """
        lock_key = f"{self.lock_prefix}:{lock_name}"

        # Use Lua script to ensure atomic comparison and deletion
        release_script = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """

        result = self.redis_client.eval(release_script, 1, lock_key, owner_id)

        if result:
            logger.debug(f"Released lock '{lock_name}' for owner '{owner_id}'")
            return True
        else:
            logger.warning(
                f"Failed to release lock '{lock_name}' for owner '{owner_id}' - not owner"
            )
            return False

    def renew_lock(self, lock_name: str, owner_id: str, timeout: int = None) -> bool:
        """
        Extend the expiration time of a lock.

        Args:
            lock_name: Name of the lock to renew
            owner_id: Identifier for the lock owner, must match current owner
            timeout: New lock expiry in seconds

        Returns:
            True if lock was renewed, False if not owned by owner_id
        """
        if timeout is None:
            timeout = self.default_timeout

        lock_key = f"{self.lock_prefix}:{lock_name}"

        # Use Lua script to ensure atomic check and update
        renew_script = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('expire', KEYS[1], ARGV[2])
        else
            return 0
        end
        """

        result = self.redis_client.eval(renew_script, 1, lock_key, owner_id, timeout)

        if result:
            logger.debug(f"Renewed lock '{lock_name}' for owner '{owner_id}'")
            return True
        else:
            logger.warning(
                f"Failed to renew lock '{lock_name}' for owner '{owner_id}' - not owner"
            )
            return False

    def is_locked(self, lock_name: str) -> bool:
        """
        Check if a lock is currently held.

        Args:
            lock_name: Name of the lock to check

        Returns:
            True if lock exists, False otherwise
        """
        lock_key = f"{self.lock_prefix}:{lock_name}"
        exists = self.redis_client.exists(lock_key)
        return exists > 0

    def get_lock_owner(self, lock_name: str) -> Optional[str]:
        """
        Get the owner ID of a current lock.

        Args:
            lock_name: Name of the lock to check

        Returns:
            Owner ID or None if lock doesn't exist
        """
        lock_key = f"{self.lock_prefix}:{lock_name}"
        return self.redis_client.get(lock_key)

    def with_lock(
        self,
        lock_name: str,
        timeout: int = None,
        blocking: bool = True,
        retry_interval: float = 0.1,
        max_retry_time: int = 10,
    ):
        """
        Decorator to execute a function with a distributed lock.

        Args:
            lock_name: Base name for the lock (may be formatted with function args)
            timeout: Lock timeout in seconds
            blocking: Whether to wait for lock acquisition
            retry_interval: Time between retries if blocking
            max_retry_time: Maximum time to retry if blocking

        Returns:
            Decorated function that acquires lock before execution
        """

        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate a unique owner ID for this function call
                owner_id = str(uuid.uuid4())

                # Format lock name if it contains placeholders
                formatted_lock_name = lock_name
                try:
                    # Try to format with args/kwargs if lock_name contains placeholders
                    formatted_lock_name = lock_name.format(*args, **kwargs)
                except (IndexError, KeyError):
                    # If formatting fails, use the original lock name
                    pass

                # Try to acquire the lock
                if not self.acquire_lock(
                    formatted_lock_name,
                    owner_id,
                    timeout,
                    blocking,
                    retry_interval,
                    max_retry_time,
                ):
                    raise LockAcquisitionError(
                        f"Failed to acquire lock '{formatted_lock_name}'"
                    )

                try:
                    # Execute the function with the lock held
                    return func(*args, **kwargs)
                finally:
                    # Always attempt to release the lock
                    self.release_lock(formatted_lock_name, owner_id)

            return wrapper

        return decorator


class LockAcquisitionError(Exception):
    """Exception raised when a lock cannot be acquired."""

    pass


# Create a singleton instance
_lock_service_instance = None


def get_redis_lock_service() -> RedisLockService:
    """
    Get the global RedisLockService instance.

    Returns:
        Singleton instance of RedisLockService
    """
    global _lock_service_instance
    if _lock_service_instance is None:
        _lock_service_instance = RedisLockService()
    return _lock_service_instance
