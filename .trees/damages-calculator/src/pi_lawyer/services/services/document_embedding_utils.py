"""
Utility functions for document embedding workflows.

This module provides utility functions to help with the process of embedding
authored documents, ensuring consistent handling across different API endpoints.
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from pi_lawyer.services.document_processing_queue import get_authored_document_queue

# Configure logging
logger = logging.getLogger(__name__)


def queue_authored_document_for_embedding(
    authored_document_id: str,
    tenant_id: str,
    user_id: str,
    document_data: Dict[str, Any],
    trigger_reason: str = "Manual trigger",
    source: str = "api",
    priority: int = 1,
    update_embedding_status: bool = True,
    supabase_client=None,
) -> Dict[str, Any]:
    """
    Queue an authored document for embedding and update its metadata.

    Args:
        authored_document_id: ID of the authored document to embed
        tenant_id: ID of the tenant owning the document
        user_id: ID of the user who triggered the embedding
        document_data: Document data including case_id, client_id, status, metadata, etc.
        trigger_reason: Reason for triggering the embedding
        source: Source of the embedding request (api, worker, etc.)
        priority: Processing priority (lower = higher priority)
        update_embedding_status: Whether to update the document's embedding_status field
        supabase_client: Optional Supabase client instance

    Returns:
        Dictionary with embedding job information
    """
    try:
        # Get the queue service
        queue_service = get_authored_document_queue()

        # Extract needed fields
        case_id = document_data.get("case_id")
        client_id = document_data.get("client_id")
        document_status = document_data.get("status", "draft")

        # Prepare metadata for the job
        job_metadata = {
            "source": source,
            "document_title": document_data.get("title", "Untitled Document"),
            "trigger_reason": trigger_reason,
            "auto_triggered": source != "manual",
        }

        # Add any additional metadata
        if "metadata" in document_data and isinstance(document_data["metadata"], dict):
            doc_metadata = document_data["metadata"] or {}
            if doc_metadata.get("embedding_metadata"):
                job_metadata.update(doc_metadata.get("embedding_metadata", {}))

        # Enqueue for embedding
        job_id = queue_service.enqueue_authored_document(
            authored_document_id=authored_document_id,
            tenant_id=tenant_id,
            user_id=user_id,
            case_id=case_id,
            client_id=client_id,
            document_status=document_status,
            metadata=job_metadata,
            priority=priority,
        )

        logger.info(
            f"Queued authored document {authored_document_id} for embedding with job ID {job_id}"
        )

        # Prepare embedding info for document metadata
        embedding_info = {
            "queued_at": datetime.now().isoformat(),
            "job_id": job_id,
            "status": "queued",
            "trigger_reason": trigger_reason,
            "source": source,
        }

        # Update document metadata and status if requested and a Supabase client is provided
        if update_embedding_status and supabase_client:
            # Get current metadata
            doc_response = (
                supabase_client.table("tenants.authored_documents")
                .select("metadata")
                .eq("id", authored_document_id)
                .eq("tenant_id", tenant_id)
                .execute()
            )

            if doc_response.data and len(doc_response.data) > 0:
                current_doc = doc_response.data[0]
                metadata = current_doc.get("metadata", {}) or {}

                # Update embedding info in metadata
                if not metadata:
                    metadata = {}

                metadata["embedding_info"] = embedding_info

                # Update the document with embedding info
                supabase_client.table("tenants.authored_documents").update(
                    {"metadata": metadata, "embedding_status": "queued"}
                ).eq("id", authored_document_id).eq("tenant_id", tenant_id).execute()

        return {
            "job_id": job_id,
            "authored_document_id": authored_document_id,
            "status": "queued",
            "embedding_info": embedding_info,
        }

    except Exception as e:
        error_msg = f"Error queueing authored document {authored_document_id} for embedding: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "status": "error",
            "error": error_msg,
            "authored_document_id": authored_document_id,
        }


def should_embed_document(
    document_data: Dict[str, Any], metadata_update: Optional[Dict[str, Any]] = None
) -> tuple:
    """
    Determine if a document should be embedded based on its status and metadata.

    Args:
        document_data: Document data including status and metadata
        metadata_update: Optional new metadata that is being applied to the document

    Returns:
        Tuple of (should_embed, reason)
    """
    should_embed = False
    reason = None

    # Check if document is in a final state
    if document_data.get("status") in ["final", "approved", "published"]:
        should_embed = True
        reason = f"Document is in '{document_data.get('status')}' state"

    # Check if embedding is explicitly requested in metadata
    document_metadata = document_data.get("metadata", {}) or {}
    if document_metadata and document_metadata.get("should_embed", False):
        should_embed = True
        reason = "Explicitly requested in document metadata"

    # Check if embedding is requested in the metadata update
    if metadata_update:
        if metadata_update.get("should_embed", False):
            should_embed = True
            reason = "Explicitly requested in metadata update"

    return should_embed, reason
