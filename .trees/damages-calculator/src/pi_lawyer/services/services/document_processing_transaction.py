"""
Document Processing Transaction

This module provides transaction-like behavior for document processing operations,
including automatic retries and state rollback for failed operations.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants for retry configuration
MAX_RETRIES = 3
BASE_RETRY_DELAY = 2  # seconds


# Define custom exceptions
class EmbeddingError(Exception):
    """Exception raised for errors in the embedding process."""

    pass


class VectorStoreError(Exception):
    """Exception raised for errors in vector storage operations."""

    pass


class TransactionError(Exception):
    """Exception raised when a transaction-like operation fails."""

    pass


# Retry decorator with exponential backoff
def retry_with_backoff(max_retries=MAX_RETRIES, base_delay=BASE_RETRY_DELAY):
    """Retry decorator with exponential backoff for transient errors"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            while True:
                try:
                    return await func(*args, **kwargs)
                except (EmbeddingError, VectorStoreError) as e:
                    retries += 1
                    if retries > max_retries:
                        logger.error(f"Failed after {max_retries} retries: {str(e)}")
                        raise

                    # Calculate backoff delay: 2^retry * base_delay (with jitter)
                    delay = (
                        (2 ** (retries - 1))
                        * base_delay
                        * (0.5 + 0.5 * hash(str(args) + str(time.time())) % 100 / 100)
                    )
                    logger.warning(
                        f"Retrying {func.__name__} after error: {str(e)}. Retry {retries}/{max_retries} in {delay:.2f}s"
                    )
                    await asyncio.sleep(delay)

        return wrapper

    return decorator


class DocumentProcessingTransaction:
    """
    Transaction manager for document processing operations, ensuring
    all-or-nothing semantics with automatic retries and rollback capabilities.
    """

    def __init__(self):
        """Initialize the transaction manager."""
        # Store active transactions with their resources
        self.transactions = {}
        self.lock = asyncio.Lock()

    async def start(self, document_id: str, tenant_id: str) -> str:
        """
        Start a new transaction for document processing.

        Args:
            document_id: The document being processed
            tenant_id: The tenant ID for this operation

        Returns:
            A unique transaction ID
        """
        async with self.lock:
            transaction_id = str(uuid.uuid4())
            self.transactions[transaction_id] = {
                "document_id": document_id,
                "tenant_id": tenant_id,
                "start_time": datetime.utcnow().isoformat(),
                "status": "started",
                "resources": {"vectors": [], "db_records": [], "files": []},
                "errors": [],
            }
            logger.info(
                f"Started transaction {transaction_id} for document {document_id}"
            )
            return transaction_id

    async def register_resource(
        self,
        transaction_id: str,
        resource_type: str,
        resource_id: str,
        location: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Register a resource created during the transaction for potential rollback.

        Args:
            transaction_id: The transaction ID
            resource_type: Type of resource (vector, db_record, file)
            resource_id: Unique identifier for the resource
            location: Where the resource is stored
            metadata: Additional information about the resource
        """
        if transaction_id not in self.transactions:
            raise TransactionError(f"Transaction {transaction_id} not found")

        async with self.lock:
            tx = self.transactions[transaction_id]

            if resource_type not in tx["resources"]:
                tx["resources"][resource_type] = []

            tx["resources"][resource_type].append(
                {
                    "id": resource_id,
                    "location": location,
                    "created_at": datetime.utcnow().isoformat(),
                    "metadata": metadata or {},
                }
            )

            logger.debug(
                f"Registered {resource_type} {resource_id} in transaction {transaction_id}"
            )

    async def register_error(
        self, transaction_id: str, error_type: str, error_message: str, step: str
    ) -> None:
        """
        Register an error that occurred during the transaction.

        Args:
            transaction_id: The transaction ID
            error_type: Type of error
            error_message: Error message details
            step: Processing step where the error occurred
        """
        if transaction_id not in self.transactions:
            logger.error(
                f"Cannot register error: Transaction {transaction_id} not found"
            )
            return

        async with self.lock:
            tx = self.transactions[transaction_id]
            tx["errors"].append(
                {
                    "type": error_type,
                    "message": error_message,
                    "step": step,
                    "time": datetime.utcnow().isoformat(),
                }
            )

            logger.error(
                f"Error in transaction {transaction_id} at step {step}: {error_message}"
            )

    async def commit(self, transaction_id: str) -> Dict[str, Any]:
        """
        Commit a transaction, finalizing all changes.

        Args:
            transaction_id: The transaction ID

        Returns:
            Transaction summary information
        """
        if transaction_id not in self.transactions:
            raise TransactionError(f"Transaction {transaction_id} not found")

        async with self.lock:
            tx = self.transactions[transaction_id]

            # Check for errors
            if tx["errors"]:
                await self.rollback(transaction_id)
                error_details = ", ".join(
                    [f"{e['step']}: {e['message']}" for e in tx["errors"]]
                )
                raise TransactionError(
                    f"Cannot commit transaction with errors: {error_details}"
                )

            # Mark as committed
            tx["status"] = "committed"
            tx["commit_time"] = datetime.utcnow().isoformat()

            # Calculate some statistics for the return value
            result = {
                "transaction_id": transaction_id,
                "document_id": tx["document_id"],
                "tenant_id": tx["tenant_id"],
                "status": "completed",
                "duration_seconds": (
                    datetime.fromisoformat(tx["commit_time"])
                    - datetime.fromisoformat(tx["start_time"])
                ).total_seconds(),
                "resource_counts": {
                    rtype: len(resources)
                    for rtype, resources in tx["resources"].items()
                },
            }

            logger.info(
                f"Committed transaction {transaction_id} for document {tx['document_id']}"
            )
            return result

    async def rollback(self, transaction_id: str) -> None:
        """
        Roll back all changes made in this transaction.

        Args:
            transaction_id: The transaction ID
        """
        if transaction_id not in self.transactions:
            logger.warning(f"Transaction {transaction_id} not found for rollback")
            return

        async with self.lock:
            tx = self.transactions[transaction_id]
            logger.warning(
                f"Rolling back transaction {transaction_id} for document {tx['document_id']}"
            )

            # Mark as rolling back
            tx["status"] = "rolling_back"

            # Track rollback status
            tx["rollback"] = {
                "start_time": datetime.utcnow().isoformat(),
                "completed": False,
                "errors": [],
            }

            # We'll implement actual resource cleanup in services that use this class
            # For now, just mark as rolled back
            tx["status"] = "rolled_back"
            tx["rollback"]["completed"] = True
            tx["rollback"]["end_time"] = datetime.utcnow().isoformat()

            logger.info(f"Rolled back transaction {transaction_id}")

    def get_transaction(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a transaction.

        Args:
            transaction_id: The transaction ID

        Returns:
            Transaction information or None if not found
        """
        return self.transactions.get(transaction_id)

    def get_resources(
        self, transaction_id: str, resource_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get resources registered in a transaction.

        Args:
            transaction_id: The transaction ID
            resource_type: Optional resource type filter

        Returns:
            List of resources
        """
        if transaction_id not in self.transactions:
            return []

        tx = self.transactions[transaction_id]

        if resource_type:
            return tx["resources"].get(resource_type, [])

        # Return all resources if no type specified
        all_resources = []
        for r_type, resources in tx["resources"].items():
            all_resources.extend(resources)

        return all_resources
