"""Task embedding service for generating and storing task embeddings."""
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

from ..db.pinecone_client import PineconeClient
from ..utils.voyage_embeddings import VoyageAIEmbeddings

logger = logging.getLogger(__name__)


class TaskEmbeddingService:
    """Service for generating and storing task embeddings."""

    def __init__(
        self,
        openai_api_key: Optional[str] = None,
        pinecone_client: Optional[PineconeClient] = None,
    ):
        """Initialize the task embedding service.

        Args:
            openai_api_key: OpenAI API key (defaults to env var if not provided)
            pinecone_client: PineconeClient instance (created if not provided)
        """
        # Create embedder with lazy initialization
        self.embedder = VoyageAIEmbeddings(model="voyage-3-large")
        self._pinecone_client = pinecone_client
        self._pinecone_client_initialized = pinecone_client is not None

        # Store parameters for lazy initialization
        self._openai_api_key = openai_api_key
        logger.info("TaskEmbeddingService initialized with lazy loading")

    @property
    def pinecone_client(self):
        """Get the PineconeClient instance, initializing it if necessary."""
        if not self._pinecone_client_initialized:
            # Create PineconeClient with default settings from env vars
            from ..db.pinecone_client import PineconeClient

            self._pinecone_client = PineconeClient(
                api_key=os.getenv("PINECONE_API_KEY"),
                environment=os.getenv("PINECONE_ENVIRONMENT"),
                index_name=os.getenv("PINECONE_INDEX_NAME", "pi-lawyer"),
                tenant_id=None,  # Will be set per request
            )
            self._pinecone_client_initialized = True
            logger.info("PineconeClient initialized lazily")

        return self._pinecone_client

    @pinecone_client.setter
    def pinecone_client(self, client):
        """Set the PineconeClient instance."""
        self._pinecone_client = client
        self._pinecone_client_initialized = client is not None

    async def generate_task_embedding(
        self, task_data: Dict[str, Any], tenant_id: str
    ) -> Dict[str, Any]:
        """Generate and store embeddings for a task.

        Args:
            task_data: Task data dictionary
            tenant_id: Tenant ID for multi-tenant isolation

        Returns:
            Status dictionary
        """
        try:
            logger.info(
                f"Generating embedding for task {task_data.get('id')} for tenant {tenant_id}"
            )

            # Set tenant ID for this operation
            self.pinecone_client.tenant_id = tenant_id

            # Create rich context from task data
            task_text = self._create_task_text(task_data)

            # Generate embedding
            embedding = await self.embedder.aembed_query(task_text)

            # Create metadata with security attributes
            metadata = self._create_task_metadata(task_data, tenant_id)

            # Store in appropriate namespace with security metadata
            self.pinecone_client.store_task(
                task_id=str(task_data["id"]), vector=embedding, metadata=metadata
            )

            logger.info(f"Successfully stored embedding for task {task_data.get('id')}")
            return {"status": "success", "task_id": task_data["id"]}

        except Exception as e:
            logger.error(f"Error generating task embedding: {str(e)}")
            return {"status": "error", "error": str(e), "task_id": task_data.get("id")}

    def _create_task_text(self, task_data: Dict[str, Any]) -> str:
        """Create rich text representation of the task for embedding.

        Args:
            task_data: Task data dictionary

        Returns:
            Text representation of the task
        """
        # Build a comprehensive text representation for semantic search
        parts = []

        # Title and description are most important
        parts.append(f"Title: {task_data.get('title', '')}")

        if task_data.get("description"):
            parts.append(f"Description: {task_data.get('description', '')}")

        # Add other task metadata to enrich the embedding
        if task_data.get("status"):
            parts.append(f"Status: {task_data.get('status', '')}")

        if task_data.get("priority"):
            parts.append(f"Priority: {task_data.get('priority', '')}")

        if task_data.get("due_date"):
            parts.append(f"Due Date: {task_data.get('due_date', '')}")

        if task_data.get("assigned_to"):
            parts.append(f"Assigned To: {task_data.get('assigned_to', '')}")

        if task_data.get("related_case"):
            parts.append(f"Related Case: {task_data.get('related_case', '')}")

        # Combine all parts with newlines
        return "\n".join(parts)

    def _create_task_metadata(
        self, task_data: Dict[str, Any], tenant_id: str
    ) -> Dict[str, Any]:
        """Create metadata for the task embedding with security attributes.

        Args:
            task_data: Task data dictionary
            tenant_id: Tenant ID for multi-tenant isolation

        Returns:
            Metadata dictionary with security attributes
        """
        # Extract metadata fields from task data
        metadata = {
            # Security attributes
            "tenant_id": tenant_id,
            "created_by": str(task_data.get("created_by", "")),
            "assigned_to": str(task_data.get("assigned_to", "")),
            "related_case": str(task_data.get("related_case", "")),
            # Task attributes for filtering
            "task_id": str(task_data.get("id", "")),
            "title": task_data.get("title", ""),
            "status": task_data.get("status", ""),
            "priority": task_data.get("priority", ""),
            "task_type": task_data.get("task_type", ""),
            "created_at": task_data.get("created_at", datetime.utcnow().isoformat()),
            "updated_at": task_data.get("updated_at", datetime.utcnow().isoformat()),
            "due_date": task_data.get("due_date", ""),
        }

        # Add tags if present
        if task_data.get("tags") and isinstance(task_data["tags"], list):
            metadata["tags"] = task_data["tags"]

        return metadata

    async def delete_task_embedding(
        self, task_id: str, tenant_id: str
    ) -> Dict[str, Any]:
        """Delete a task embedding from the index.

        Args:
            task_id: ID of the task to delete
            tenant_id: Tenant ID for multi-tenant isolation

        Returns:
            Status dictionary
        """
        try:
            # Set tenant ID for this operation
            self.pinecone_client.tenant_id = tenant_id

            # Delete from Pinecone
            self.pinecone_client.delete_task(task_id=str(task_id))

            return {"status": "success", "task_id": task_id}

        except Exception as e:
            logger.error(f"Error deleting task embedding: {str(e)}")
            return {"status": "error", "error": str(e), "task_id": task_id}

    async def search_similar_tasks(
        self,
        query: str,
        tenant_id: str,
        user_context: Dict[str, Any],
        top_k: int = 5,
        filters: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """Search for similar tasks based on semantic similarity.

        Args:
            query: Search query text
            tenant_id: Tenant ID for multi-tenant isolation
            user_context: User context for RBAC filtering
            top_k: Number of results to return
            filters: Additional filters to apply

        Returns:
            List of similar tasks with scores
        """
        try:
            # Set tenant ID for this operation
            self.pinecone_client.tenant_id = tenant_id

            # Generate embedding for query
            query_embedding = await self.embedder.aembed_query(query)

            # Search with RBAC filtering
            results = self.pinecone_client.search_tasks(
                query_vector=query_embedding,
                top_k=top_k,
                filter=filters,
                user_context=user_context,
            )

            # Format results
            formatted_results = []
            for match in results:
                formatted_results.append(
                    {
                        "task_id": match.metadata.get("task_id"),
                        "title": match.metadata.get("title"),
                        "status": match.metadata.get("status"),
                        "priority": match.metadata.get("priority"),
                        "assigned_to": match.metadata.get("assigned_to"),
                        "due_date": match.metadata.get("due_date"),
                        "score": match.score,
                    }
                )

            return formatted_results

        except Exception as e:
            logger.error(f"Error searching similar tasks: {str(e)}")
            return []


# Create a singleton instance
task_embedding_service = TaskEmbeddingService()
