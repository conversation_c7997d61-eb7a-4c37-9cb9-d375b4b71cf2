"""
Authored Document Embedding Service

This service extends the document embedding pipeline to handle authored documents,
ensuring they can be searched alongside uploaded documents with consistent functionality.
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from .document_processing_transaction import (
    DocumentProcessingTransaction,
)
from .tenant_document_embedding_service import TenantDocumentEmbeddingService

logger = logging.getLogger(__name__)


class AuthoredDocumentEmbeddingService:
    """
    Service for processing authored document text into chunks, generating embeddings,
    and storing them in Pinecone with proper tenant isolation.
    """

    def __init__(self):
        """Initialize the authored document embedding service."""
        # Reuse the existing embedding service
        self.embedding_service = TenantDocumentEmbeddingService()
        self.transaction_manager = DocumentProcessingTransaction()

    async def process_authored_document(
        self,
        authored_document_id: str,
        tenant_id: str,
        case_id: Optional[str] = None,
        client_id: Optional[str] = None,
        user_id: Optional[str] = None,
        document_version: int = 1,
        document_status: str = "draft",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process an authored document for embedding.

        Args:
            authored_document_id: UUID of the authored document
            tenant_id: ID of the tenant owning the document
            case_id: Optional case ID the document is related to
            client_id: Optional client ID the document is related to
            user_id: ID of the user who authored the document
            document_version: Version number of the document
            document_status: Status of the document (draft, final, etc.)
            metadata: Optional additional metadata

        Returns:
            Result dictionary with processing status
        """
        transaction_id = None

        try:
            # Start a new transaction for this processing job
            transaction_id = await self.transaction_manager.start(
                authored_document_id,
                tenant_id,
                resource_type="authored_document_embedding",
            )

            logger.info(
                f"Started transaction {transaction_id} for authored document {authored_document_id}"
            )

            # Fetch the authored document from database
            # This will be implemented in a way that ensures tenant isolation
            document = await self._fetch_authored_document(
                authored_document_id, tenant_id
            )

            if not document:
                raise ValueError(
                    f"Authored document {authored_document_id} not found for tenant {tenant_id}"
                )

            # Extract text content from the document
            text_content = document.get("content", "")

            if not text_content.strip():
                logger.warning(
                    f"Authored document {authored_document_id} has no content to embed"
                )
                await self._update_embedding_status(
                    authored_document_id,
                    tenant_id,
                    "skipped",
                    "Document has no content to embed",
                )
                await self.transaction_manager.commit(transaction_id)
                return {
                    "status": "skipped",
                    "message": "Document has no content to embed",
                    "transaction_id": transaction_id,
                }

            # Prepare document metadata for embeddings
            doc_metadata = {
                **(metadata or {}),
                "document_id": authored_document_id,
                "authored_document_id": authored_document_id,
                "document_type": "authored_document",
                "document_title": document.get("title", ""),
                "tenant_id": tenant_id,
                "case_id": case_id or document.get("case_id"),
                "client_id": client_id or document.get("client_id"),
                "user_id": user_id or document.get("created_by"),
                "version": document_version,
                "status": document_status,
                "created_at": document.get("created_at"),
                "updated_at": document.get("updated_at"),
                "transaction_id": transaction_id,
                "source_type": "authored_document",
            }

            # Register document with transaction
            await self.transaction_manager.register_resource(
                transaction_id=transaction_id,
                resource_type="authored_document",
                resource_id=authored_document_id,
                location="database",
                metadata=doc_metadata,
            )

            # Update status to processing
            await self._update_embedding_status(
                authored_document_id,
                tenant_id,
                "processing",
                f"Processing document for embedding under transaction {transaction_id}",
            )

            # Process document through the embedding service
            embedding_result = await self.embedding_service.process_document(
                document_id=authored_document_id,
                text_content=text_content,
                tenant_id=tenant_id,
                document_metadata=doc_metadata,
                transaction_id=transaction_id,
                document_source_type="authored_document",
            )

            # Update status to completed
            await self._update_embedding_status(
                authored_document_id,
                tenant_id,
                "completed",
                f"Generated {embedding_result['chunk_count']} embeddings",
            )

            # Commit the transaction
            await self.transaction_manager.commit(transaction_id)

            logger.info(
                f"Authored document {authored_document_id} embedding completed with {embedding_result['chunk_count']} chunks"
            )

            return {
                "status": "completed",
                "message": f"Document embedding completed successfully with {embedding_result['chunk_count']} chunks",
                "transaction_id": transaction_id,
                "chunk_count": embedding_result["chunk_count"],
                "vector_count": embedding_result["vector_count"],
                "pool_namespace": embedding_result["pool_namespace"],
            }

        except Exception as e:
            error_message = str(e)
            logger.error(
                f"Error processing authored document {authored_document_id}: {error_message}"
            )

            # Update status to failed
            try:
                await self._update_embedding_status(
                    authored_document_id,
                    tenant_id,
                    "failed",
                    f"Failed to embed document: {error_message}",
                )
            except Exception as update_error:
                logger.error(f"Failed to update embedding status: {str(update_error)}")

            # If we have a transaction, roll it back
            if transaction_id:
                try:
                    error_type = type(e).__name__
                    await self.transaction_manager.register_error(
                        transaction_id=transaction_id,
                        error_type=error_type,
                        error_message=error_message,
                        step="authored_document_embedding",
                    )
                    await self.transaction_manager.rollback(transaction_id)
                    logger.info(
                        f"Registered error and rolled back transaction {transaction_id}"
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Error during error registration/rollback: {str(rollback_error)}"
                    )

            raise

    async def _fetch_authored_document(
        self, authored_document_id: str, tenant_id: str
    ) -> Dict[str, Any]:
        """
        Fetch authored document from the database with tenant isolation.

        Args:
            authored_document_id: UUID of the authored document
            tenant_id: ID of the tenant owning the document

        Returns:
            Dictionary containing the authored document data
        """
        import os

        from supabase import create_client

        # Initialize Supabase client (service role)
        supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY", os.getenv("SUPABASE_KEY")),
        )

        # Fetch the document with tenant isolation
        response = (
            supabase.table("authored_documents")
            .select("*")
            .eq("id", authored_document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if response.error:
            logger.error(f"Error fetching authored document: {response.error.message}")
            raise Exception(
                f"Error fetching authored document: {response.error.message}"
            )

        if not response.data or len(response.data) == 0:
            return None

        return response.data[0]

    async def _update_embedding_status(
        self,
        authored_document_id: str,
        tenant_id: str,
        status: str,
        message: str = None,
    ):
        """
        Update the embedding status of an authored document.

        Args:
            authored_document_id: UUID of the authored document
            tenant_id: ID of the tenant owning the document
            status: New status (pending, processing, completed, failed, skipped)
            message: Optional status message
        """
        import os

        from supabase import create_client

        # Initialize Supabase client (service role)
        supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY", os.getenv("SUPABASE_KEY")),
        )

        # Update metadata with status information
        update_data = {
            "embedding_status": status,
            "last_embedded_at": datetime.now().isoformat(),
        }

        # Add message to metadata if provided
        if message:
            response = (
                supabase.table("authored_documents")
                .select("metadata")
                .eq("id", authored_document_id)
                .eq("tenant_id", tenant_id)
                .execute()
            )

            if response.error:
                logger.error(
                    f"Error fetching authored document metadata: {response.error.message}"
                )
                raise Exception(
                    f"Error fetching authored document metadata: {response.error.message}"
                )

            if response.data and len(response.data) > 0:
                existing_metadata = response.data[0].get("metadata", {})
                embedding_info = existing_metadata.get("embedding_info", {})
                embedding_info.update(
                    {
                        "status": status,
                        "last_message": message,
                        "updated_at": datetime.now().isoformat(),
                    }
                )
                existing_metadata["embedding_info"] = embedding_info
                update_data["metadata"] = existing_metadata

        # Update the document
        response = (
            supabase.table("authored_documents")
            .update(update_data)
            .eq("id", authored_document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if response.error:
            logger.error(
                f"Error updating authored document embedding status: {response.error.message}"
            )
            raise Exception(
                f"Error updating authored document status: {response.error.message}"
            )

    async def reprocess_authored_document(
        self, authored_document_id: str, tenant_id: str
    ) -> Dict[str, Any]:
        """
        Reprocess an authored document (e.g., after content update).

        Args:
            authored_document_id: UUID of the authored document
            tenant_id: ID of the tenant owning the document

        Returns:
            Result dictionary with reprocessing status
        """
        # First, delete existing embeddings
        await self.delete_authored_document_embeddings(authored_document_id, tenant_id)

        # Then process the document again
        return await self.process_authored_document(authored_document_id, tenant_id)

    async def delete_authored_document_embeddings(
        self, authored_document_id: str, tenant_id: str
    ) -> Dict[str, Any]:
        """
        Delete all embeddings for an authored document.

        Args:
            authored_document_id: UUID of the authored document
            tenant_id: ID of the tenant owning the document

        Returns:
            Result dictionary with deletion status
        """
        transaction_id = None

        try:
            # Start a new transaction for this deletion
            transaction_id = await self.transaction_manager.start(
                authored_document_id,
                tenant_id,
                resource_type="authored_document_embedding_deletion",
            )

            # First, delete from vector database
            deleted_vectors = await self.embedding_service.delete_document_embeddings(
                document_id=authored_document_id,
                tenant_id=tenant_id,
                transaction_id=transaction_id,
            )

            # Then, delete from relational database
            import os

            from supabase import create_client

            # Initialize Supabase client (service role)
            supabase = create_client(
                os.getenv("SUPABASE_URL"),
                os.getenv("SUPABASE_SERVICE_KEY", os.getenv("SUPABASE_KEY")),
            )

            # Delete document chunks with tenant isolation
            response = (
                supabase.table("document_chunks")
                .delete()
                .eq("authored_document_id", authored_document_id)
                .eq("tenant_id", tenant_id)
                .execute()
            )

            if response.error:
                logger.error(
                    f"Error deleting document chunks: {response.error.message}"
                )
                raise Exception(
                    f"Error deleting document chunks: {response.error.message}"
                )

            deleted_chunks = response.count if hasattr(response, "count") else 0

            # Update the embedding status
            await self._update_embedding_status(
                authored_document_id,
                tenant_id,
                "pending",
                f"Embeddings deleted ({deleted_chunks} chunks, {deleted_vectors} vectors)",
            )

            # Commit the transaction
            await self.transaction_manager.commit(transaction_id)

            logger.info(
                f"Deleted {deleted_chunks} chunks for authored document {authored_document_id}"
            )

            return {
                "status": "completed",
                "message": "Document embeddings deleted successfully",
                "deleted_chunks": deleted_chunks,
                "deleted_vectors": deleted_vectors,
                "transaction_id": transaction_id,
            }

        except Exception as e:
            error_message = str(e)
            logger.error(
                f"Error deleting authored document embeddings for {authored_document_id}: {error_message}"
            )

            # If we have a transaction, roll it back
            if transaction_id:
                try:
                    error_type = type(e).__name__
                    await self.transaction_manager.register_error(
                        transaction_id=transaction_id,
                        error_type=error_type,
                        error_message=error_message,
                        step="authored_document_embedding_deletion",
                    )
                    await self.transaction_manager.rollback(transaction_id)
                    logger.info(
                        f"Registered error and rolled back transaction {transaction_id}"
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Error during error registration/rollback: {str(rollback_error)}"
                    )

            raise


# Singleton pattern for the service
_service_instance = None


def get_authored_document_embedding_service():
    """
    Get the global authored document embedding service instance.

    Returns:
        Singleton instance of AuthoredDocumentEmbeddingService
    """
    global _service_instance
    if _service_instance is None:
        _service_instance = AuthoredDocumentEmbeddingService()
    return _service_instance
