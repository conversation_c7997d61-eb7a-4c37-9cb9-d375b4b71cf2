"""
Redis-based persistent queue service for document processing.

This service provides a reliable, persistent queue implementation using Redis
to ensure document processing tasks can survive application restarts and
scale across multiple worker processes.
"""

import json
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import redis

logger = logging.getLogger(__name__)


class RedisQueueService:
    """Service for handling persistent document processing queues with Redis."""

    def __init__(self):
        """Initialize Redis queue service with configuration from environment."""
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_client = redis.from_url(
            self.redis_url,
            password=os.getenv("REDIS_PASSWORD", ""),
            decode_responses=True,
        )
        self.queue_prefix = os.getenv("REDIS_QUEUE_PREFIX", "pi_lawyer_docs")
        self.active_queue = f"{self.queue_prefix}:queue:active"
        self.processing_queue = f"{self.queue_prefix}:queue:processing"
        self.failed_queue = f"{self.queue_prefix}:queue:failed"
        self.completed_queue = f"{self.queue_prefix}:queue:completed"

        logger.info(f"RedisQueueService initialized with prefix {self.queue_prefix}")

    def enqueue(self, job_data: Dict[str, Any], priority: int = 1) -> str:
        """
        Add a job to the processing queue with priority.

        Args:
            job_data: Job data including document_id, file_path, etc.
            priority: Priority level (lower is higher priority)

        Returns:
            job_id: Unique identifier for the job
        """
        job_id = job_data.get("id") or str(uuid.uuid4())
        job_data["id"] = job_id
        job_data["priority"] = priority
        job_data["created_at"] = datetime.now().isoformat()
        job_data["status"] = "queued"

        # Save job data
        self.redis_client.hset(
            f"{self.queue_prefix}:jobs", job_id, json.dumps(job_data)
        )

        # Add to sorted set with priority score
        self.redis_client.zadd(self.active_queue, {job_id: priority})

        logger.info(f"Enqueued job {job_id} with priority {priority}")
        return job_id

    def dequeue(self) -> Optional[Dict[str, Any]]:
        """
        Get the highest priority job and move it to processing.

        Returns:
            job_data: Complete job data dictionary or None if queue is empty
        """
        # Use transaction to ensure atomic operations
        pipeline = self.redis_client.pipeline()

        # Get highest priority job (lowest score)
        job_ids = self.redis_client.zrange(self.active_queue, 0, 0)
        if not job_ids:
            return None

        job_id = job_ids[0]

        # Move from active to processing queue
        pipeline.zrem(self.active_queue, job_id)
        pipeline.zadd(self.processing_queue, {job_id: datetime.now().timestamp()})

        # Get job data
        pipeline.hget(f"{self.queue_prefix}:jobs", job_id)
        _, _, job_data_json = pipeline.execute()

        if not job_data_json:
            return None

        job_data = json.loads(job_data_json)
        job_data["processing_started"] = datetime.now().isoformat()

        # Update job status
        self.update_job_status(job_id, {"status": "processing"})

        logger.debug(f"Dequeued job {job_id}")
        return job_data

    def complete_job(self, job_id: str, result: Dict[str, Any]) -> None:
        """
        Mark a job as completed with results.

        Args:
            job_id: ID of the job to mark as completed
            result: Result data to store with the job
        """
        pipeline = self.redis_client.pipeline()

        # Remove from processing queue
        pipeline.zrem(self.processing_queue, job_id)

        # Add to completed queue (keep for 7 days)
        expiry = datetime.now() + timedelta(days=7)
        pipeline.zadd(self.completed_queue, {job_id: expiry.timestamp()})

        # Update job data
        job_data_json = self.redis_client.hget(f"{self.queue_prefix}:jobs", job_id)
        if job_data_json:
            job_data = json.loads(job_data_json)
            job_data.update(
                {
                    "status": "completed",
                    "completed_at": datetime.now().isoformat(),
                    "result": result,
                }
            )
            pipeline.hset(f"{self.queue_prefix}:jobs", job_id, json.dumps(job_data))

        pipeline.execute()
        logger.info(f"Job {job_id} completed successfully")

    def fail_job(self, job_id: str, error: str, retry: bool = False) -> None:
        """
        Mark a job as failed and optionally requeue for retry.

        Args:
            job_id: ID of the job to mark as failed
            error: Error message to store with the job
            retry: Whether to requeue the job for retry
        """
        pipeline = self.redis_client.pipeline()

        # Get job data
        job_data_json = self.redis_client.hget(f"{self.queue_prefix}:jobs", job_id)
        if not job_data_json:
            logger.warning(f"Cannot fail job {job_id}: job data not found")
            return

        job_data = json.loads(job_data_json)

        # Update retry count
        retry_count = job_data.get("retry_count", 0)
        job_data["retry_count"] = retry_count + 1 if retry else retry_count
        job_data["last_error"] = error
        job_data["failed_at"] = datetime.now().isoformat()

        # Remove from processing queue
        pipeline.zrem(self.processing_queue, job_id)

        if retry and retry_count < int(os.getenv("PROCESSING_MAX_RETRIES", "3")):
            # Exponential backoff for retry delay
            base_delay = int(os.getenv("PROCESSING_RETRY_DELAY", "5"))
            delay = base_delay * (2**retry_count)

            # Schedule for retry using sorted set with future timestamp
            retry_at = datetime.now() + timedelta(seconds=delay)
            job_data["retry_at"] = retry_at.isoformat()
            job_data["status"] = "retry_scheduled"

            pipeline.zadd(self.active_queue, {job_id: job_data["priority"]})

            logger.info(
                f"Job {job_id} scheduled for retry in {delay} seconds (attempt {retry_count+1})"
            )
        else:
            # Move to failed queue
            job_data["status"] = "failed"
            pipeline.zadd(self.failed_queue, {job_id: datetime.now().timestamp()})

            logger.warning(f"Job {job_id} failed permanently: {error}")

        # Update job data
        pipeline.hset(f"{self.queue_prefix}:jobs", job_id, json.dumps(job_data))

        pipeline.execute()

    def update_job_status(self, job_id: str, update: Dict[str, Any]) -> None:
        """
        Update job status and metadata.

        Args:
            job_id: ID of the job to update
            update: Dictionary of fields to update
        """
        job_data_json = self.redis_client.hget(f"{self.queue_prefix}:jobs", job_id)
        if job_data_json:
            job_data = json.loads(job_data_json)
            job_data.update(update)
            job_data["updated_at"] = datetime.now().isoformat()

            self.redis_client.hset(
                f"{self.queue_prefix}:jobs", job_id, json.dumps(job_data)
            )

            logger.debug(f"Updated job {job_id} status: {update.get('status', 'N/A')}")
        else:
            logger.warning(f"Cannot update job {job_id}: job data not found")

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current job status and data.

        Args:
            job_id: ID of the job to get status for

        Returns:
            Dictionary with job data or None if job not found
        """
        job_data_json = self.redis_client.hget(f"{self.queue_prefix}:jobs", job_id)
        if job_data_json:
            return json.loads(job_data_json)
        return None

    def get_document_jobs(self, document_id: str) -> List[Dict[str, Any]]:
        """
        Get all jobs associated with a document.

        Args:
            document_id: ID of the document

        Returns:
            List of job status dictionaries
        """
        # Get all jobs
        all_jobs = self.redis_client.hgetall(f"{self.queue_prefix}:jobs")

        # Filter to find jobs for this document
        document_jobs = []
        for job_id, job_data_json in all_jobs.items():
            job_data = json.loads(job_data_json)
            if job_data.get("document_id") == document_id:
                document_jobs.append(job_data)

        return document_jobs

    def requeue_stalled_jobs(self, timeout_minutes: int = 30) -> List[str]:
        """
        Find and requeue jobs that have been processing for too long.

        Args:
            timeout_minutes: How long a job can be processing before considered stalled

        Returns:
            List of requeued job IDs
        """
        cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
        cutoff_timestamp = cutoff_time.timestamp()

        # Find jobs in processing older than cutoff
        stalled_jobs = self.redis_client.zrangebyscore(
            self.processing_queue, 0, cutoff_timestamp
        )

        requeued_jobs = []
        for job_id in stalled_jobs:
            job_data = self.get_job_status(job_id)
            if job_data:
                # Mark for retry
                self.fail_job(
                    job_id, f"Job stalled after {timeout_minutes} minutes", retry=True
                )
                requeued_jobs.append(job_id)

        if requeued_jobs:
            logger.info(f"Requeued {len(requeued_jobs)} stalled jobs")

        return requeued_jobs

    def get_queue_stats(self) -> Dict[str, int]:
        """
        Get statistics about queue state.

        Returns:
            Dictionary with queue statistics
        """
        active_count = self.redis_client.zcard(self.active_queue)
        processing_count = self.redis_client.zcard(self.processing_queue)
        failed_count = self.redis_client.zcard(self.failed_queue)
        completed_count = self.redis_client.zcard(self.completed_queue)

        return {
            "active": active_count,
            "processing": processing_count,
            "failed": failed_count,
            "completed": completed_count,
            "total": active_count + processing_count + failed_count + completed_count,
        }


# Create a singleton instance
_queue_instance = None


def get_redis_queue_service() -> RedisQueueService:
    """
    Get the global RedisQueueService instance.

    Returns:
        Singleton instance of RedisQueueService
    """
    global _queue_instance
    if _queue_instance is None:
        _queue_instance = RedisQueueService()
    return _queue_instance
