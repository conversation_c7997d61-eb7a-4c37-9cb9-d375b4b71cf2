"""
Health check and system status service.

Provides endpoints for monitoring system health, component status,
and operational metrics.
"""

import logging
import os
import platform
import threading
import time
from datetime import datetime
from typing import Any, Dict

import psutil

from .metrics_collector import get_metrics_collector
from .redis_queue_service import get_redis_queue_service

logger = logging.getLogger(__name__)

# Track application start time
APP_START_TIME = time.time()


class HealthService:
    """
    Service for monitoring system health and component status.

    Provides methods to check health of various system components,
    aggregate system metrics, and expose health check endpoints.
    """

    def __init__(self):
        """Initialize the health check service."""
        self.components = {
            "redis": self._check_redis,
            "database": self._check_database,
            "document_processor": self._check_document_processor,
            "vector_store": self._check_vector_store,
        }

        # Initialize metrics collector
        self.metrics = get_metrics_collector(prefix="system_health")

        # Cache health check results to avoid repeated checks
        self.health_cache = {}
        self.cache_lock = threading.Lock()
        self.cache_ttl = 10  # seconds

        logger.info("HealthService initialized")

    def get_system_status(self) -> Dict[str, Any]:
        """
        Get overall system status including component health.

        Returns:
            Dictionary with system status information
        """
        status = {
            "status": "healthy",  # Default to healthy, will be changed if any checks fail
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": int(time.time() - APP_START_TIME),
            "version": os.getenv("APP_VERSION", "development"),
            "environment": os.getenv("APP_ENV", "development"),
            "components": {},
            "system": self._get_system_metrics(),
        }

        # Check all components
        failed_components = []

        for component_name, check_func in self.components.items():
            try:
                component_status = self._get_cached_or_check(component_name, check_func)
                status["components"][component_name] = component_status

                # If any component is not healthy, the overall status is not healthy
                if component_status["status"] != "healthy":
                    failed_components.append(component_name)
            except Exception as e:
                logger.error(
                    f"Error checking {component_name} health: {str(e)}", exc_info=True
                )
                status["components"][component_name] = {
                    "status": "error",
                    "message": f"Health check failed: {str(e)}",
                }
                failed_components.append(component_name)

        # Set overall status based on component health
        if failed_components:
            status["status"] = (
                "degraded"
                if len(failed_components) < len(self.components)
                else "critical"
            )
            status["degraded_components"] = failed_components

        # Record metrics
        self.metrics.gauge("system_health", 1 if status["status"] == "healthy" else 0)
        self.metrics.gauge("system_uptime", status["uptime_seconds"])

        return status

    def _get_cached_or_check(self, component_name: str, check_func) -> Dict[str, Any]:
        """
        Get cached health check result or perform check if cache is expired.

        Args:
            component_name: Name of the component to check
            check_func: Function to call for health check

        Returns:
            Health check result dictionary
        """
        with self.cache_lock:
            now = time.time()
            if component_name in self.health_cache:
                cache_entry = self.health_cache[component_name]
                if now - cache_entry["timestamp"] < self.cache_ttl:
                    return cache_entry["result"]

            # Cache miss or expired, perform check
            result = check_func()
            self.health_cache[component_name] = {"timestamp": now, "result": result}
            return result

    def _get_system_metrics(self) -> Dict[str, Any]:
        """
        Get system-level metrics like CPU, memory, and disk usage.

        Returns:
            Dictionary with system metrics
        """
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")

        metrics = {
            "cpu": {"percent": cpu_percent, "count": psutil.cpu_count()},
            "memory": {
                "total_mb": memory.total / (1024 * 1024),
                "available_mb": memory.available / (1024 * 1024),
                "percent_used": memory.percent,
            },
            "disk": {
                "total_gb": disk.total / (1024 * 1024 * 1024),
                "free_gb": disk.free / (1024 * 1024 * 1024),
                "percent_used": disk.percent,
            },
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "python": platform.python_version(),
            },
        }

        # Record system metrics
        self.metrics.gauge("cpu_usage_percent", cpu_percent)
        self.metrics.gauge("memory_usage_percent", memory.percent)
        self.metrics.gauge("disk_usage_percent", disk.percent)

        return metrics

    def _check_redis(self) -> Dict[str, Any]:
        """
        Check Redis connectivity and health.

        Returns:
            Health check result for Redis
        """
        try:
            # Get the Redis queue service
            redis_queue = get_redis_queue_service()

            # Perform a simple ping operation
            start_time = time.time()
            ping_result = redis_queue.redis_client.ping()
            response_time = time.time() - start_time

            if ping_result:
                # Get queue statistics
                queue_stats = redis_queue.get_queue_stats()

                self.metrics.gauge("redis_response_time_ms", response_time * 1000)

                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time * 1000, 2),
                    "queue_stats": queue_stats,
                }
            else:
                return {"status": "degraded", "message": "Redis ping failed"}
        except Exception as e:
            logger.error(f"Redis health check failed: {str(e)}", exc_info=True)
            return {
                "status": "critical",
                "message": f"Redis connection error: {str(e)}",
            }

    def _check_database(self) -> Dict[str, Any]:
        """
        Check database connectivity and health.

        Returns:
            Health check result for the database
        """
        try:
            # For this example, we assume the database is healthy
            # In a real implementation, you would perform a lightweight
            # query to check connectivity
            return {"status": "healthy", "message": "Database connection successful"}
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}", exc_info=True)
            return {
                "status": "critical",
                "message": f"Database connection error: {str(e)}",
            }

    def _check_document_processor(self) -> Dict[str, Any]:
        """
        Check document processing system health.

        Returns:
            Health check result for document processor
        """
        try:
            # Get the Redis queue service to check document processing
            redis_queue = get_redis_queue_service()

            # Check for stalled jobs
            stalled_jobs = redis_queue.get_stalled_jobs_count(timeout_minutes=30)

            # Get processing statistics
            stats = redis_queue.get_queue_stats()

            # Calculate job completion rate
            total_completed = stats.get("completed", 0)
            total_failed = stats.get("failed", 0)
            total_jobs = total_completed + total_failed

            completion_rate = 0
            if total_jobs > 0:
                completion_rate = (total_completed / total_jobs) * 100

            # Record metrics
            self.metrics.gauge("document_processor_stalled_jobs", stalled_jobs)
            self.metrics.gauge("document_processor_completion_rate", completion_rate)

            status = "healthy"
            message = "Document processor is functioning normally"

            # Determine health status based on stalled jobs
            if stalled_jobs > 10:
                status = "degraded"
                message = f"Document processor has {stalled_jobs} stalled jobs"

            return {
                "status": status,
                "message": message,
                "stalled_jobs": stalled_jobs,
                "completion_rate_percent": round(completion_rate, 2),
                "stats": stats,
            }
        except Exception as e:
            logger.error(
                f"Document processor health check failed: {str(e)}", exc_info=True
            )
            return {
                "status": "critical",
                "message": f"Document processor check error: {str(e)}",
            }

    def _check_vector_store(self) -> Dict[str, Any]:
        """
        Check vector store connectivity and health.

        Returns:
            Health check result for vector store
        """
        try:
            # For this example, we assume the vector store is healthy
            # In a real implementation, you would check connectivity to Pinecone
            return {"status": "healthy", "message": "Vector store is operational"}
        except Exception as e:
            logger.error(f"Vector store health check failed: {str(e)}", exc_info=True)
            return {
                "status": "critical",
                "message": f"Vector store connection error: {str(e)}",
            }


# Create a singleton instance
_health_service_instance = None


def get_health_service() -> HealthService:
    """
    Get the global HealthService instance.

    Returns:
        Singleton instance of HealthService
    """
    global _health_service_instance

    if _health_service_instance is None:
        _health_service_instance = HealthService()

    return _health_service_instance
