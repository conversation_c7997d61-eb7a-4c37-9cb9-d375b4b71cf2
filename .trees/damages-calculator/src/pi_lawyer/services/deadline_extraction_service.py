"""Deadline extraction service for legal documents."""
import hashlib
import json
import logging
import os
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import google.generativeai as genai
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import END, StateGraph

from pi_lawyer.models.deadline import (
    DeadlineExtractionState,
    LegalDeadline,
)
from pi_lawyer.services.circuit_breaker import CircuitBreaker
from src.pi_lawyer.db.supabase_client import SupabaseClient

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Gemini model
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))


class DeadlineExtractionService:
    """Service for extracting legal deadlines from documents."""

    def __init__(self, supabase_client: Optional[SupabaseClient] = None):
        """Initialize the deadline extraction service.

        Args:
            supabase_client: Optional Supabase client for database operations.
        """
        self.supabase = supabase_client or SupabaseClient()
        self.workflow = self._build_graph().compile()
        self.cache = {}

        # Create Gemini models with different parameters for different tasks
        self.gemini_extraction = ChatVertexAI(
            model_name="gemini-1.5-flash",
            temperature=0.1,
            max_output_tokens=4096,
            convert_system_message_to_human=True,
        )

        self.gemini_verification = ChatVertexAI(
            model_name="gemini-1.5-flash",
            temperature=0.0,
            max_output_tokens=2048,
            convert_system_message_to_human=True,
        )

        # Circuit breaker for MCP server calls
        self.mcp_circuit_breaker = CircuitBreaker(service_name="mcp_server")

    async def process_document(
        self, document_text: str, metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process a document to extract legal deadlines.

        Args:
            document_text: Text content of the document.
            metadata: Document metadata including jurisdiction, document_type, etc.

        Returns:
            Dictionary containing extracted deadlines and processing metadata.
        """
        # Generate cache key for document
        cache_key = self._generate_cache_key(document_text, metadata)

        # Return cached results if available
        if cache_key in self.cache:
            logger.info(
                f"Using cached deadline extraction for document {metadata.get('id', 'unknown')}"
            )
            return self.cache[cache_key]

        # Load jurisdiction rules
        jurisdiction = metadata.get("jurisdiction", "Unknown")
        jurisdiction_rules = await self._load_jurisdiction_rules(jurisdiction)

        # Initialize state
        initial_state = DeadlineExtractionState(
            document_text=document_text,
            document_metadata=metadata,
            jurisdiction_rules=jurisdiction_rules,
            tenant_id=metadata.get("tenant_id"),
        )

        try:
            # Run the workflow
            logger.info(
                f"Starting deadline extraction for document {metadata.get('id', 'unknown')}"
            )
            result = await self.workflow.ainvoke(initial_state)

            # Cache the results
            self.cache[cache_key] = result.final_output

            # Store results in database if tenant_id is provided
            if metadata.get("tenant_id") and metadata.get("document_id"):
                await self._store_deadlines(
                    result.parsed_deadlines,
                    metadata.get("tenant_id"),
                    metadata.get("document_id"),
                )

            return result.final_output

        except Exception as e:
            logger.error(f"Error extracting deadlines: {str(e)}")
            # Return a graceful failure response
            return {
                "metadata": metadata,
                "deadlines": [],
                "errors": [str(e)],
                "warnings": ["Deadline extraction failed unexpectedly"],
                "stats": {"total_deadlines": 0},
            }

    async def _extract_deadlines_node(
        self, state: DeadlineExtractionState
    ) -> DeadlineExtractionState:
        """Node 1: Extract deadlines from document using Gemini Flash."""

        # Extract document metadata
        jurisdiction = state.document_metadata.get("jurisdiction", "Unknown")
        doc_type = state.document_metadata.get("document_type", "legal document")
        case_type = state.document_metadata.get("case_type", "legal case")

        # Set up prompt with jurisdiction-specific context
        prompt = ChatPromptTemplate.from_template(
            """
        You are a specialized legal AI for {jurisdiction} law.

        Analyze this {doc_type} for {case_type} cases and extract ALL deadlines mentioned.
        Focus on court deadlines, statutes of limitations, response periods, and filing requirements.

        For each deadline, provide:
        1. Description: What needs to be done
        2. Date: If an absolute date is mentioned (in ISO format)
        3. Relative terms: If deadline is relative to another event (e.g., "30 days after service")
        4. Jurisdiction: Legal jurisdiction
        5. Source: Where in the document this deadline appears (section/paragraph)
        6. Priority: critical/high/medium/low
        7. Legal basis: Statute or rule that establishes this deadline
        8. Consequences: What happens if deadline is missed

        Format as JSON array with these fields for EACH deadline found.
        If no deadlines are found, return an empty array.

        ---DOCUMENT---
        {text}
        """
        )

        # Extract deadlines
        chain = prompt | self.gemini_extraction | JsonOutputParser()
        try:
            # Split document into chunks if too large
            if len(state.document_text) > 20000:
                chunks = self._split_document(state.document_text)
                all_deadlines = []
                for i, chunk in enumerate(chunks):
                    logger.info(f"Processing chunk {i+1}/{len(chunks)}")
                    chunk_deadlines = await chain.ainvoke(
                        {
                            "jurisdiction": jurisdiction,
                            "doc_type": doc_type,
                            "case_type": case_type,
                            "text": chunk,
                        }
                    )
                    if chunk_deadlines:
                        all_deadlines.extend(chunk_deadlines)
                state.raw_llm_output = all_deadlines
            else:
                state.raw_llm_output = await chain.ainvoke(
                    {
                        "jurisdiction": jurisdiction,
                        "doc_type": doc_type,
                        "case_type": case_type,
                        "text": state.document_text,
                    }
                )

            logger.info(f"Extracted {len(state.raw_llm_output)} potential deadlines")
            return state

        except Exception as e:
            logger.error(f"Error in deadline extraction: {str(e)}")
            state.validation_errors.append(f"Extraction error: {str(e)}")
            state.raw_llm_output = []
            return state

    async def _validate_dates_node(
        self, state: DeadlineExtractionState
    ) -> DeadlineExtractionState:
        """Node 2: Validate extracted dates against jurisdiction rules."""
        if not state.raw_llm_output:
            logger.info("No deadlines to validate")
            return state

        jurisdiction_rules = state.jurisdiction_rules or {}

        for deadline_data in state.raw_llm_output:
            # Check date format if present
            if deadline_data.get("date"):
                try:
                    datetime.fromisoformat(deadline_data["date"].replace("Z", "+00:00"))
                except (ValueError, AttributeError):
                    state.validation_errors.append(
                        f"Invalid date format in deadline: {deadline_data.get('description')}"
                    )
                    # Try to fix the date format
                    deadline_data["date"] = self._fix_date_format(deadline_data["date"])

            # Check against jurisdiction rules
            legal_basis = deadline_data.get("legal_basis", "")
            if legal_basis and legal_basis in jurisdiction_rules:
                rule = jurisdiction_rules[legal_basis]

                # Check relative terms against max days
                if deadline_data.get("relative_terms") and rule.get("max_days"):
                    days_match = re.search(
                        r"(\d+)\s+day", deadline_data["relative_terms"]
                    )
                    if days_match and int(days_match.group(1)) > rule["max_days"]:
                        state.validation_errors.append(
                            f"Deadline exceeds maximum: {deadline_data['description']} - "
                            f"{days_match.group(1)} days > maximum {rule['max_days']} days"
                        )

        logger.info(f"Validated dates with {len(state.validation_errors)} errors")
        return state

    async def _calculate_dates_node(
        self, state: DeadlineExtractionState
    ) -> DeadlineExtractionState:
        """Node 3: Calculate absolute dates from relative deadlines."""
        if not state.raw_llm_output:
            return state

        # Get filing date from metadata if available
        filing_date_str = state.document_metadata.get("filing_date")

        try:
            if filing_date_str:
                filing_date = datetime.fromisoformat(
                    filing_date_str.replace("Z", "+00:00")
                )

                for deadline_data in state.raw_llm_output:
                    # Calculate absolute date if relative terms are present but no date
                    if deadline_data.get("relative_terms") and not deadline_data.get(
                        "date"
                    ):
                        relative_terms = deadline_data["relative_terms"]

                        # Extract number and unit from relative terms
                        match = re.search(
                            r"(\d+)\s+(day|week|month|year)", relative_terms.lower()
                        )
                        if match:
                            num, unit = match.groups()
                            num = int(num)

                            # Calculate the delta
                            delta_args = {}
                            if unit == "day":
                                delta_args["days"] = num
                            elif unit == "week":
                                delta_args["days"] = num * 7
                            elif unit == "month":
                                delta_args["days"] = num * 30  # Approximation
                            elif unit == "year":
                                delta_args["days"] = num * 365  # Approximation

                            calculated_date = filing_date + timedelta(**delta_args)
                            deadline_data["date"] = calculated_date.isoformat().split(
                                "T"
                            )[0]

                            logger.info(
                                f"Calculated date {deadline_data['date']} from relative term: {relative_terms}"
                            )
        except Exception as e:
            logger.error(f"Error calculating dates: {str(e)}")
            state.validation_errors.append(f"Date calculation error: {str(e)}")

        return state

    async def _verify_deadlines_node(
        self, state: DeadlineExtractionState
    ) -> DeadlineExtractionState:
        """Node 4: Verify extracted deadlines against document text to detect hallucinations."""
        if not state.raw_llm_output:
            return state

        verification_prompt = ChatPromptTemplate.from_template(
            """
        Verify if the following deadline actually exists in the document text:

        Deadline: {deadline}

        Document Text:
        {text}

        Return a JSON object with these fields:
        {
            "exists": true/false,
            "evidence": "exact text snippet if found",
            "confidence": "high/medium/low"
        }
        """
        )

        verification_chain = (
            verification_prompt | self.gemini_verification | JsonOutputParser()
        )

        for deadline_data in state.raw_llm_output:
            try:
                # Create a simple representation of the deadline for verification
                deadline_repr = (
                    f"{deadline_data.get('description', 'Unknown deadline')} - "
                    f"{deadline_data.get('relative_terms', deadline_data.get('date', 'unspecified date'))}"
                )

                # Sample the document text for verification (full document may be too large)
                sample_text = self._extract_relevant_sample(
                    state.document_text,
                    deadline_data.get("description", "")
                    + " "
                    + deadline_data.get("relative_terms", ""),
                )

                # Verify the deadline
                result = await verification_chain.ainvoke(
                    {"deadline": deadline_repr, "text": sample_text}
                )

                # Add to validation errors if the deadline doesn't exist
                if (
                    not result.get("exists", False)
                    or result.get("confidence", "").lower() == "low"
                ):
                    state.validation_errors.append(
                        f"Potential hallucination: {deadline_repr} - "
                        f"Confidence: {result.get('confidence', 'unknown')}"
                    )
                    deadline_data["hallucination_risk"] = True
                else:
                    deadline_data["hallucination_risk"] = False
                    deadline_data["evidence"] = result.get("evidence", "")

            except Exception as e:
                logger.error(f"Error in verification: {str(e)}")
                state.validation_errors.append(
                    f"Verification error for {deadline_repr}: {str(e)}"
                )

        logger.info(f"Verified {len(state.raw_llm_output)} deadlines")
        return state

    async def _format_output_node(
        self, state: DeadlineExtractionState
    ) -> DeadlineExtractionState:
        """Node 5: Format the final output for the caller."""
        parsed_deadlines = []

        for deadline_data in state.raw_llm_output:
            try:
                # Filter out likely hallucinations
                if deadline_data.get("hallucination_risk", False):
                    continue

                # Create LegalDeadline objects with validation
                deadline = LegalDeadline(
                    **{
                        k: v
                        for k, v in deadline_data.items()
                        if k in LegalDeadline.__annotations__
                    },
                    tenant_id=state.tenant_id,
                    document_id=state.document_metadata.get("document_id"),
                )
                parsed_deadlines.append(deadline)

            except Exception as e:
                logger.error(f"Error parsing deadline: {str(e)}")
                state.validation_errors.append(f"Parsing error: {str(e)}")

        state.parsed_deadlines = parsed_deadlines

        # Format final output
        state.final_output = {
            "metadata": state.document_metadata,
            "deadlines": [deadline.dict() for deadline in parsed_deadlines],
            "warnings": state.validation_errors,
            "stats": {
                "total_deadlines": len(parsed_deadlines),
                "critical_count": sum(
                    1 for d in parsed_deadlines if d.priority == "critical"
                ),
                "high_count": sum(1 for d in parsed_deadlines if d.priority == "high"),
                "medium_count": sum(
                    1 for d in parsed_deadlines if d.priority == "medium"
                ),
                "low_count": sum(1 for d in parsed_deadlines if d.priority == "low"),
            },
        }

        logger.info(f"Final output contains {len(parsed_deadlines)} verified deadlines")
        return state

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow for deadline extraction."""
        # Create the graph and add nodes
        workflow = StateGraph(DeadlineExtractionState)

        # Add nodes
        workflow.add_node("extract", self._extract_deadlines_node)
        workflow.add_node("validate", self._validate_dates_node)
        workflow.add_node("calculate", self._calculate_dates_node)
        workflow.add_node("verify", self._verify_deadlines_node)
        workflow.add_node("format", self._format_output_node)

        # Define edges
        workflow.add_edge("extract", "validate")
        workflow.add_edge("validate", "calculate")
        workflow.add_edge("calculate", "verify")
        workflow.add_edge("verify", "format")
        workflow.add_edge("format", END)

        # Set entry point
        workflow.set_entry_point("extract")

        return workflow

    @staticmethod
    def _generate_cache_key(text: str, metadata: Dict[str, Any]) -> str:
        """Generate a cache key for a document.

        Args:
            text: Document text.
            metadata: Document metadata.

        Returns:
            A hash string to use as cache key.
        """
        # Create a deterministic representation for hashing
        content = f"{text[:1000]}-{json.dumps(metadata, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()

    @staticmethod
    def _split_document(
        text: str, max_chunk_size: int = 15000, overlap: int = 1000
    ) -> List[str]:
        """Split a document into chunks for processing.

        Args:
            text: Document text.
            max_chunk_size: Maximum size of each chunk.
            overlap: Overlap between chunks.

        Returns:
            List of text chunks.
        """
        chunks = []
        start = 0

        while start < len(text):
            end = min(start + max_chunk_size, len(text))

            # Try to find a sentence boundary to end on
            if end < len(text):
                sentence_breaks = [
                    text.rfind(". ", start, end),
                    text.rfind(".\n", start, end),
                    text.rfind("\n\n", start, end),
                ]
                break_point = max(sentence_breaks)
                if break_point != -1:
                    end = break_point + 2  # Include the period and space

            chunks.append(text[start:end])
            start = end - overlap  # Overlap with previous chunk

        return chunks

    @staticmethod
    def _fix_date_format(date_str: Optional[str]) -> Optional[str]:
        """Attempt to fix a malformed date string.

        Args:
            date_str: The date string to fix.

        Returns:
            Fixed date string or None if cannot be fixed.
        """
        if not date_str:
            return None

        # Common date patterns
        patterns = [
            (r"(\d{1,2})/(\d{1,2})/(\d{4})", r"\3-\1-\2"),  # MM/DD/YYYY to YYYY-MM-DD
            (r"(\d{1,2})-(\d{1,2})-(\d{4})", r"\3-\1-\2"),  # MM-DD-YYYY to YYYY-MM-DD
            (
                r"(\w+)\s+(\d{1,2}),\s+(\d{4})",
                lambda m: _convert_month_name_date(m),
            ),  # Month DD, YYYY
        ]

        for pattern, replacement in patterns:
            if re.match(pattern, date_str):
                if callable(replacement):
                    return replacement(re.match(pattern, date_str))
                else:
                    return re.sub(pattern, replacement, date_str)

        return None

    @staticmethod
    def _extract_relevant_sample(
        text: str, search_term: str, context_size: int = 5000
    ) -> str:
        """Extract a relevant sample of text containing search terms.

        Args:
            text: Full document text.
            search_term: Term to search for.
            context_size: Size of context to extract.

        Returns:
            A relevant sample of the text.
        """
        # If text is small enough, return it all
        if len(text) <= context_size:
            return text

        # Find occurrences of search term
        search_term = search_term.lower()
        text_lower = text.lower()

        # Find first occurrence
        pos = text_lower.find(search_term)
        if pos == -1:
            # Search for individual words if exact term not found
            words = search_term.split()
            for word in words:
                if len(word) > 3:  # Only consider significant words
                    pos = text_lower.find(word)
                    if pos != -1:
                        break

        # If nothing found, return beginning of document
        if pos == -1:
            return text[:context_size]

        # Extract context around the occurrence
        start = max(0, pos - context_size // 2)
        end = min(len(text), pos + context_size // 2)

        # Try to start and end at sentence boundaries
        if start > 0:
            sentence_start = text.rfind(".", 0, start)
            if sentence_start != -1:
                start = sentence_start + 1

        if end < len(text):
            sentence_end = text.find(".", end)
            if sentence_end != -1:
                end = sentence_end + 1

        return text[start:end]

    async def _load_jurisdiction_rules(self, jurisdiction: str) -> Dict[str, Any]:
        """Load jurisdiction-specific deadline rules from the MCP server.

        Args:
            jurisdiction: The jurisdiction to load rules for.

        Returns:
            Dictionary of jurisdiction rules.
        """
        try:
            with self.mcp_circuit_breaker:
                # Query MCP server for jurisdiction rules
                from modelcontextprotocol.client import ModelContextClient

                mcp_client = ModelContextClient()
                result = await mcp_client.query(
                    f"""
                SELECT rule_id, name, description, max_days, min_days, exclusions, holidays, citations
                FROM jurisdiction_rules
                WHERE jurisdiction = '{jurisdiction}'
                """
                )

                # Convert to dictionary keyed by rule_id
                rules = {}
                for row in result.get("data", []):
                    rules[row["rule_id"]] = row

                logger.info(
                    f"Loaded {len(rules)} jurisdiction rules for {jurisdiction}"
                )
                return rules

        except Exception as e:
            logger.error(f"Error loading jurisdiction rules: {str(e)}")
            # Return empty rules on failure
            return {}

    async def _store_deadlines(
        self,
        deadlines: List[LegalDeadline],
        tenant_id: str,
        document_id: str,
        case_id: Optional[str] = None,
        auto_extracted: bool = True,
    ) -> List[str]:
        """Store extracted deadlines in the database.

        Args:
            deadlines: List of LegalDeadline objects.
            tenant_id: Tenant ID for multi-tenant isolation.
            document_id: Source document ID.
            case_id: Optional case ID if the document is associated with a case.
            auto_extracted: Whether these deadlines were automatically extracted.

        Returns:
            List of stored deadline IDs.
        """
        stored_ids = []
        try:
            # Store deadlines in the database
            for deadline in deadlines:
                # Set basic metadata
                deadline_data = deadline.dict()
                deadline_data["tenant_id"] = tenant_id
                deadline_data["document_id"] = document_id
                if case_id:
                    deadline_data["case_id"] = case_id

                # Set validation fields
                deadline_data[
                    "validation_status"
                ] = "pending"  # All auto-extracted deadlines start as pending
                deadline_data["auto_extracted"] = auto_extracted
                deadline_data["validated_by"] = None
                deadline_data["validated_at"] = None

                # Store in database
                result = (
                    await self.supabase.client.schema("tenants")
                    .table("deadlines")
                    .insert(deadline_data)
                    .execute()
                )
                if result.data and len(result.data) > 0:
                    stored_ids.append(result.data[0]["id"])

            logger.info(
                f"Stored {len(deadlines)} deadlines for document {document_id} with pending validation"
            )
            return stored_ids

        except Exception as e:
            logger.error(f"Error storing deadlines: {str(e)}")
            return []


def _convert_month_name_date(match) -> str:
    """Convert a month name date to ISO format.

    Args:
        match: Regex match object containing month, day, year.

    Returns:
        ISO formatted date string.
    """
    month_names = {
        "january": 1,
        "february": 2,
        "march": 3,
        "april": 4,
        "may": 5,
        "june": 6,
        "july": 7,
        "august": 8,
        "september": 9,
        "october": 10,
        "november": 11,
        "december": 12,
    }

    month_str = match.group(1).lower()
    day = match.group(2)
    year = match.group(3)

    month = month_names.get(month_str, 1)
    return f"{year}-{month:02d}-{int(day):02d}"


# Initialize singleton instance
deadline_extraction_service = DeadlineExtractionService()
