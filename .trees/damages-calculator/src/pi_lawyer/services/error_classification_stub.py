"""
STUB FILE - NOT FOR PRODUCTION USE

This is a temporary stub implementation of the ErrorClassification service.
Must be replaced with actual implementation before production deployment.
Created: 2025-05-22

This stub is used to satisfy import requirements during testing.
"""

import logging
import warnings
from enum import Enum

# Setup logging
logger = logging.getLogger(__name__)

# Emit warning when this stub is imported
warnings.warn(
    "Using stub implementation of ErrorClassification - NOT SUITABLE FOR PRODUCTION",
    category=RuntimeWarning
)

class ErrorCategory(Enum):
    """Categories for error classification"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    RATE_LIMIT = "rate_limit"
    RESOURCE_NOT_FOUND = "resource_not_found"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL_SERVER = "internal_server"
    UNKNOWN = "unknown"


class ErrorClassifier:
    """
    Stub implementation of ErrorClassifier.
    
    TODO: Replace with actual implementation before production.
    """
    
    def __init__(self):
        logger.warning("Stub ErrorClassifier initialized - NOT FOR PRODUCTION USE")
        
    def classify(self, error: Exception) -> ErrorCategory:
        """
        Stub method to classify an error into a category
        
        Args:
            error: The exception to classify
            
        Returns:
            ErrorCategory: The classified category
        """
        logger.warning(f"[STUB] Classifying error: {type(error).__name__}")
        
        # Simple stub classification based on error name
        error_name = type(error).__name__.lower()
        
        if "auth" in error_name:
            return ErrorCategory.AUTHENTICATION
        elif "permission" in error_name or "forbidden" in error_name:
            return ErrorCategory.AUTHORIZATION
        elif "validation" in error_name or "invalid" in error_name:
            return ErrorCategory.VALIDATION
        elif "notfound" in error_name or "not_found" in error_name:
            return ErrorCategory.RESOURCE_NOT_FOUND
        elif "timeout" in error_name or "connection" in error_name:
            return ErrorCategory.EXTERNAL_SERVICE
        elif "limit" in error_name or "quota" in error_name:
            return ErrorCategory.RATE_LIMIT
        elif "server" in error_name or "internal" in error_name:
            return ErrorCategory.INTERNAL_SERVER
        else:
            return ErrorCategory.UNKNOWN
    
    def get_response_code(self, category: ErrorCategory) -> int:
        """
        Stub method to get HTTP response code for an error category
        
        Args:
            category: The error category
            
        Returns:
            int: HTTP status code
        """
        code_map = {
            ErrorCategory.AUTHENTICATION: 401,
            ErrorCategory.AUTHORIZATION: 403,
            ErrorCategory.VALIDATION: 400,
            ErrorCategory.RESOURCE_NOT_FOUND: 404,
            ErrorCategory.EXTERNAL_SERVICE: 502,
            ErrorCategory.RATE_LIMIT: 429,
            ErrorCategory.INTERNAL_SERVER: 500,
            ErrorCategory.UNKNOWN: 500
        }
        return code_map.get(category, 500)
