"""
Redis-based document processing worker.

This module implements a standalone worker process that pulls jobs from
the Redis queue and processes them. It can be run as a separate process
from the main application for better scalability and fault tolerance.
"""

import asyncio
import logging
import os
import signal
import sys
import time
from typing import Any, Dict

from .circuit_breaker import with_circuit_breaker
from .deadline_extraction_service import deadline_extraction_service
from .document_analysis_service import DocumentAnalysisService
from .document_classifier_service import DocumentClassifierService
from .document_processing_transaction import (
    DocumentProcessingTransaction,
    TransactionError,
)
from .error_classification import ErrorClassifier
from .redis_lock_service import get_redis_lock_service
from .redis_queue_service import get_redis_queue_service
from .tenant_document_embedding_service import TenantDocumentEmbeddingService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DocumentWorker:
    """Worker process that pulls document processing jobs from Redis queue."""

    def __init__(self, worker_id: str = None):
        """
        Initialize document worker.

        Args:
            worker_id: Unique identifier for this worker instance
        """
        self.worker_id = worker_id or f"worker-{os.getpid()}"
        self.running = False
        self.processed_count = 0
        self.error_count = 0

        # Initialize services
        self.queue_service = get_redis_queue_service()
        self.lock_service = get_redis_lock_service()
        self.classifier = DocumentClassifierService()
        self.analyzer = DocumentAnalysisService()
        self.embedding_service = TenantDocumentEmbeddingService()
        self.transaction_manager = DocumentProcessingTransaction()

        # Retry configuration
        self.max_retries = int(os.getenv("PROCESSING_MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("PROCESSING_RETRY_DELAY", "5"))  # seconds

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._handle_shutdown)
        signal.signal(signal.SIGINT, self._handle_shutdown)

        logger.info(f"Document worker {self.worker_id} initialized")

    def start(self):
        """Start the worker process."""
        logger.info(f"Document worker {self.worker_id} starting")
        self.running = True

        try:
            self._worker_loop()
        except Exception as e:
            logger.error(
                f"Unhandled exception in worker process: {str(e)}", exc_info=True
            )
            self.error_count += 1
        finally:
            logger.info(
                f"Document worker {self.worker_id} stopping - processed {self.processed_count} jobs with {self.error_count} errors"
            )

    def stop(self):
        """Stop the worker process."""
        logger.info(f"Document worker {self.worker_id} stopping")
        self.running = False

    def _handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down worker {self.worker_id}")
        self.stop()
        sys.exit(0)

    def _worker_loop(self):
        """Main worker loop that processes documents from the queue."""
        logger.info(f"Document worker {self.worker_id} started")

        while self.running:
            try:
                # Get job from queue - this blocks until a job is available
                job = self.queue_service.dequeue()

                if not job:
                    # No jobs available, wait a bit and try again
                    time.sleep(1.0)
                    continue

                job_id = job["id"]
                document_id = job["document_id"]
                tenant_id = job["tenant_id"]

                # Try to acquire a lock for this document_id to prevent concurrent processing
                lock_owner_id = f"{self.worker_id}:{job_id}"
                document_lock_name = f"doc:{document_id}"
                lock_acquired = self.lock_service.acquire_lock(
                    document_lock_name,
                    lock_owner_id,
                    timeout=3600,  # 1 hour max processing time
                    blocking=False,
                )

                if not lock_acquired:
                    logger.warning(
                        f"Document {document_id} already being processed by another worker, requeuing job {job_id}"
                    )
                    # Put back in queue with small delay
                    self.queue_service.fail_job(
                        job_id,
                        "Document already being processed by another worker",
                        retry=True,
                    )
                    continue

                try:
                    # Process the document with proper error handling
                    self._process_document(job)
                    self.processed_count += 1
                except Exception as e:
                    self.error_count += 1
                    error_type = type(e).__name__
                    error_message = str(e)

                    # Determine if the error is retryable
                    should_retry = ErrorClassifier.should_retry(e)
                    retry_count = job.get("retry_count", 0)
                    can_retry = retry_count < self.max_retries

                    logger.error(
                        f"Error processing job {job_id} (document {document_id}): {error_type} - {error_message}",
                        exc_info=True,
                    )

                    # Log with error classification
                    ErrorClassifier.log_error_with_classification(
                        e,
                        {
                            "job_id": job_id,
                            "document_id": document_id,
                            "tenant_id": tenant_id,
                            "retry_count": retry_count,
                            "max_retries": self.max_retries,
                            "can_retry": can_retry and should_retry,
                        },
                    )

                    # Mark job as failed (with retry if appropriate)
                    self.queue_service.fail_job(
                        job_id=job_id,
                        error=f"{error_type}: {error_message}",
                        retry=should_retry and can_retry,
                    )
                finally:
                    # Always release the document lock
                    self.lock_service.release_lock(document_lock_name, lock_owner_id)

            except Exception as e:
                logger.error(
                    f"Unexpected error in worker loop: {str(e)}", exc_info=True
                )
                # Sleep briefly to avoid tight error loops
                time.sleep(1)

        logger.info(f"Document worker {self.worker_id} stopped")

    def _process_document(self, job: Dict[str, Any]):
        """
        Process a document job.

        Args:
            job: Job data dictionary
        """
        job_id = job["id"]
        document_id = job["document_id"]
        tenant_id = job["tenant_id"]
        transaction_id = None

        try:
            # Start a new transaction for this processing job
            transaction_id = asyncio.run(
                self.transaction_manager.start(document_id, tenant_id)
            )
            logger.info(
                f"Started transaction {transaction_id} for job {job_id} (document {document_id})"
            )

            self.queue_service.update_job_status(
                job_id,
                {
                    "message": "Processing started with transaction management",
                    "transaction_id": transaction_id,
                },
            )

            # Step 1: Classify document
            self.queue_service.update_job_status(
                job_id, {"message": "Classifying document", "progress": 20}
            )

            classification = self._classify_document(job)

            # Register the classification result with the transaction
            asyncio.run(
                self.transaction_manager.register_resource(
                    transaction_id=transaction_id,
                    resource_type="classification",
                    resource_id=f"{document_id}_classification",
                    location="memory",
                    metadata={
                        "document_type": classification["document_type"],
                        "analysis_type": classification["analysis_type"],
                    },
                )
            )

            self.queue_service.update_job_status(
                job_id,
                {
                    "message": f"Document classified as {classification['document_type']}",
                    "progress": 30,
                    "classification": classification,
                },
            )

            # Step 2: Analyze document
            self.queue_service.update_job_status(
                job_id,
                {
                    "message": f"Analyzing document with {classification['analysis_type']} analyzer",
                    "progress": 40,
                },
            )

            result = self._analyze_document(job, classification)

            # Register the analysis result with the transaction
            asyncio.run(
                self.transaction_manager.register_resource(
                    transaction_id=transaction_id,
                    resource_type="analysis_result",
                    resource_id=f"{document_id}_analysis",
                    location="memory",
                    metadata={
                        "document_id": document_id,
                        "analysis_type": classification["analysis_type"],
                    },
                )
            )

            self.queue_service.update_job_status(
                job_id,
                {
                    "message": "Document analysis completed",
                    "progress": 50,
                    "analysis_completed": True,
                },
            )

            # Step 3: Generate embeddings for vector search
            self.queue_service.update_job_status(
                job_id,
                {
                    "message": "Generating text embeddings for semantic search",
                    "progress": 70,
                },
            )

            # Extract text content from the analysis result
            text_content = self._extract_text_from_result(result)

            # Prepare document metadata for embeddings
            doc_metadata = {
                **job.get("metadata", {}),
                "document_id": document_id,
                "document_type": classification["document_type"],
                "analysis_type": classification["analysis_type"],
                "file_name": job.get("file_name", ""),
                "tenant_id": job["tenant_id"],
                "case_id": job.get("metadata", {}).get("case_id"),
                "user_id": job.get("user_id", ""),
                "transaction_id": transaction_id,  # Track which transaction created these embeddings
            }

            # Update status for embedding generation
            self.queue_service.update_job_status(
                job_id,
                {
                    "message": "Chunking document and generating embeddings",
                    "progress": 80,
                },
            )

            # Pass transaction_id to the embedding service
            embedding_result = asyncio.run(
                self.embedding_service.process_document(
                    document_id=document_id,
                    text_content=text_content,
                    tenant_id=job["tenant_id"],
                    document_metadata=doc_metadata,
                    transaction_id=transaction_id,  # Pass transaction ID to enable rollback if needed
                )
            )

            # Update status with embedding results
            self.queue_service.update_job_status(
                job_id,
                {
                    "message": f"Generated {embedding_result['chunk_count']} embeddings for semantic search",
                    "progress": 70,
                    "embedding_result": {
                        "chunk_count": embedding_result["chunk_count"],
                        "vector_count": embedding_result["vector_count"],
                        "pool_namespace": embedding_result["pool_namespace"],
                    },
                },
            )

            logger.info(
                f"Document {document_id} embedding completed with {embedding_result['chunk_count']} chunks"
            )

            # Step 4: Extract legal deadlines if document type is legal
            if classification["document_type"].lower() in [
                "legal",
                "pleading",
                "court_filing",
                "court_order",
                "contract",
                "agreement",
                "motion",
                "brief",
                "opinion",
            ]:
                self.queue_service.update_job_status(
                    job_id,
                    {
                        "message": "Extracting legal deadlines from document",
                        "progress": 80,
                    },
                )

                # Prepare metadata for deadline extraction
                deadline_metadata = {
                    "document_id": document_id,
                    "tenant_id": tenant_id,
                    "document_type": classification["document_type"],
                    "jurisdiction": job.get("metadata", {}).get(
                        "jurisdiction", "Unknown"
                    ),
                    "case_id": job.get("metadata", {}).get("case_id"),
                    "case_type": job.get("metadata", {}).get(
                        "case_type", "personal_injury"
                    ),
                    "id": document_id,  # Used for cache key generation
                }

                # Extract text content for deadline extraction
                text_content = self._extract_text_from_result(result)

                try:
                    # Process document for deadline extraction
                    deadline_result = asyncio.run(
                        deadline_extraction_service.process_document(
                            document_text=text_content, metadata=deadline_metadata
                        )
                    )

                    # Register deadlines with transaction
                    asyncio.run(
                        self.transaction_manager.register_resource(
                            transaction_id=transaction_id,
                            resource_type="deadlines",
                            resource_id=f"{document_id}_deadlines",
                            location="database",
                            metadata={
                                "document_id": document_id,
                                "deadline_count": len(
                                    deadline_result.get("deadlines", [])
                                ),
                                "has_critical": any(
                                    d.get("priority") == "critical"
                                    for d in deadline_result.get("deadlines", [])
                                ),
                            },
                        )
                    )

                    # Update status with deadline results
                    self.queue_service.update_job_status(
                        job_id,
                        {
                            "message": f"Extracted {len(deadline_result.get('deadlines', []))} legal deadlines",
                            "progress": 90,
                            "deadline_result": {
                                "deadline_count": len(
                                    deadline_result.get("deadlines", [])
                                ),
                                "has_critical": any(
                                    d.get("priority") == "critical"
                                    for d in deadline_result.get("deadlines", [])
                                ),
                                "has_warnings": len(deadline_result.get("warnings", []))
                                > 0,
                            },
                        },
                    )

                    logger.info(
                        f"Document {document_id} deadline extraction completed with {len(deadline_result.get('deadlines', []))} deadlines"
                    )

                except Exception as e:
                    logger.warning(
                        f"Deadline extraction failed for document {document_id}: {str(e)}"
                    )
                    # Don't fail the whole transaction just because deadline extraction failed
                    self.queue_service.update_job_status(
                        job_id,
                        {
                            "message": "Deadline extraction skipped due to error",
                            "progress": 90,
                            "deadline_error": str(e),
                        },
                    )
            else:
                # Skip deadline extraction for non-legal documents
                self.queue_service.update_job_status(
                    job_id,
                    {
                        "message": "Skipping deadline extraction (not a legal document)",
                        "progress": 90,
                    },
                )

            # Commit the transaction since everything succeeded
            transaction_result = asyncio.run(
                self.transaction_manager.commit(transaction_id)
            )
            logger.info(
                f"Transaction {transaction_id} committed successfully for document {document_id}"
            )

            # Mark job as completed
            self.queue_service.complete_job(
                job_id,
                {
                    "document_type": classification["document_type"],
                    "analysis_type": classification["analysis_type"],
                    "embedding_status": "completed",
                    "chunk_count": embedding_result["chunk_count"],
                    "transaction_id": transaction_id,
                    "transaction_status": "committed",
                },
            )

            logger.info(
                f"Job {job_id} for document {document_id} completed successfully"
            )

        except TransactionError as te:
            logger.error(f"Transaction error in job {job_id}: {str(te)}")

            if transaction_id:
                # Attempt rollback
                try:
                    asyncio.run(self.transaction_manager.rollback(transaction_id))
                    logger.info(
                        f"Successfully rolled back transaction {transaction_id}"
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Error rolling back transaction {transaction_id}: {str(rollback_error)}",
                        exc_info=True,
                    )

            # Let the main loop handle retry logic
            raise

        except Exception as e:
            # General processing errors
            error_message = str(e)
            logger.error(f"Error processing document {document_id}: {error_message}")

            # If we have a transaction, register the error and roll back
            if transaction_id:
                try:
                    error_type = type(e).__name__
                    asyncio.run(
                        self.transaction_manager.register_error(
                            transaction_id=transaction_id,
                            error_type=error_type,
                            error_message=error_message,
                            step="document_processing",
                        )
                    )
                    asyncio.run(self.transaction_manager.rollback(transaction_id))
                    logger.info(
                        f"Registered error and rolled back transaction {transaction_id}"
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Error during error registration/rollback: {str(rollback_error)}"
                    )

            # Let the main loop handle retry logic
            raise

    @with_circuit_breaker("document_classifier")
    def _classify_document(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Classify a document with circuit breaker protection.

        Args:
            job: Job data dictionary

        Returns:
            Classification result dictionary
        """
        return asyncio.run(
            self.classifier.classify_document(
                job["file_path"],
                job.get("file_name", ""),
                job.get("file_type", ""),
                job.get("metadata", {}),
            )
        )

    @with_circuit_breaker("document_analyzer")
    def _analyze_document(
        self, job: Dict[str, Any], classification: Dict[str, Any]
    ) -> Any:
        """
        Analyze a document with circuit breaker protection.

        Args:
            job: Job data dictionary
            classification: Document classification result

        Returns:
            Analysis result
        """
        # Choose analysis method based on classification
        if classification["analysis_type"] == "medical":
            return self.analyzer.analyze_medical_form(job["file_path"])
        elif classification["analysis_type"] == "tasks":
            return self.analyzer.extract_tasks_from_document(job["file_path"])
        else:
            return self.analyzer.analyze_legal_document(
                job["file_path"], classification["document_type"]
            )

    @staticmethod
    def _extract_text_from_result(result: Any) -> str:
        """
        Extract text content from analysis result.

        Args:
            result: Analysis result object

        Returns:
            Text content as string
        """
        if isinstance(result, dict) and "text" in result:
            return result["text"]
        elif isinstance(result, str):
            return result
        else:
            # Try to convert to string if it's another format
            return str(result)


def main():
    """Entry point for running worker as a standalone process."""
    worker_id = os.getenv("WORKER_ID", f"worker-{os.getpid()}")
    worker = DocumentWorker(worker_id)

    logger.info(f"Starting document worker {worker_id}")
    worker.start()


if __name__ == "__main__":
    main()
