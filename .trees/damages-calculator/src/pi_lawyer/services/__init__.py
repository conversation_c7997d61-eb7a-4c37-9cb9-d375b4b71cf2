"""Service modules for PI Lawyer AI."""

# Import and export service modules
try:
    # Task embedding service
    # Circuit breaker service
    from .circuit_breaker import CircuitBreaker, CircuitState
    from .document_analysis_service import DocumentAnalysisService
    from .document_embedding_utils import (
        calculate_embedding,
        chunk_text,
        extract_text_chunks,
        get_embedding,
    )

    # Document services
    from .document_worker import DocumentWorker

    # Error classification service
    from .error_classification import ErrorCategory, ErrorClassifier
    from .task_embedding_service import TaskEmbeddingService, task_embedding_service
    
    # Make stubs available if needed
    try:
        from .circuit_breaker_stub import CircuitBreakerStub
        from .error_classification_stub import ErrorClassifier as ErrorClassifierStub
    except ImportError:
        pass  # Stub files are optional

except ImportError as e:
    import warnings
    warnings.warn(f"Error importing service module: {e}")

# Update __all__ to include all exported symbols
__all__ = [
    "TaskEmbeddingService", "task_embedding_service",
    "CircuitBreaker", "CircuitState",
    "ErrorClassifier", "ErrorCategory",
    "DocumentWorker", "DocumentAnalysisService",
    "get_embedding", "extract_text_chunks", "chunk_text", "calculate_embedding"
]
