import json
import os

import openai

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


def build_prompt(text: str) -> str:
    """
    Construct the prompt for GPT-4.1 to generate a legal summary, section summaries, and highlights.
    """
    return (
        "You are a legal assistant. Given the following legal document, "
        "provide a concise overall summary, section summaries (Facts, Issues, Orders if present), "
        "and extract key highlights (parties, dates, deadlines, statutes, etc.) with their text spans.\n\n"
        f"Document:\n{text}\n\n"
        "Respond in this JSON format:\n"
        "{"
        '"summary": "...",'
        '"sections": [{"title": "Facts", "summary": "..."}, ...],'
        '"highlights": [{"text": "...", "type": "date", "start": 123, "end": 130}, ...]'
        "}"
    )


def summarize_and_highlight(text: str) -> dict:
    """
    Calls OpenAI GPT-4.1 to summarize the document and extract highlights.
    Returns a dictionary with 'summary', 'sections', and 'highlights'.
    """
    prompt = build_prompt(text)
    response = openai.ChatCompletion.create(
        model="gpt-4-1106-preview",  # Replace with "gpt-4.1-2025-04-14" when available
        messages=[{"role": "system", "content": prompt}],
        max_tokens=2048,
        temperature=0.3,
        api_key=OPENAI_API_KEY,
    )
    content = response.choices[0].message["content"]
    try:
        result = json.loads(content)
    except Exception as e:
        # Handle/Log error, maybe return a fallback
        result = {"summary": "", "sections": [], "highlights": [], "error": str(e)}
    return result
