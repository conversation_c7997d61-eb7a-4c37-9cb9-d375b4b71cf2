"""
Document Parser Service

This module provides a service for parsing documents using Gemini 2.0 Flash
to extract structured information from complex documents like medical forms,
invoices, and other semi-structured content.
"""

import base64
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, Optional

import google.generativeai as genai
from google.generativeai.types import HarmBlockThreshold, HarmCategory

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentParserService:
    """
    Service to parse documents using Gemini 2.0 Flash model to extract
    structured information from complex documents.
    """

    def __init__(self):
        """Initialize the document parser service with Gemini API"""
        api_key = os.getenv("GOOGLE_API_KEY", os.getenv("GOOGLE_GENAI_API_KEY", ""))
        if not api_key:
            logger.warning(
                "No Google Gemini API key found. Parser service will not function."
            )

        # Configure the Gemini API client
        genai.configure(api_key=api_key)

        # Set default model
        self.primary_model = "gemini-2.0-flash-lite-001"
        self.fallback_model = "gemini-1.5-flash"

        # Load document type schemas
        self.schemas = self._load_schemas()

        # Configure safety settings dynamically, skipping unavailable categories
        self.safety_settings = {}
        for cat_name in [
            "HARASSMENT",
            "HATE_SPEECH",
            "SEXUALLY_EXPLICIT",
            "DANGEROUS_CONTENT",
        ]:
            if hasattr(HarmCategory, cat_name):
                self.safety_settings[
                    getattr(HarmCategory, cat_name)
                ] = HarmBlockThreshold.MEDIUM_AND_ABOVE
            else:
                logger.warning(
                    f"HarmCategory.{cat_name} not available, skipping safety setting"
                )

    def _load_schemas(self) -> Dict[str, Any]:
        """Load JSON schemas for different document types"""

        # Basic schemas for common document types
        schemas = {
            # Medical form schema
            "medical_form": {
                "type": "object",
                "properties": {
                    "patient_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "id": {"type": "string"},
                            "date_of_birth": {"type": "string"},
                            "gender": {"type": "string"},
                            "contact_information": {"type": "string"},
                        },
                    },
                    "medical_details": {
                        "type": "object",
                        "properties": {
                            "primary_diagnosis": {"type": "string"},
                            "secondary_diagnoses": {
                                "type": "array",
                                "items": {"type": "string"},
                            },
                            "symptoms": {"type": "array", "items": {"type": "string"}},
                            "onset_date": {"type": "string"},
                        },
                    },
                    "treatment": {
                        "type": "object",
                        "properties": {
                            "plan": {"type": "string"},
                            "medications": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "dosage": {"type": "string"},
                                        "frequency": {"type": "string"},
                                        "duration": {"type": "string"},
                                    },
                                },
                            },
                            "procedures": {
                                "type": "array",
                                "items": {"type": "string"},
                            },
                        },
                    },
                    "provider_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "id": {"type": "string"},
                            "facility": {"type": "string"},
                            "signature": {"type": "boolean"},
                        },
                    },
                    "insurance": {
                        "type": "object",
                        "properties": {
                            "provider": {"type": "string"},
                            "policy_number": {"type": "string"},
                            "group_number": {"type": "string"},
                            "coverage_details": {"type": "string"},
                        },
                    },
                    "additional_notes": {"type": "string"},
                },
            },
            # Invoice/billing schema
            "invoice": {
                "type": "object",
                "properties": {
                    "invoice_info": {
                        "type": "object",
                        "properties": {
                            "invoice_number": {"type": "string"},
                            "date": {"type": "string"},
                            "due_date": {"type": "string"},
                            "total_amount": {"type": "string"},
                        },
                    },
                    "biller_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "address": {"type": "string"},
                            "contact": {"type": "string"},
                            "tax_id": {"type": "string"},
                        },
                    },
                    "client_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "address": {"type": "string"},
                            "account_number": {"type": "string"},
                        },
                    },
                    "line_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "description": {"type": "string"},
                                "quantity": {"type": "string"},
                                "unit_price": {"type": "string"},
                                "amount": {"type": "string"},
                            },
                        },
                    },
                    "totals": {
                        "type": "object",
                        "properties": {
                            "subtotal": {"type": "string"},
                            "tax": {"type": "string"},
                            "total": {"type": "string"},
                        },
                    },
                    "payment_info": {
                        "type": "object",
                        "properties": {
                            "terms": {"type": "string"},
                            "methods": {"type": "string"},
                            "instructions": {"type": "string"},
                        },
                    },
                },
            },
            # Legal document schema
            "legal_document": {
                "type": "object",
                "properties": {
                    "document_info": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "date": {"type": "string"},
                            "case_number": {"type": "string"},
                            "document_type": {"type": "string"},
                        },
                    },
                    "parties": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "role": {"type": "string"},
                                "contact_info": {"type": "string"},
                            },
                        },
                    },
                    "key_sections": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "content": {"type": "string"},
                            },
                        },
                    },
                    "cited_laws": {"type": "array", "items": {"type": "string"}},
                    "action_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "description": {"type": "string"},
                                "deadline": {"type": "string"},
                                "responsible_party": {"type": "string"},
                            },
                        },
                    },
                    "signatures": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "role": {"type": "string"},
                                "date": {"type": "string"},
                                "signed": {"type": "boolean"},
                            },
                        },
                    },
                },
            },
            # General document schema (fallback)
            "general": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "date": {"type": "string"},
                    "author": {"type": "string"},
                    "recipient": {"type": "string"},
                    "key_points": {"type": "array", "items": {"type": "string"}},
                    "main_sections": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "heading": {"type": "string"},
                                "content": {"type": "string"},
                            },
                        },
                    },
                    "metadata": {
                        "type": "object",
                        "properties": {
                            "document_type": {"type": "string"},
                            "important_dates": {
                                "type": "array",
                                "items": {"type": "string"},
                            },
                            "important_entities": {
                                "type": "array",
                                "items": {"type": "string"},
                            },
                        },
                    },
                },
            },
        }

        return schemas

    def _get_schema_for_document_type(self, document_type: str) -> Dict[str, Any]:
        """Get the appropriate schema for a document type with fallback to general"""
        return self.schemas.get(document_type, self.schemas.get("general", {}))

    def _encode_file(self, file_path: str) -> Dict[str, Any]:
        """Encode a file for the Gemini API"""

        # Determine MIME type based on file extension
        ext = os.path.splitext(file_path)[1].lower()
        mime_type = "application/pdf"  # Default to PDF

        if ext == ".jpg" or ext == ".jpeg":
            mime_type = "image/jpeg"
        elif ext == ".png":
            mime_type = "image/png"
        elif ext == ".txt":
            mime_type = "text/plain"
        elif ext == ".docx":
            mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

        # Read the file as bytes
        with open(file_path, "rb") as f:
            file_bytes = f.read()

        # Encode as base64
        file_data = base64.b64encode(file_bytes).decode("utf-8")

        # Create data structure for Gemini
        return {"mime_type": mime_type, "data": file_data}

    async def parse_document(
        self,
        file_path: str,
        document_type: str,
        custom_schema: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Parse a document using Gemini 2.0 Flash to extract structured information

        Args:
            file_path: Path to the document file
            document_type: Type of document (medical_form, invoice, etc.)
            custom_schema: Optional JSON schema to guide the extraction

        Returns:
            Parsed structured data from the document or error information
        """
        try:
            # Get appropriate schema based on document type
            schema = (
                custom_schema
                if custom_schema
                else self._get_schema_for_document_type(document_type)
            )

            # Encode the file
            file_data = self._encode_file(file_path)

            # Prepare the model
            model = genai.GenerativeModel(
                model_name=self.primary_model,
                generation_config={
                    "temperature": 0.1,  # Low temperature for deterministic outputs
                    "top_p": 0.95,
                    "top_k": 40,
                    "response_mime_type": "application/json",
                    "structured_response_schema": schema,
                },
                safety_settings=self.safety_settings,
            )

            # Prepare the prompt
            prompt = f"""
            You are a document parsing assistant specialized in extracting structured information.

            Please analyze the provided document which is a {document_type}.

            Extract all relevant information according to the schema provided.
            Be precise and thorough.

            If you can't extract certain information with confidence, indicate it with null or "unknown".
            For dates, try to standardize to YYYY-MM-DD format when possible.

            Your output should be valid JSON matching the provided schema.
            """

            # Call Gemini with structured output
            response = await model.generate_content_async([prompt, file_data])

            # Parse the response
            try:
                # The model is expected to return JSON
                parsed_data = json.loads(response.text)

                # Add metadata about parsing
                result = {
                    "parsed_data": parsed_data,
                    "model_used": self.primary_model,
                    "parsing_timestamp": datetime.now().isoformat(),
                    "document_type": document_type,
                    "success": True,
                }

                return result
            except json.JSONDecodeError as e:
                # If the primary model fails to return valid JSON, try the fallback
                logger.warning(f"Primary model failed to return valid JSON: {str(e)}")
                return await self._try_fallback_model(
                    prompt, file_data, document_type, schema
                )

        except Exception as e:
            logger.error(f"Error parsing document: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "document_type": document_type,
                "parsing_timestamp": datetime.now().isoformat(),
            }

    async def _try_fallback_model(
        self,
        prompt: str,
        file_data: Dict[str, Any],
        document_type: str,
        schema: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Try parsing with the fallback model if primary model fails"""
        try:
            logger.info(f"Falling back to {self.fallback_model} for document parsing")

            # Prepare the fallback model
            fallback = genai.GenerativeModel(
                model_name=self.fallback_model,
                generation_config={
                    "temperature": 0.1,
                    "top_p": 0.95,
                    "top_k": 40,
                    "response_mime_type": "application/json",
                    "structured_response_schema": schema,
                },
                safety_settings=self.safety_settings,
            )

            # Call fallback model
            response = await fallback.generate_content_async([prompt, file_data])

            try:
                # The model is expected to return JSON
                parsed_data = json.loads(response.text)

                # Add metadata about parsing
                result = {
                    "parsed_data": parsed_data,
                    "model_used": self.fallback_model,
                    "parsing_timestamp": datetime.now().isoformat(),
                    "document_type": document_type,
                    "success": True,
                }

                return result
            except json.JSONDecodeError:
                # Both models failed to return valid JSON
                return {
                    "success": False,
                    "error": "Both primary and fallback models failed to generate valid JSON",
                    "document_type": document_type,
                    "parsing_timestamp": datetime.now().isoformat(),
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Fallback model error: {str(e)}",
                "document_type": document_type,
                "parsing_timestamp": datetime.now().isoformat(),
            }


# Singleton instance
_parser_instance = None


def get_document_parser():
    """Get the singleton instance of DocumentParserService"""
    global _parser_instance
    if _parser_instance is None:
        _parser_instance = DocumentParserService()
    return _parser_instance
