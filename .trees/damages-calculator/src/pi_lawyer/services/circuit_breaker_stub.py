"""
STUB FILE - NOT FOR PRODUCTION USE

This is a temporary stub implementation of the CircuitBreaker service.
Must be replaced with actual implementation before production deployment.
Created: 2025-05-22

This stub is used to satisfy import requirements during testing.
"""

import functools
import logging
import warnings
from enum import Enum
from typing import Optional

# Setup logging
logger = logging.getLogger(__name__)

# Emit warning when this stub is imported
warnings.warn(
    "Using stub implementation of CircuitBreaker - NOT SUITABLE FOR PRODUCTION",
    category=RuntimeWarning
)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"  # Normal operation, requests flow through
    OPEN = "open"      # Failing, requests are blocked
    HALF_OPEN = "half_open"  # Testing if service has recovered


class CircuitBreakerStub:
    """
    Stub implementation of CircuitBreaker.
    
    TODO: Replace with actual implementation before production.
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 30, 
                 name: str = "default", failure_cache_key: Optional[str] = None):
        logger.warning("Stub CircuitBreaker initialized - NOT FOR PRODUCTION USE")
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.failure_cache_key = failure_cache_key or f"circuit_breaker:{name}:failures"
        
    def __call__(self, func):
        """Decorator to wrap functions with circuit breaker logic"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # In stub implementation, always let calls go through
            return await func(*args, **kwargs)
        return wrapper
    
    def open_circuit(self):
        """Open the circuit to block calls"""
        self.state = CircuitState.OPEN
        logger.warning(f"[STUB] Circuit '{self.name}' opened")
    
    def close_circuit(self):
        """Close the circuit to allow calls"""
        self.state = CircuitState.CLOSED
        logger.warning(f"[STUB] Circuit '{self.name}' closed")
    
    def half_open_circuit(self):
        """Set circuit to half-open to test recovery"""
        self.state = CircuitState.HALF_OPEN
        logger.warning(f"[STUB] Circuit '{self.name}' half-opened")


# For backwards compatibility with any code using the original names
CircuitBreaker = CircuitBreakerStub
