"""
Mock LLM Client

This module provides a mock client for testing LLM functionality without
making actual API calls.
"""

import json
import logging
import re
from typing import Any, Dict, List, Optional

from ..base import BaseLLMClient

# Set up logging
logger = logging.getLogger(__name__)


class MockClient(BaseLLMClient):
    """
    Mock client for testing LLM functionality.
    
    This class provides methods for generating text and chat completions
    without making actual API calls.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None,
        responses: Optional[Dict[str, str]] = None
    ):
        """
        Initialize the mock client.
        
        Args:
            api_key: The API key (not used)
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
            responses: Dictionary of predefined responses
        """
        super().__init__(
            api_key=api_key,
            max_retries=max_retries,
            retry_delay=retry_delay,
            retry_backoff=retry_backoff,
            timeout=timeout,
            logger=logger
        )
        
        # Dictionary of predefined responses
        self.responses = responses or {}
    
    async def generate(
        self,
        prompt: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the mock client.
        
        Args:
            prompt: The prompt to send
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters
            
        Returns:
            A dictionary containing the generated text and metadata
        """
        # Check for predefined responses
        for pattern, response in self.responses.items():
            if re.search(pattern, prompt, re.IGNORECASE):
                return {
                    "text": response,
                    "model": model,
                    "usage": {
                        "prompt_tokens": len(prompt.split()),
                        "completion_tokens": len(response.split()),
                        "total_tokens": len(prompt.split()) + len(response.split())
                    },
                    "finish_reason": "stop"
                }
        
        # Generate a default response
        response = f"This is a mock response to: {prompt[:50]}..."
        
        return {
            "text": response,
            "model": model,
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(response.split()),
                "total_tokens": len(prompt.split()) + len(response.split())
            },
            "finish_reason": "stop"
        }
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the mock client.
        
        Args:
            messages: The messages to send
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters
            
        Returns:
            A dictionary containing the generated response and metadata
        """
        # Extract the last user message
        last_user_message = ""
        for message in reversed(messages):
            if message["role"] == "user":
                last_user_message = message["content"]
                break
        
        # Check for predefined responses
        for pattern, response in self.responses.items():
            if re.search(pattern, last_user_message, re.IGNORECASE):
                return {
                    "text": response,
                    "model": model,
                    "usage": {
                        "prompt_tokens": sum(len(m["content"].split()) for m in messages),
                        "completion_tokens": len(response.split()),
                        "total_tokens": sum(len(m["content"].split()) for m in messages) + len(response.split())
                    },
                    "finish_reason": "stop"
                }
        
        # Generate a default response
        response = f"This is a mock response to: {last_user_message[:50]}..."
        
        # Check if JSON response is requested
        if "json" in kwargs.get("response_format", {}).get("type", ""):
            response = json.dumps({"response": response})
        
        return {
            "text": response,
            "model": model,
            "usage": {
                "prompt_tokens": sum(len(m["content"].split()) for m in messages),
                "completion_tokens": len(response.split()),
                "total_tokens": sum(len(m["content"].split()) for m in messages) + len(response.split())
            },
            "finish_reason": "stop"
        }


# Singleton instance
_mock_client = None


def get_mock_client(
    responses: Optional[Dict[str, str]] = None,
    force_new: bool = False
) -> MockClient:
    """
    Get a singleton instance of the mock client.
    
    Args:
        responses: Dictionary of predefined responses
        force_new: Whether to force creation of a new client
        
    Returns:
        A mock client
    """
    global _mock_client
    
    if _mock_client is None or force_new:
        _mock_client = MockClient(
            responses=responses
        )
    
    return _mock_client
