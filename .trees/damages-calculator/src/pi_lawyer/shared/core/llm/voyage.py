"""
Voyage AI Client

This module provides a client for the Voyage AI API with retry logic, logging,
and error handling. It's primarily used for embeddings and reranking.
"""

import logging
import os
from typing import Any, Dict, List, Optional

import aiohttp

from .base import (
    AuthenticationError,
    ClientError,
    RateLimitError,
    ServerError,
)

# Set up logging
logger = logging.getLogger(__name__)

# Voyage API endpoints
VOYAGE_API_URL = "https://api.voyageai.com/v1"
EMBEDDINGS_ENDPOINT = f"{VOYAGE_API_URL}/embeddings"
RERANK_ENDPOINT = f"{VOYAGE_API_URL}/rerank"


class VoyageClient:
    """
    Client for the Voyage AI API.
    
    This class provides methods for generating embeddings and reranking
    using the Voyage AI API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the Voyage AI client.
        
        Args:
            api_key: The Voyage AI API key
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        self.api_key = api_key or os.environ.get("VOYAGE_API_KEY")
        if not self.api_key:
            raise ValueError("Voyage API key is required")
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_backoff = retry_backoff
        self.timeout = timeout
        self.logger = logger or logging.getLogger(__name__)
    
    async def get_embeddings(
        self,
        texts: List[str],
        model: str = "voyage-3-large",
        input_type: str = "document",
        truncation: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: The texts to embed
            model: The model to use
            input_type: The type of input (document, query, or passage)
            truncation: Whether to truncate texts that exceed the model's token limit
            **kwargs: Additional Voyage-specific parameters
            
        Returns:
            A dictionary containing the embeddings and metadata
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": model,
            "input": texts,
            "input_type": input_type,
            "truncation": truncation,
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            for retry in range(self.max_retries + 1):
                try:
                    async with session.post(
                        EMBEDDINGS_ENDPOINT,
                        headers=headers,
                        json=payload,
                        timeout=self.timeout
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result
                        
                        error_text = await response.text()
                        
                        if response.status == 429:
                            if retry < self.max_retries:
                                delay = self.retry_delay * (self.retry_backoff ** retry)
                                self.logger.warning(
                                    f"Rate limit hit, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                                    extra={"error": error_text, "retry": retry+1, "max_retries": self.max_retries}
                                )
                                await self._sleep(delay)
                                continue
                            raise RateLimitError(f"Voyage rate limit exceeded: {error_text}")
                        
                        if response.status == 401:
                            raise AuthenticationError(f"Voyage authentication error: {error_text}")
                        
                        if response.status >= 500:
                            if retry < self.max_retries:
                                delay = self.retry_delay * (self.retry_backoff ** retry)
                                self.logger.warning(
                                    f"Server error, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                                    extra={"error": error_text, "retry": retry+1, "max_retries": self.max_retries}
                                )
                                await self._sleep(delay)
                                continue
                            raise ServerError(f"Voyage server error: {error_text}")
                        
                        raise ClientError(f"Voyage client error: {error_text}")
                
                except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                    if retry < self.max_retries:
                        delay = self.retry_delay * (self.retry_backoff ** retry)
                        self.logger.warning(
                            f"Connection error, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                            extra={"error": str(e), "retry": retry+1, "max_retries": self.max_retries}
                        )
                        await self._sleep(delay)
                        continue
                    raise ClientError(f"Voyage connection error: {str(e)}")
    
    async def rerank(
        self,
        query: str,
        documents: List[str],
        model: str = "voyage-reranker-v2",
        top_k: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Rerank documents based on relevance to the query.
        
        Args:
            query: The query to use for reranking
            documents: The documents to rerank
            model: The model to use
            top_k: The number of top results to return
            **kwargs: Additional Voyage-specific parameters
            
        Returns:
            A dictionary containing the reranked documents and metadata
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": model,
            "query": query,
            "documents": documents,
            **kwargs
        }
        
        if top_k is not None:
            payload["top_k"] = top_k
        
        async with aiohttp.ClientSession() as session:
            for retry in range(self.max_retries + 1):
                try:
                    async with session.post(
                        RERANK_ENDPOINT,
                        headers=headers,
                        json=payload,
                        timeout=self.timeout
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result
                        
                        error_text = await response.text()
                        
                        if response.status == 429:
                            if retry < self.max_retries:
                                delay = self.retry_delay * (self.retry_backoff ** retry)
                                self.logger.warning(
                                    f"Rate limit hit, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                                    extra={"error": error_text, "retry": retry+1, "max_retries": self.max_retries}
                                )
                                await self._sleep(delay)
                                continue
                            raise RateLimitError(f"Voyage rate limit exceeded: {error_text}")
                        
                        if response.status == 401:
                            raise AuthenticationError(f"Voyage authentication error: {error_text}")
                        
                        if response.status >= 500:
                            if retry < self.max_retries:
                                delay = self.retry_delay * (self.retry_backoff ** retry)
                                self.logger.warning(
                                    f"Server error, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                                    extra={"error": error_text, "retry": retry+1, "max_retries": self.max_retries}
                                )
                                await self._sleep(delay)
                                continue
                            raise ServerError(f"Voyage server error: {error_text}")
                        
                        raise ClientError(f"Voyage client error: {error_text}")
                
                except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                    if retry < self.max_retries:
                        delay = self.retry_delay * (self.retry_backoff ** retry)
                        self.logger.warning(
                            f"Connection error, retrying in {delay:.2f}s (retry {retry+1}/{self.max_retries})",
                            extra={"error": str(e), "retry": retry+1, "max_retries": self.max_retries}
                        )
                        await self._sleep(delay)
                        continue
                    raise ClientError(f"Voyage connection error: {str(e)}")
    
    async def _sleep(self, seconds: float) -> None:
        """
        Sleep for the specified number of seconds.
        
        Args:
            seconds: The number of seconds to sleep
        """
        import asyncio
        await asyncio.sleep(seconds)


# Singleton instance for the Voyage client
_voyage_client: Optional[VoyageClient] = None


def get_voyage_client() -> VoyageClient:
    """
    Get the singleton instance of the Voyage client.
    
    Returns:
        The Voyage client instance
    """
    global _voyage_client
    
    if _voyage_client is None:
        api_key = os.environ.get("VOYAGE_API_KEY")
        
        _voyage_client = VoyageClient(
            api_key=api_key
        )
    
    return _voyage_client
