"""
LLM Module

This module provides a comprehensive abstraction layer for various LLM providers
with support for multiple models, fallback mechanisms, and admin capabilities.
"""

# Base classes and errors
# Admin capabilities
from .admin import (
    APIKeyManager,
    LLMRegistry,
    ModelConfig,
    PromptManager,
    PromptTemplate,
    get_available_models,
    get_llm_registry,
)

# Unified API
from .api import generate_chat, generate_text
from .base import (
    AuthenticationError,
    BaseLLMClient,
    ClientError,
    LLMError,
    RateLimitError,
    ServerError,
)

# Configuration models
from .config import (
    EmbeddingConfig,
    LLMConfig,
    LLMMessage,
    LLMProvider,
    LLMResponse,
    LLMUsage,
)

# Embedding API
from .embeddings import BaseEmbeddingClient, get_embedding_client, get_embeddings
from .factory import get_llm_client

# Provider implementations
from .providers import (
    AnthropicClient,
    GeminiClient,
    GroqClient,
    MockClient,
    OpenAIClient,
)
from .providers.anthropic import get_anthropic_client
from .providers.gemini import get_gemini_client
from .providers.groq import get_groq_client
from .providers.mock import get_mock_client
from .providers.openai import get_openai_client

# Define __all__ first
__all__ = [
    # Base classes and errors
    "BaseLLMClient",
    "LLMError",
    "RateLimitError",
    "AuthenticationError",
    "ServerError",
    "ClientError",

    # Configuration models
    "LLMProvider",
    "LLMConfig",
    "LLMMessage",
    "LLMResponse",
    "LLMUsage",
    "EmbeddingConfig",

    # Unified API
    "generate_text",
    "generate_chat",
    "get_llm_client",

    # Provider implementations
    "OpenAIClient",
    "AnthropicClient",
    "GeminiClient",
    "GroqClient",
    "MockClient",
    "get_openai_client",
    "get_anthropic_client",
    "get_gemini_client",
    "get_groq_client",
    "get_mock_client",

    # Embedding API
    "BaseEmbeddingClient",
    "get_embedding_client",
    "get_embeddings",

    # Admin capabilities
    "ModelConfig",
    "get_available_models",
    "PromptTemplate",
    "PromptManager",
    "APIKeyManager",
    "LLMRegistry",
    "get_llm_registry"
]

# Legacy imports
try:
    from .voyage import VoyageClient, get_voyage_client
    # Add to __all__ if import succeeds
    __all__.extend(["VoyageClient", "get_voyage_client"])
except ImportError:
    pass
