# LLM Abstraction Layer

This module provides a comprehensive abstraction layer for various LLM providers with support for multiple models, fallback mechanisms, and admin capabilities.

## Features

- **Multiple Provider Support**: OpenAI, Anthropic, Gemini, Groq, and more
- **Unified API**: Common interface for all LLM operations
- **Fallback Mechanisms**: Automatic fallback to alternative providers
- **Comprehensive Error Handling**: Consistent error handling across providers
- **Retry Logic**: Automatic retries for transient errors
- **Embedding Support**: Unified API for embeddings
- **Admin Capabilities**: Model selection, prompt management, and API key management

## Architecture

The LLM Abstraction Layer is organized into the following components:

- **Base Classes**: Common interfaces and error types
- **Configuration Models**: Pydantic models for configuration
- **Provider Implementations**: Client implementations for each provider
- **Unified API**: High-level API for LLM operations
- **Embedding API**: API for generating embeddings
- **Admin Capabilities**: Interfaces for model selection, prompt management, and API key management

## Usage

### Basic Usage

```python
import asyncio
from src.pi_lawyer.shared.core.llm import (
    LLMConfig,
    LL<PERSON>rovider,
    generate_text,
    generate_chat,
    LLMMessage
)

async def example():
    # Generate text
    response = await generate_text(
        prompt="Explain the concept of LLM abstraction layers.",
        config=LLMConfig(
            provider=LLMProvider.OPENAI,
            model="gpt-4o",
            temperature=0.7
        )
    )

    print(response.content)

    # Generate chat response
    messages = [
        LLMMessage(role="system", content="You are a helpful assistant."),
        LLMMessage(role="user", content="What are the benefits of using an LLM abstraction layer?")
    ]

    response = await generate_chat(
        messages=messages,
        config=LLMConfig(
            provider=LLMProvider.ANTHROPIC,
            model="claude-3-haiku-20240307",
            temperature=0.7
        )
    )

    print(response.content)

asyncio.run(example())
```

### Fallback Mechanisms

```python
import asyncio
from src.pi_lawyer.shared.core.llm import (
    LLMConfig,
    LLMProvider,
    generate_text
)

async def example_fallback():
    # Configure primary and fallback providers
    primary_config = LLMConfig(
        provider=LLMProvider.OPENAI,
        model="gpt-4o"
    )

    fallback_config = LLMConfig(
        provider=LLMProvider.ANTHROPIC,
        model="claude-3-haiku-20240307"
    )

    # Generate text with fallback
    response = await generate_text(
        prompt="Explain the concept of fallback mechanisms in LLM systems.",
        config=primary_config,
        fallback_configs=[fallback_config]
    )

    print(f"Response from {response.model}: {response.content}")

asyncio.run(example_fallback())
```

### Embeddings

```python
import asyncio
from src.pi_lawyer.shared.core.llm import (
    EmbeddingConfig,
    LLMProvider,
    get_embeddings
)

async def example_embeddings():
    # Generate embeddings
    texts = [
        "LLM abstraction layers provide a unified interface for multiple providers.",
        "Embeddings are vector representations of text used for semantic search."
    ]

    embeddings = await get_embeddings(
        texts=texts,
        config=EmbeddingConfig(
            provider=LLMProvider.OPENAI,
            model="text-embedding-3-small"
        )
    )

    print(f"Number of embeddings: {len(embeddings)}")
    print(f"Embedding dimensions: {len(embeddings[0])}")

asyncio.run(example_embeddings())
```

### Prompt Management

```python
from src.pi_lawyer.shared.core.llm import (
    PromptTemplate,
    PromptManager
)

# Create a prompt manager
prompt_manager = PromptManager()

# Create a prompt template
template = PromptTemplate(
    name="research_query",
    description="Template for generating research queries",
    template="Generate {num_queries} research queries about {topic} that would be relevant for {audience}."
)

# Create the template
prompt_manager.create_template(template)

# Render the template
rendered = template.render({
    "num_queries": 3,
    "topic": "artificial intelligence",
    "audience": "legal professionals"
})

print(rendered)
```

## Provider Support

The LLM Abstraction Layer supports the following providers:

- **OpenAI**: GPT-4o, GPT-4 Turbo, GPT-3.5 Turbo
- **Anthropic**: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- **Gemini**: Gemini 1.5 Pro, Gemini 1.5 Flash
- **Groq**: Llama 3 70B, Llama 3 8B
- **Voyage**: Voyage 3 Large (embeddings)

## Error Handling

The LLM Abstraction Layer provides a comprehensive error handling system:

- **LLMError**: Base exception for all LLM-related errors
- **RateLimitError**: Exception raised when rate limits are hit
- **AuthenticationError**: Exception raised when authentication fails
- **ServerError**: Exception raised when the LLM server returns an error
- **ClientError**: Exception raised when there's a client-side error

## Configuration

The LLM Abstraction Layer can be configured using environment variables:

- **OPENAI_API_KEY**: API key for OpenAI
- **ANTHROPIC_API_KEY**: API key for Anthropic
- **GEMINI_API_KEY**: API key for Gemini
- **GROQ_API_KEY**: API key for Groq
- **VOYAGE_API_KEY**: API key for Voyage

## Testing

The LLM Abstraction Layer includes a mock client for testing:

```python
from src.pi_lawyer.shared.core.llm import (
    LLMConfig,
    LLMProvider,
    generate_text
)

# Use the mock client for testing
response = await generate_text(
    prompt="Test prompt",
    config=LLMConfig(
        provider=LLMProvider.MOCK,
        model="mock-model"
    )
)

print(response.content)
```

## Contributing

When adding a new provider to the LLM Abstraction Layer:

1. Create a new client class in `providers/` that implements `BaseLLMClient`
2. Add the provider to the `LLMProvider` enum in `config.py`
3. Update the provider map in `factory.py`
4. Add model configurations to `admin/models.py`
5. Add tests for the new provider

## Documentation

For more detailed documentation, see the [LLM Abstraction Layer Documentation](../../../../../docs/llm-abstraction-layer/index.md).

## License

This module is part of the AiLex project and is subject to the same license terms.
