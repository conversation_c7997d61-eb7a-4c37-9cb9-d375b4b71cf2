"""
Model Selection Interface

This module provides interfaces for model selection and configuration.
"""

import logging
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field

from ..config import LL<PERSON>rovider

# Set up logging
logger = logging.getLogger(__name__)


class ModelCapabilities(BaseModel):
    """
    Capabilities of an LLM model.
    
    Attributes:
        context_window: Maximum context window size in tokens
        max_output_tokens: Maximum output tokens
        supports_vision: Whether the model supports vision inputs
        supports_function_calling: Whether the model supports function calling
        supports_json_mode: Whether the model supports JSON mode
        supports_streaming: Whether the model supports streaming
    """
    context_window: int = 4096
    max_output_tokens: int = 4096
    supports_vision: bool = False
    supports_function_calling: bool = False
    supports_json_mode: bool = False
    supports_streaming: bool = True


class ModelConfig(BaseModel):
    """
    Configuration for an LLM model.
    
    Attributes:
        provider: The LLM provider
        model_id: The model ID
        display_name: Display name for the model
        description: Description of the model
        capabilities: Capabilities of the model
        default_parameters: Default parameters for the model
        cost_per_1k_tokens_input: Cost per 1000 input tokens
        cost_per_1k_tokens_output: Cost per 1000 output tokens
    """
    provider: Union[LLMProvider, str]
    model_id: str
    display_name: str
    description: str = ""
    capabilities: ModelCapabilities = Field(default_factory=ModelCapabilities)
    default_parameters: Dict[str, Union[str, int, float, bool]] = Field(default_factory=dict)
    cost_per_1k_tokens_input: float = 0.0
    cost_per_1k_tokens_output: float = 0.0


# Default models by provider
DEFAULT_MODELS: Dict[LLMProvider, List[ModelConfig]] = {
    LLMProvider.OPENAI: [
        ModelConfig(
            provider=LLMProvider.OPENAI,
            model_id="gpt-4o",
            display_name="GPT-4o",
            description="OpenAI's most advanced model, optimized for chat",
            capabilities=ModelCapabilities(
                context_window=128000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=True,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            },
            cost_per_1k_tokens_input=0.01,
            cost_per_1k_tokens_output=0.03
        ),
        ModelConfig(
            provider=LLMProvider.OPENAI,
            model_id="gpt-4-turbo",
            display_name="GPT-4 Turbo",
            description="Optimized version of GPT-4 with improved performance",
            capabilities=ModelCapabilities(
                context_window=128000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=True,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            },
            cost_per_1k_tokens_input=0.01,
            cost_per_1k_tokens_output=0.03
        ),
        ModelConfig(
            provider=LLMProvider.OPENAI,
            model_id="gpt-3.5-turbo",
            display_name="GPT-3.5 Turbo",
            description="Fast and cost-effective model for most tasks",
            capabilities=ModelCapabilities(
                context_window=16385,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=True,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            },
            cost_per_1k_tokens_input=0.0005,
            cost_per_1k_tokens_output=0.0015
        )
    ],
    LLMProvider.ANTHROPIC: [
        ModelConfig(
            provider=LLMProvider.ANTHROPIC,
            model_id="claude-3-opus-20240229",
            display_name="Claude 3 Opus",
            description="Anthropic's most powerful model for complex tasks",
            capabilities=ModelCapabilities(
                context_window=200000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=False,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.015,
            cost_per_1k_tokens_output=0.075
        ),
        ModelConfig(
            provider=LLMProvider.ANTHROPIC,
            model_id="claude-3-sonnet-20240229",
            display_name="Claude 3 Sonnet",
            description="Balanced model for most tasks",
            capabilities=ModelCapabilities(
                context_window=200000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=False,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.003,
            cost_per_1k_tokens_output=0.015
        ),
        ModelConfig(
            provider=LLMProvider.ANTHROPIC,
            model_id="claude-3-haiku-20240307",
            display_name="Claude 3 Haiku",
            description="Fast and cost-effective model",
            capabilities=ModelCapabilities(
                context_window=200000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_function_calling=False,
                supports_json_mode=True,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.00025,
            cost_per_1k_tokens_output=0.00125
        )
    ],
    LLMProvider.GEMINI: [
        ModelConfig(
            provider=LLMProvider.GEMINI,
            model_id="gemini-1.5-pro",
            display_name="Gemini 1.5 Pro",
            description="Google's most capable model for complex tasks",
            capabilities=ModelCapabilities(
                context_window=1000000,
                max_output_tokens=8192,
                supports_vision=True,
                supports_function_calling=True,
                supports_json_mode=False,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.00125,
            cost_per_1k_tokens_output=0.00375
        ),
        ModelConfig(
            provider=LLMProvider.GEMINI,
            model_id="gemini-1.5-flash",
            display_name="Gemini 1.5 Flash",
            description="Fast and cost-effective model",
            capabilities=ModelCapabilities(
                context_window=1000000,
                max_output_tokens=8192,
                supports_vision=True,
                supports_function_calling=True,
                supports_json_mode=False,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.0005,
            cost_per_1k_tokens_output=0.0015
        )
    ],
    LLMProvider.GROQ: [
        ModelConfig(
            provider=LLMProvider.GROQ,
            model_id="llama3-70b-8192",
            display_name="Llama 3 70B",
            description="High-performance model with low latency",
            capabilities=ModelCapabilities(
                context_window=8192,
                max_output_tokens=4096,
                supports_vision=False,
                supports_function_calling=False,
                supports_json_mode=False,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.0009,
            cost_per_1k_tokens_output=0.0009
        ),
        ModelConfig(
            provider=LLMProvider.GROQ,
            model_id="llama3-8b-8192",
            display_name="Llama 3 8B",
            description="Fast and cost-effective model",
            capabilities=ModelCapabilities(
                context_window=8192,
                max_output_tokens=4096,
                supports_vision=False,
                supports_function_calling=False,
                supports_json_mode=False,
                supports_streaming=True
            ),
            default_parameters={
                "temperature": 0.7,
                "top_p": 1.0
            },
            cost_per_1k_tokens_input=0.0001,
            cost_per_1k_tokens_output=0.0001
        )
    ]
}


def get_available_models(provider: Optional[Union[LLMProvider, str]] = None) -> List[ModelConfig]:
    """
    Get available models for the specified provider.
    
    Args:
        provider: The LLM provider to get models for
        
    Returns:
        A list of available models
    """
    if provider is None:
        # Return all models
        return [model for models in DEFAULT_MODELS.values() for model in models]
    
    # Convert string provider to enum
    if isinstance(provider, str):
        try:
            provider = LLMProvider(provider)
        except ValueError:
            return []
    
    # Return models for the specified provider
    return DEFAULT_MODELS.get(provider, [])
