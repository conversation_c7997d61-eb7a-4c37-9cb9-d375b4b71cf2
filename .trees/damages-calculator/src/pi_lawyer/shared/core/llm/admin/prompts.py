"""
Prompt Management Interface

This module provides interfaces for prompt template management.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator

# Set up logging
logger = logging.getLogger(__name__)


class PromptTemplate(BaseModel):
    """
    A prompt template with variable substitution.
    
    Attributes:
        id: Unique identifier for the template
        name: Name of the template
        description: Description of the template
        template: The template string with variables in {variable} format
        variables: List of variables used in the template
        version: Version of the template
        created_at: Creation timestamp
        updated_at: Last update timestamp
        created_by: User who created the template
        updated_by: User who last updated the template
        is_active: Whether the template is active
        tags: Tags for the template
    """
    id: Optional[str] = None
    name: str
    description: str = ""
    template: str
    variables: List[str] = Field(default_factory=list)
    version: int = 1
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    is_active: bool = True
    tags: List[str] = Field(default_factory=list)
    
    @field_validator('variables', mode='after')
    def extract_variables(cls, v: List[str], values: Dict[str, Any]) -> List[str]:
        """Extract variables from the template if not provided."""
        if not v and 'template' in values:
            # Extract variables from the template using regex
            template = values['template']
            variables = re.findall(r'\{([a-zA-Z0-9_]+)\}', template)
            return list(set(variables))
        return v
    
    def render(self, variables: Dict[str, Any]) -> str:
        """
        Render the template with the given variables.
        
        Args:
            variables: Dictionary of variables to substitute
            
        Returns:
            The rendered template
            
        Raises:
            KeyError: If a required variable is missing
        """
        template = self.template
        
        # Check for missing variables
        missing_vars = [var for var in self.variables if var not in variables]
        if missing_vars:
            raise KeyError(f"Missing required variables: {', '.join(missing_vars)}")
        
        # Substitute variables
        for var, value in variables.items():
            template = template.replace(f"{{{var}}}", str(value))
        
        return template


class PromptManager:
    """
    Manager for prompt templates.
    
    This class provides methods for creating, updating, and retrieving
    prompt templates, with support for versioning and history.
    """
    
    _instance = None
    _templates: Dict[str, List[PromptTemplate]] = {}
    
    def __new__(cls):
        """Create a singleton instance of the manager."""
        if cls._instance is None:
            cls._instance = super(PromptManager, cls).__new__(cls)
            cls._instance._templates = {}
        return cls._instance
    
    def create_template(self, template: PromptTemplate) -> PromptTemplate:
        """
        Create a new prompt template.
        
        Args:
            template: The template to create
            
        Returns:
            The created template
            
        Raises:
            ValueError: If a template with the same name already exists
        """
        if template.name in self._templates:
            raise ValueError(f"Template with name '{template.name}' already exists")
        
        # Generate ID if not provided
        if template.id is None:
            template.id = f"template_{len(self._templates) + 1}"
        
        # Set timestamps
        template.created_at = datetime.now()
        template.updated_at = datetime.now()
        
        # Store the template
        self._templates[template.name] = [template]
        
        return template
    
    def update_template(
        self,
        name: str,
        template: str,
        description: Optional[str] = None,
        updated_by: Optional[str] = None,
        is_active: Optional[bool] = None,
        tags: Optional[List[str]] = None
    ) -> PromptTemplate:
        """
        Update an existing prompt template.
        
        Args:
            name: Name of the template to update
            template: New template string
            description: New description
            updated_by: User who updated the template
            is_active: Whether the template is active
            tags: Tags for the template
            
        Returns:
            The updated template
            
        Raises:
            ValueError: If the template does not exist
        """
        if name not in self._templates:
            raise ValueError(f"Template with name '{name}' does not exist")
        
        # Get the latest version
        latest = self._templates[name][-1]
        
        # Create a new version
        new_version = PromptTemplate(
            id=latest.id,
            name=latest.name,
            description=description or latest.description,
            template=template,
            version=latest.version + 1,
            created_at=latest.created_at,
            updated_at=datetime.now(),
            created_by=latest.created_by,
            updated_by=updated_by,
            is_active=is_active if is_active is not None else latest.is_active,
            tags=tags or latest.tags
        )
        
        # Store the new version
        self._templates[name].append(new_version)
        
        return new_version
    
    def get_template(
        self,
        name: str,
        version: Optional[int] = None
    ) -> PromptTemplate:
        """
        Get a prompt template.
        
        Args:
            name: Name of the template to get
            version: Version of the template to get (None for latest)
            
        Returns:
            The template
            
        Raises:
            ValueError: If the template does not exist
        """
        if name not in self._templates:
            raise ValueError(f"Template with name '{name}' does not exist")
        
        templates = self._templates[name]
        
        if version is None:
            # Get the latest version
            return templates[-1]
        
        # Get the specified version
        for template in templates:
            if template.version == version:
                return template
        
        raise ValueError(f"Template '{name}' version {version} does not exist")
    
    def get_all_templates(self, include_inactive: bool = False) -> List[PromptTemplate]:
        """
        Get all prompt templates.
        
        Args:
            include_inactive: Whether to include inactive templates
            
        Returns:
            A list of templates
        """
        templates = []
        
        for template_list in self._templates.values():
            latest = template_list[-1]
            if include_inactive or latest.is_active:
                templates.append(latest)
        
        return templates
    
    def get_template_history(self, name: str) -> List[PromptTemplate]:
        """
        Get the history of a prompt template.
        
        Args:
            name: Name of the template to get history for
            
        Returns:
            A list of template versions
            
        Raises:
            ValueError: If the template does not exist
        """
        if name not in self._templates:
            raise ValueError(f"Template with name '{name}' does not exist")
        
        return self._templates[name]
    
    def delete_template(self, name: str) -> None:
        """
        Delete a prompt template.
        
        Args:
            name: Name of the template to delete
            
        Raises:
            ValueError: If the template does not exist
        """
        if name not in self._templates:
            raise ValueError(f"Template with name '{name}' does not exist")
        
        del self._templates[name]
