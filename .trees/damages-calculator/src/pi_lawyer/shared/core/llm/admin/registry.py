"""
LLM Registry Module

This module provides a registry for LLM models and configurations,
allowing runtime modification of default models and provider settings.
"""

import json
import logging
import os
import threading
from typing import Dict, List, Optional

from ..config import LLMProvider
from .models import ModelConfig, get_available_models

# Set up logging
logger = logging.getLogger(__name__)


class LLMRegistry:
    """
    Registry for LLM models and configurations.
    
    This class provides a singleton registry for LLM models and configurations,
    allowing runtime modification of default models and provider settings.
    
    The registry persists changes to a JSON file for durability across restarts.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Ensure singleton instance."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(LLMRegistry, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        """Initialize the registry."""
        # Skip initialization if already initialized
        if self._initialized:
            return
            
        # Initialize registry data
        self._default_models: Dict[str, str] = {}
        self._available_models: Dict[str, List[ModelConfig]] = {}
        self._config_path = os.environ.get(
            "LLM_REGISTRY_CONFIG_PATH", 
            os.path.join(os.path.dirname(__file__), "llm_registry.json")
        )
        
        # Load registry data
        self._load_registry()
        
        # Mark as initialized
        self._initialized = True
    
    def _load_registry(self) -> None:
        """
        Load registry data from file.
        
        If the file doesn't exist, initialize with default values.
        """
        try:
            if os.path.exists(self._config_path):
                with open(self._config_path, "r") as f:
                    data = json.load(f)
                    self._default_models = data.get("default_models", {})
                    logger.info(f"Loaded LLM registry from {self._config_path}")
            else:
                # Initialize with default values
                for provider in LLMProvider:
                    provider_str = provider.value
                    models = get_available_models(provider)
                    if models:
                        # Set the first model as default
                        self._default_models[provider_str] = models[0].model_id
                
                # Save the initial registry
                self._save_registry()
                logger.info(f"Initialized LLM registry at {self._config_path}")
        except Exception as e:
            logger.error(f"Error loading LLM registry: {str(e)}")
            # Initialize with empty defaults
            self._default_models = {}
    
    def _save_registry(self) -> None:
        """Save registry data to file."""
        try:
            data = {
                "default_models": self._default_models
            }
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self._config_path), exist_ok=True)
            
            with open(self._config_path, "w") as f:
                json.dump(data, f, indent=2)
                
            logger.info(f"Saved LLM registry to {self._config_path}")
        except Exception as e:
            logger.error(f"Error saving LLM registry: {str(e)}")
    
    def get_default_model(self, provider: str) -> Optional[str]:
        """
        Get the default model for a provider.
        
        Args:
            provider: The provider name or enum value
            
        Returns:
            The default model ID, or None if not found
        """
        # Convert provider enum to string if needed
        if isinstance(provider, LLMProvider):
            provider = provider.value
            
        return self._default_models.get(provider)
    
    def set_default_model(self, provider: str, model_id: str) -> bool:
        """
        Set the default model for a provider.
        
        Args:
            provider: The provider name or enum value
            model_id: The model ID to set as default
            
        Returns:
            True if successful, False otherwise
        """
        # Convert provider enum to string if needed
        if isinstance(provider, LLMProvider):
            provider = provider.value
            
        # Verify the model exists
        models = get_available_models(provider)
        model_ids = [model.model_id for model in models]
        
        if model_id not in model_ids:
            logger.warning(f"Model {model_id} not found for provider {provider}")
            return False
            
        # Set the default model
        self._default_models[provider] = model_id
        
        # Save the registry
        self._save_registry()
        
        logger.info(f"Set default model for {provider} to {model_id}")
        return True
    
    def get_all_defaults(self) -> Dict[str, str]:
        """
        Get all default models.
        
        Returns:
            A dictionary mapping provider names to default model IDs
        """
        return self._default_models.copy()


# Singleton getter
def get_llm_registry() -> LLMRegistry:
    """
    Get the LLM registry singleton.
    
    Returns:
        The LLM registry singleton
    """
    return LLMRegistry()
