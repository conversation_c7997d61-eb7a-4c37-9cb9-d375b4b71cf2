"""
API Key Management Interface

This module provides interfaces for API key management.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field

from ..config import LLMProvider

# Set up logging
logger = logging.getLogger(__name__)


class APIKeyInfo(BaseModel):
    """
    Information about an API key.
    
    Attributes:
        provider: The LLM provider
        key_id: Unique identifier for the key
        name: Name of the key
        key_prefix: First few characters of the key for identification
        is_active: Whether the key is active
        created_at: Creation timestamp
        expires_at: Expiration timestamp
        created_by: User who created the key
        usage_limit: Monthly usage limit in dollars
        current_usage: Current usage in dollars
        last_used: Last usage timestamp
        last_rotated: Last rotation timestamp
        rotation_frequency_days: Frequency of key rotation in days
    """
    provider: Union[LLMProvider, str]
    key_id: str
    name: str
    key_prefix: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    created_by: Optional[str] = None
    usage_limit: Optional[float] = None
    current_usage: float = 0.0
    last_used: Optional[datetime] = None
    last_rotated: Optional[datetime] = None
    rotation_frequency_days: int = 90


class APIKeyManager:
    """
    Manager for API keys.
    
    This class provides methods for creating, updating, and retrieving
    API keys, with support for rotation and usage tracking.
    """
    
    _instance = None
    _keys: Dict[str, APIKeyInfo] = {}
    _key_values: Dict[str, str] = {}
    
    def __new__(cls):
        """Create a singleton instance of the manager."""
        if cls._instance is None:
            cls._instance = super(APIKeyManager, cls).__new__(cls)
            cls._instance._keys = {}
            cls._instance._key_values = {}
        return cls._instance
    
    def create_key(
        self,
        provider: Union[LLMProvider, str],
        name: str,
        key_value: str,
        created_by: Optional[str] = None,
        usage_limit: Optional[float] = None,
        expires_in_days: Optional[int] = None,
        rotation_frequency_days: int = 90
    ) -> APIKeyInfo:
        """
        Create a new API key.
        
        Args:
            provider: The LLM provider
            name: Name of the key
            key_value: The API key value
            created_by: User who created the key
            usage_limit: Monthly usage limit in dollars
            expires_in_days: Number of days until the key expires
            rotation_frequency_days: Frequency of key rotation in days
            
        Returns:
            Information about the created key
            
        Raises:
            ValueError: If a key with the same name already exists
        """
        if name in self._keys:
            raise ValueError(f"Key with name '{name}' already exists")
        
        # Generate key ID
        key_id = f"key_{len(self._keys) + 1}"
        
        # Get key prefix (first 4 characters)
        key_prefix = key_value[:4] + "..." if len(key_value) > 4 else key_value
        
        # Calculate expiration date
        expires_at = None
        if expires_in_days is not None:
            expires_at = datetime.now() + timedelta(days=expires_in_days)
        
        # Create key info
        key_info = APIKeyInfo(
            provider=provider,
            key_id=key_id,
            name=name,
            key_prefix=key_prefix,
            is_active=True,
            created_at=datetime.now(),
            expires_at=expires_at,
            created_by=created_by,
            usage_limit=usage_limit,
            current_usage=0.0,
            last_used=None,
            last_rotated=datetime.now(),
            rotation_frequency_days=rotation_frequency_days
        )
        
        # Store the key info and value
        self._keys[name] = key_info
        self._key_values[name] = key_value
        
        return key_info
    
    def get_key(self, name: str) -> str:
        """
        Get an API key value.
        
        Args:
            name: Name of the key to get
            
        Returns:
            The API key value
            
        Raises:
            ValueError: If the key does not exist or is inactive
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        key_info = self._keys[name]
        
        if not key_info.is_active:
            raise ValueError(f"Key with name '{name}' is inactive")
        
        if key_info.expires_at and key_info.expires_at < datetime.now():
            raise ValueError(f"Key with name '{name}' has expired")
        
        # Update last used timestamp
        key_info.last_used = datetime.now()
        
        return self._key_values[name]
    
    def get_key_info(self, name: str) -> APIKeyInfo:
        """
        Get information about an API key.
        
        Args:
            name: Name of the key to get information for
            
        Returns:
            Information about the key
            
        Raises:
            ValueError: If the key does not exist
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        return self._keys[name]
    
    def get_all_keys(self, provider: Optional[Union[LLMProvider, str]] = None) -> List[APIKeyInfo]:
        """
        Get information about all API keys.
        
        Args:
            provider: The LLM provider to filter by
            
        Returns:
            A list of key information
        """
        if provider is None:
            return list(self._keys.values())
        
        # Convert string provider to enum
        if isinstance(provider, str):
            try:
                provider = LLMProvider(provider)
            except ValueError:
                return []
        
        # Filter keys by provider
        return [
            key_info for key_info in self._keys.values()
            if key_info.provider == provider
        ]
    
    def update_key(
        self,
        name: str,
        is_active: Optional[bool] = None,
        usage_limit: Optional[float] = None,
        expires_in_days: Optional[int] = None,
        rotation_frequency_days: Optional[int] = None
    ) -> APIKeyInfo:
        """
        Update an API key.
        
        Args:
            name: Name of the key to update
            is_active: Whether the key is active
            usage_limit: Monthly usage limit in dollars
            expires_in_days: Number of days until the key expires
            rotation_frequency_days: Frequency of key rotation in days
            
        Returns:
            Updated information about the key
            
        Raises:
            ValueError: If the key does not exist
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        key_info = self._keys[name]
        
        # Update key info
        if is_active is not None:
            key_info.is_active = is_active
        
        if usage_limit is not None:
            key_info.usage_limit = usage_limit
        
        if expires_in_days is not None:
            key_info.expires_at = datetime.now() + timedelta(days=expires_in_days)
        
        if rotation_frequency_days is not None:
            key_info.rotation_frequency_days = rotation_frequency_days
        
        return key_info
    
    def rotate_key(
        self,
        name: str,
        new_key_value: str
    ) -> APIKeyInfo:
        """
        Rotate an API key.
        
        Args:
            name: Name of the key to rotate
            new_key_value: The new API key value
            
        Returns:
            Updated information about the key
            
        Raises:
            ValueError: If the key does not exist
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        key_info = self._keys[name]
        
        # Update key prefix
        key_info.key_prefix = new_key_value[:4] + "..." if len(new_key_value) > 4 else new_key_value
        
        # Update last rotated timestamp
        key_info.last_rotated = datetime.now()
        
        # Store the new key value
        self._key_values[name] = new_key_value
        
        return key_info
    
    def delete_key(self, name: str) -> None:
        """
        Delete an API key.
        
        Args:
            name: Name of the key to delete
            
        Raises:
            ValueError: If the key does not exist
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        # Remove the key
        del self._keys[name]
        del self._key_values[name]
    
    def track_usage(
        self,
        name: str,
        amount: float
    ) -> float:
        """
        Track usage for an API key.
        
        Args:
            name: Name of the key to track usage for
            amount: Amount to add to the current usage
            
        Returns:
            The new current usage
            
        Raises:
            ValueError: If the key does not exist
        """
        if name not in self._keys:
            raise ValueError(f"Key with name '{name}' does not exist")
        
        key_info = self._keys[name]
        
        # Update current usage
        key_info.current_usage += amount
        
        # Check if usage limit is exceeded
        if key_info.usage_limit is not None and key_info.current_usage > key_info.usage_limit:
            logger.warning(
                f"Usage limit exceeded for key '{name}': {key_info.current_usage} > {key_info.usage_limit}"
            )
        
        return key_info.current_usage
