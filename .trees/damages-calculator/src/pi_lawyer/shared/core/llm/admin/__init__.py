"""
LLM Admin Capabilities

This package contains admin capabilities for LLM management, including
model selection, prompt management, and API key management.
"""

from .keys import APIKeyManager
from .models import ModelConfig, get_available_models
from .prompts import PromptManager, PromptTemplate
from .registry import LLMRegistry, get_llm_registry

__all__ = [
    "ModelConfig",
    "get_available_models",
    "PromptTemplate",
    "PromptManager",
    "APIKeyManager",
    "LLMRegistry",
    "get_llm_registry"
]
