# AiLex Shared Core Components

This directory contains the shared core components for the AiLex Unified Agent Architecture. These components are designed to be reused across all agent types, ensuring consistency and reducing duplication.

## Components

### State Management (`state.py`)

The state management system provides a bridge between TypedDict (for runtime efficiency) and Pydantic (for validation and serialization). It includes:

- `AiLexState` (TypedDict): Runtime representation of agent state
- `StateModel` (Pydantic): Validation and serialization for agent state
- Utility functions for state creation and manipulation

### LLM Wrappers (`llm/`)

The LLM wrappers provide a consistent interface for interacting with different LLM providers, with built-in retry logic, logging, and error handling. They include:

- `BaseLLMClient`: Abstract base class for LLM clients
- `OpenAIClient`: Client for the OpenAI API
- `VoyageClient`: Client for the Voyage AI API (embeddings and reranking)
- Singleton getters for each client

### Tool Specifications (`tools/`)

The tool specifications provide JSON schema definitions for tools used by agents, with a registry for easy access. They include:

- JSON schema definitions for common tools
- A tool registry for easy access to tool schemas
- A tool executor for executing tools based on their schemas

## Usage

See the [Shared Core Components Documentation](../../../docs/shared-core-components.md) for detailed usage examples and integration points.

## Testing

The shared core components have been thoroughly tested using both pytest and standalone test scripts. All tests are located in the `tests/shared/` directory.

To run the tests:

```bash
# Run the standalone test script
python test_shared_core.py

# Run the pytest tests (requires fixing global fixture issues)
pytest pi_lawyer/tests/shared/test_core_state.py -v
```
