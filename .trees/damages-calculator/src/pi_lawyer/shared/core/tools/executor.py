"""
Tool Executor

This module provides functionality for executing tools based on their schemas.
It integrates with LangGraph's tool handling system and supports both synchronous
and asynchronous tool execution with proper error handling and tenant isolation.

Key Features:
- Tool registration and execution
- Support for both synchronous and asynchronous tools
- Integration with LangGraph's tool handling
- Tenant isolation for tool execution
- Proper error handling and logging
- Support for tool call formats from different LLM providers

Usage:
    from pi_lawyer.shared.core.tools import get_tool_executor

    # Get the tool executor
    executor = get_tool_executor()

    # Register a tool
    async def search_documents(query: str, tenant_id: str) -> Dict[str, Any]:
        # Implementation...
        return {"results": [...]}

    executor.register_tool("search_documents", search_documents)

    # Execute a tool
    result = await executor.execute_tool(
        tool_name="search_documents",
        tool_args={"query": "legal precedent", "tenant_id": "tenant-123"}
    )

    # Execute a tool call from an LLM
    result = await executor.execute_tool_call({
        "function": {
            "name": "search_documents",
            "arguments": '{"query": "legal precedent", "tenant_id": "tenant-123"}'
        }
    })
"""

import asyncio
import inspect
import json
import logging
from typing import Any, Callable, Dict, List, Optional

# Set up logging
logger = logging.getLogger(__name__)


class ToolExecutionError(Exception):
    """Exception raised when a tool execution fails."""
    pass


class ToolExecutor:
    """
    Tool executor for AiLex agents.

    This class provides functionality for executing tools based on their schemas.
    It integrates with LangGraph's tool handling system and supports both synchronous
    and asynchronous tool execution with proper error handling and tenant isolation.
    """

    def __init__(self):
        """Initialize the tool executor."""
        self.tools: Dict[str, Callable] = {}
        self.langchain_tools: Dict[str, Any] = {}

    def register_tool(self, name: str, func: Callable) -> None:
        """
        Register a tool function.

        Args:
            name: The name of the tool
            func: The function to execute when the tool is called
        """
        self.tools[name] = func
        logger.info(f"Registered tool: {name}")

        # Create a LangChain tool if not already registered
        if name not in self.langchain_tools:
            self._create_langchain_tool(name, func)

    def register_tools(self, tools: Dict[str, Callable]) -> None:
        """
        Register multiple tool functions.

        Args:
            tools: A dictionary mapping tool names to functions
        """
        for name, func in tools.items():
            self.register_tool(name, func)

    def _create_langchain_tool(self, name: str, func: Callable) -> None:
        """
        Create a LangChain tool from a function.

        Args:
            name: The name of the tool
            func: The function to execute when the tool is called
        """
        try:
            from langchain_core.tools import Tool

            # Get the tool schema
            from pi_lawyer.shared.core.tools.schema import get_tool_schema
            schema = get_tool_schema(name)

            # Extract description and parameters from schema
            description = schema["function"].get("description", f"Tool: {name}")
            parameters = schema["function"].get("parameters", {})

            # Create the tool
            tool = Tool(
                name=name,
                description=description,
                func=func,
                args_schema=parameters
            )

            self.langchain_tools[name] = tool
            logger.debug(f"Created LangChain tool: {name}")
        except ImportError:
            logger.warning("LangChain not available, skipping LangChain tool creation")
        except Exception as e:
            logger.error(f"Failed to create LangChain tool '{name}': {e}")

    def get_langchain_tools(self) -> List[Any]:
        """
        Get all registered LangChain tools.

        Returns:
            List of LangChain tools
        """
        return list(self.langchain_tools.values())

    async def execute_tool(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        tenant_id: Optional[str] = None
    ) -> Any:
        """
        Execute a tool.

        Args:
            tool_name: The name of the tool to execute
            tool_args: The arguments for the tool
            tenant_id: Tenant ID for isolation (optional)

        Returns:
            The result of the tool execution

        Raises:
            ToolExecutionError: If the tool execution fails
        """
        if tool_name not in self.tools:
            raise ToolExecutionError(f"Tool {tool_name} not found")

        # Add tenant_id to tool arguments if provided and not already present
        if tenant_id and "tenant_id" not in tool_args:
            tool_args["tenant_id"] = tenant_id

        try:
            logger.info(f"Executing tool: {tool_name}")
            logger.debug(f"Tool arguments: {json.dumps(tool_args, default=str)}")

            func = self.tools[tool_name]

            # Check if the function is async
            if inspect.iscoroutinefunction(func):
                result = await func(**tool_args)
            else:
                # Run synchronous function in a thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, lambda: func(**tool_args))

            logger.info(f"Tool {tool_name} executed successfully")

            # Convert result to a serializable format if possible
            try:
                serialized_result = json.dumps(result, default=str)
                logger.debug(f"Tool result: {serialized_result}")
            except Exception:
                logger.debug(f"Tool result: {result} (not serializable)")

            return result

        except Exception as e:
            logger.error(f"Tool {tool_name} execution failed: {str(e)}")
            raise ToolExecutionError(f"Tool {tool_name} execution failed: {str(e)}")

    async def execute_tool_call(
        self,
        tool_call: Dict[str, Any],
        tenant_id: Optional[str] = None
    ) -> Any:
        """
        Execute a tool call from an LLM.

        Args:
            tool_call: The tool call from the LLM
            tenant_id: Tenant ID for isolation (optional)

        Returns:
            The result of the tool execution

        Raises:
            ToolExecutionError: If the tool execution fails
        """
        # Handle different tool call formats

        # OpenAI format: {"type": "function", "function": {"name": "...", "arguments": "..."}}
        if "function" in tool_call:
            function_call = tool_call["function"]

            if "name" not in function_call:
                raise ToolExecutionError("Invalid tool call: missing function name")

            if "arguments" not in function_call:
                raise ToolExecutionError("Invalid tool call: missing function arguments")

            tool_name = function_call["name"]

            # Parse arguments
            try:
                if isinstance(function_call["arguments"], str):
                    tool_args = json.loads(function_call["arguments"])
                else:
                    tool_args = function_call["arguments"]
            except json.JSONDecodeError:
                raise ToolExecutionError(f"Invalid tool arguments: {function_call['arguments']}")

            return await self.execute_tool(tool_name, tool_args, tenant_id)

        # Anthropic format: {"name": "...", "input": {...}}
        elif "name" in tool_call and "input" in tool_call:
            tool_name = tool_call["name"]
            tool_args = tool_call["input"]

            return await self.execute_tool(tool_name, tool_args, tenant_id)

        # LangGraph format: {"tool": "...", "tool_input": {...}}
        elif "tool" in tool_call and "tool_input" in tool_call:
            tool_name = tool_call["tool"]
            tool_args = tool_call["tool_input"]

            return await self.execute_tool(tool_name, tool_args, tenant_id)

        # Unknown format
        else:
            raise ToolExecutionError(f"Unsupported tool call format: {tool_call}")

    async def execute_langgraph_tool(
        self,
        tool_invocation: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        Execute a tool invocation from LangGraph.

        Args:
            tool_invocation: The tool invocation from LangGraph
            config: LangGraph runnable configuration

        Returns:
            The result of the tool execution

        Raises:
            ToolExecutionError: If the tool execution fails
        """
        # Extract tenant_id from config if available
        tenant_id = None
        if config and "configurable" in config:
            tenant_id = config["configurable"].get("tenant_id")

        # Handle LangGraph tool invocation format
        if "tool" in tool_invocation and "tool_input" in tool_invocation:
            tool_name = tool_invocation["tool"]
            tool_args = tool_invocation["tool_input"]

            return await self.execute_tool(tool_name, tool_args, tenant_id)
        else:
            raise ToolExecutionError(f"Unsupported tool invocation format: {tool_invocation}")


# Singleton instance for the tool executor
_tool_executor: Optional[ToolExecutor] = None


def get_tool_executor() -> ToolExecutor:
    """
    Get the singleton instance of the tool executor.

    Returns:
        The tool executor instance
    """
    global _tool_executor

    if _tool_executor is None:
        _tool_executor = ToolExecutor()

    return _tool_executor
