"""
Async Job Tool

This module provides functionality for enqueueing asynchronous jobs.
It integrates with the tool executor system and provides a way to
execute long-running tasks asynchronously.

Usage:
    from pi_lawyer.shared.core.tools.executor import get_tool_executor
    
    # Get the tool executor
    executor = get_tool_executor()
    
    # Enqueue an async job
    job_id = await executor.execute_tool(
        tool_name="enqueue_async_job",
        tool_args={
            "tool_name": "documentDraftAgent",
            "params": {"template": "settlement_letter", "matter_id": "123"},
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "thread_id": "thread-789"
        }
    )
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict

# Set up logging
logger = logging.getLogger(__name__)

# Try to import Redis queue service
try:
    from pi_lawyer.services.redis_queue_service import get_redis_queue_service
    REDIS_AVAILABLE = True
except ImportError:
    logger.warning("Redis queue service not available, using in-memory queue")
    REDIS_AVAILABLE = False


# Queue name for async jobs
ASYNC_QUEUE_NAME = "ailex-long"


async def enqueue_async_job(
    tool_name: str,
    params: Dict[str, Any],
    tenant_id: str,
    user_id: str,
    thread_id: str,
    priority: int = 1
) -> str:
    """
    Enqueue an async job.
    
    Args:
        tool_name: The name of the tool to execute asynchronously
        params: The parameters for the tool
        tenant_id: The tenant ID for the job
        user_id: The user ID for the job
        thread_id: The thread ID for the job
        priority: Priority level (lower is higher priority)
        
    Returns:
        job_id: Unique identifier for the job
    """
    # Generate a job ID
    job_id = str(uuid.uuid4())
    
    # Create job data
    job_data = {
        "id": job_id,
        "tool_name": tool_name,
        "params": params,
        "tenant_id": tenant_id,
        "user_id": user_id,
        "thread_id": thread_id,
        "status": "queued",
        "created_at": datetime.now().isoformat(),
        "priority": priority
    }
    
    logger.info(f"Enqueueing async job: {tool_name} for tenant {tenant_id}")
    logger.debug(f"Job data: {json.dumps(job_data, default=str)}")
    
    # Use Redis queue if available
    if REDIS_AVAILABLE:
        try:
            # Get the Redis queue service
            queue_service = get_redis_queue_service(queue_name=ASYNC_QUEUE_NAME)
            
            # Enqueue the job
            queue_service.enqueue(job_data, priority)
            
            logger.info(f"Async job {job_id} enqueued in Redis queue {ASYNC_QUEUE_NAME}")
        except Exception as e:
            logger.error(f"Failed to enqueue job in Redis: {str(e)}")
            # Fall back to in-memory queue
            _enqueue_in_memory(job_data)
    else:
        # Use in-memory queue
        _enqueue_in_memory(job_data)
    
    return job_id


def _enqueue_in_memory(job_data: Dict[str, Any]) -> None:
    """
    Enqueue a job in the in-memory queue.
    
    Args:
        job_data: The job data
    """
    # This is a placeholder for the in-memory queue
    # In a real implementation, this would add the job to a queue
    # that is processed by a worker thread or process
    logger.info(f"Job {job_data['id']} enqueued in memory (placeholder)")
    
    # In a real implementation, we would store the job in a database
    # and have a worker process that polls for new jobs
    # For now, we just log the job
    logger.info(f"Would execute: {job_data['tool_name']} with params: {job_data['params']}")


# Register the tool with the executor
def register_async_job_tool() -> None:
    """Register the async job tool with the executor."""
    from pi_lawyer.shared.core.tools.executor import get_tool_executor
    
    # Get the tool executor
    executor = get_tool_executor()
    
    # Register the tool
    executor.register_tool("enqueue_async_job", enqueue_async_job)
    
    logger.info("Registered enqueue_async_job tool")
