"""
State Management for LangGraph

This module implements the state management for LangGraph agents.
It uses Pydantic models for type safety and validation.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator

# Set up logging
logger = logging.getLogger(__name__)

class AiLexState(BaseModel):
    """
    The main state model for AiLex agents.

    This model represents the state that is passed between agents in the LangGraph.
    It contains all the information needed for agents to make decisions and maintain context.
    """

    # Tenant and user information
    tenant_id: str = Field(..., description="The tenant ID")
    user_id: str = Field(..., description="The user ID")

    # Matter information
    matter_id: Optional[str] = Field(None, description="The current matter ID")

    # Locale information
    locale: str = Field("en", description="The locale for the user")

    # Document information
    active_doc: Optional[str] = Field(None, description="The ID of the active document")

    # Memory for agent state
    memory: Dict[str, Any] = Field(default_factory=dict, description="Memory for agent state")

    # Message history
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="Message history")

    # Thread information
    thread_id: str = Field(..., description="The thread ID for the conversation")

    # Timestamps
    created_at: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="When the state was created")
    updated_at: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="When the state was last updated")

    @field_validator("updated_at", mode="before")
    @classmethod
    def update_timestamp(cls, _):
        """Update the timestamp whenever the state is modified."""
        return datetime.now(timezone.utc).isoformat()

    def snapshot(self) -> Dict[str, Any]:
        """
        Create a snapshot of the current state.

        Returns:
            A dictionary representation of the state
        """
        return self.model_dump()

    def to_json(self) -> str:
        """
        Convert the state to a JSON string.

        Returns:
            A JSON string representation of the state
        """
        return json.dumps(self.model_dump(), default=str)

    @classmethod
    def from_json(cls, json_str: str) -> "AiLexState":
        """
        Create a state object from a JSON string.

        Args:
            json_str: A JSON string representation of the state

        Returns:
            An AiLexState object
        """
        data = json.loads(json_str)
        return cls(**data)

    def add_message(self, role: str, content: Union[str, List[Dict[str, Any]]]) -> None:
        """
        Add a message to the message history.

        Args:
            role: The role of the message sender (user, assistant, system)
            content: The content of the message
        """
        self.messages.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_last_message(self) -> Optional[Dict[str, Any]]:
        """
        Get the last message in the history.

        Returns:
            The last message or None if there are no messages
        """
        if not self.messages:
            return None
        return self.messages[-1]

    def get_messages_by_role(self, role: str) -> List[Dict[str, Any]]:
        """
        Get all messages with a specific role.

        Args:
            role: The role to filter by

        Returns:
            A list of messages with the specified role
        """
        return [msg for msg in self.messages if msg["role"] == role]

    def set_memory(self, key: str, value: Any) -> None:
        """
        Set a value in the memory.

        Args:
            key: The key to set
            value: The value to set
        """
        self.memory[key] = value
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_memory(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the memory.

        Args:
            key: The key to get
            default: The default value to return if the key is not found

        Returns:
            The value for the key or the default value
        """
        return self.memory.get(key, default)

    def clear_memory(self) -> None:
        """Clear the memory."""
        self.memory = {}
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_active_agent(self) -> Optional[str]:
        """
        Get the active agent from memory.

        Returns:
            The active agent or None if not set
        """
        return self.get_memory("agent")

    def set_active_agent(self, agent: str) -> None:
        """
        Set the active agent in memory.

        Args:
            agent: The agent to set as active
        """
        self.set_memory("agent", agent)


# Create a factory function for creating new state objects
def create_state(
    tenant_id: str,
    user_id: str,
    thread_id: str,
    matter_id: Optional[str] = None,
    locale: str = "en",
    active_doc: Optional[str] = None,
    initial_memory: Optional[Dict[str, Any]] = None,
    initial_messages: Optional[List[Dict[str, Any]]] = None
) -> AiLexState:
    """
    Create a new state object.

    Args:
        tenant_id: The tenant ID
        user_id: The user ID
        thread_id: The thread ID
        matter_id: The matter ID (optional)
        locale: The locale (default: "en")
        active_doc: The active document ID (optional)
        initial_memory: Initial memory (optional)
        initial_messages: Initial messages (optional)

    Returns:
        A new AiLexState object
    """
    return AiLexState(
        tenant_id=tenant_id,
        user_id=user_id,
        thread_id=thread_id,
        matter_id=matter_id,
        locale=locale,
        active_doc=active_doc,
        memory=initial_memory or {},
        messages=initial_messages or []
    )
