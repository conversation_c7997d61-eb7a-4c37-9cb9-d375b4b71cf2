#!/usr/bin/env python
"""
Standalone document processing worker.

This worker process pulls document processing jobs from the Redis queue
and processes them independently. It can be scaled horizontally by
running multiple instances.
"""

import argparse
import json
import logging
import signal
import time
import uuid
from datetime import datetime
from typing import Any, Dict

# Configure logging first
from pythonjsonlogger import jsonlogger


# Create custom JSON formatter with correlation IDs
class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)
        log_record["timestamp"] = datetime.utcnow().isoformat()
        log_record["level"] = record.levelname
        log_record["correlation_id"] = getattr(record, "correlation_id", "-")
        log_record["tenant_id"] = getattr(record, "tenant_id", "-")
        log_record["worker_id"] = getattr(record, "worker_id", "-")
        log_record["service"] = "document-worker"


# Set up JSON logging
formatter = CustomJsonFormatter("%(timestamp)s %(level)s %(name)s %(message)s")
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger = logging.getLogger()
logger.addHandler(handler)
logger.setLevel(logging.INFO)


# Add filter to inject context properties into records
class ContextFilter(logging.Filter):
    """Add worker-specific context to log records."""

    def __init__(self, worker_id=None, correlation_id=None, tenant_id=None):
        super().__init__()
        self.worker_id = worker_id
        self.correlation_id = correlation_id
        self.tenant_id = tenant_id

    def filter(self, record):
        if not hasattr(record, "worker_id"):
            record.worker_id = self.worker_id
        if not hasattr(record, "correlation_id"):
            record.correlation_id = self.correlation_id
        if not hasattr(record, "tenant_id"):
            record.tenant_id = self.tenant_id
        return True


# Import services after logging is configured
from pi_lawyer.services.document_processing_queue import DocumentProcessingQueue
from pi_lawyer.services.metrics_collector import MetricsCollector
from pi_lawyer.services.redis_lock_service import (
    get_redis_lock_service,
)
from pi_lawyer.services.redis_queue_service import get_redis_queue_service
from pi_lawyer.utils.circuit_breaker import CircuitBreaker, CircuitOpenError

# Global flag for graceful shutdown
running = True
worker_id = str(uuid.uuid4())[:8]  # Short unique identifier for this worker

# Set up the context filter with worker_id
context_filter = ContextFilter(worker_id=worker_id)
logger.addFilter(context_filter)

# Initialize metrics collector
metrics = MetricsCollector(prefix="document_worker")


def signal_handler(sig, frame):
    """Handle termination signals."""
    global running
    logger.info(f"Worker {worker_id} received shutdown signal")
    running = False


def update_context(correlation_id=None, tenant_id=None):
    """Update the logging context for the current task."""
    global context_filter
    context_filter.correlation_id = correlation_id or "-"
    context_filter.tenant_id = tenant_id or "-"


def process_document(job: Dict[str, Any], processor: DocumentProcessingQueue) -> bool:
    """
    Process a single document processing job.

    Args:
        job: Document job dictionary
        processor: DocumentProcessingQueue instance

    Returns:
        True if successful, False otherwise
    """
    job_id = job.get("id", "unknown")
    document_id = job.get("document_id", "unknown")
    tenant_id = job.get("tenant_id", "unknown")

    # Update logging context
    update_context(correlation_id=job_id, tenant_id=tenant_id)

    # Create resource name for locking
    resource_name = f"doc:{document_id}"

    # Get the lock service
    lock_service = get_redis_lock_service()

    try:
        # Try to acquire a lock for this document
        lock_acquired = lock_service.acquire_lock(
            resource_name,
            owner_id=worker_id,
            timeout=300,  # 5 minutes should be enough for processing
            blocking=True,
            max_retry_time=10,  # Try for up to 10 seconds
        )

        if not lock_acquired:
            logger.warning(
                f"Could not acquire lock for document {document_id}",
                extra={
                    "action": "process_document",
                    "status": "lock_failed",
                    "document_id": document_id,
                },
            )
            # Record metric for lock acquisition failure
            metrics.increment("process_lock_failed", tags={"tenant": tenant_id})
            return False

        logger.info(
            f"Processing document {document_id} (job: {job_id})",
            extra={
                "action": "process_document",
                "status": "started",
                "document_id": document_id,
            },
        )

        # Record start time for processing duration metric
        start_time = time.time()

        # Create circuit breaker for this processing operation
        circuit_breaker = CircuitBreaker(
            name=f"doc_processor_{tenant_id}", failure_threshold=3, reset_timeout=60
        )

        try:
            # Use circuit breaker pattern for the processing call
            with circuit_breaker:
                # Call the actual document processing logic
                # This would extract the document details and call the appropriate processor methods
                result = processor._process_with_circuit_breaker(
                    processor._process_document_job,
                    job=job,
                    circuit_name=f"worker_{worker_id}",
                )

                # Record processing time
                metrics.observe(
                    "document_processing_duration",
                    time.time() - start_time,
                    tags={"tenant": tenant_id},
                )

                logger.info(
                    f"Successfully processed document {document_id}",
                    extra={
                        "action": "process_document",
                        "status": "success",
                        "document_id": document_id,
                        "duration_ms": int((time.time() - start_time) * 1000),
                    },
                )
                metrics.increment(
                    "document_processed",
                    tags={"tenant": tenant_id, "status": "success"},
                )
                return True

        except CircuitOpenError as e:
            # Circuit breaker is open, services may be overloaded
            logger.error(
                f"Circuit breaker open for document processing: {str(e)}",
                extra={
                    "action": "process_document",
                    "status": "circuit_open",
                    "document_id": document_id,
                    "error": str(e),
                },
            )
            metrics.increment("circuit_breaker_trips", tags={"tenant": tenant_id})
            return False

        except Exception as e:
            # Processing failed
            logger.error(
                f"Error processing document {document_id}: {str(e)}",
                exc_info=True,
                extra={
                    "action": "process_document",
                    "status": "error",
                    "document_id": document_id,
                    "error": str(e),
                },
            )
            metrics.increment(
                "document_processed", tags={"tenant": tenant_id, "status": "error"}
            )
            return False

    finally:
        # Always release the lock when done
        try:
            lock_service.release_lock(resource_name, worker_id)
        except Exception as e:
            logger.warning(
                f"Failed to release lock for document {document_id}: {str(e)}"
            )


def maintenance_task():
    """
    Run maintenance tasks as leader.

    Uses Redis locks for leader election to ensure only one worker
    performs maintenance at a time.
    """
    lock_service = get_redis_lock_service()
    queue_service = get_redis_queue_service()

    # Try to acquire the leader lock
    leader_lock = "worker_leader"
    is_leader = lock_service.acquire_lock(
        leader_lock,
        owner_id=worker_id,
        timeout=60,  # Hold leadership for 60 seconds
        blocking=False,
    )

    if not is_leader:
        # Not the leader, nothing to do
        return

    logger.info(
        f"Worker {worker_id} acquired leadership for maintenance tasks",
        extra={"action": "maintenance", "status": "leader_elected"},
    )

    try:
        # Task 1: Requeue stalled jobs
        stalled_count = queue_service.requeue_stalled_jobs(
            timeout_minutes=30,  # Jobs processing for 30+ minutes are stalled
            max_jobs=100,  # Limit number of jobs to requeue
        )

        if stalled_count > 0:
            logger.info(
                f"Requeued {stalled_count} stalled jobs",
                extra={
                    "action": "maintenance",
                    "task": "requeue_stalled",
                    "count": stalled_count,
                },
            )
            metrics.gauge("stalled_jobs_requeued", stalled_count)

        # Task 2: Clean up expired job status entries
        cleaned_count = queue_service.cleanup_expired_status(
            older_than_days=7  # Remove status entries older than 7 days
        )

        if cleaned_count > 0:
            logger.info(
                f"Cleaned up {cleaned_count} expired job status entries",
                extra={
                    "action": "maintenance",
                    "task": "cleanup_status",
                    "count": cleaned_count,
                },
            )
            metrics.gauge("expired_status_cleaned", cleaned_count)

        # Task 3: Update queue metrics
        queue_stats = queue_service.get_queue_stats()
        for key, value in queue_stats.items():
            metrics.gauge(f"queue_{key}", value)

        logger.info(
            f"Updated queue metrics: {json.dumps(queue_stats)}",
            extra={
                "action": "maintenance",
                "task": "update_metrics",
                "stats": queue_stats,
            },
        )

    finally:
        # Always release the leader lock when done
        lock_service.release_lock(leader_lock, worker_id)
        logger.info(
            f"Worker {worker_id} released leadership",
            extra={"action": "maintenance", "status": "leader_released"},
        )


def worker_loop(args):
    """
    Main worker loop that processes jobs from the Redis queue.

    Args:
        args: Command-line arguments
    """
    global running, worker_id

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info(
        f"Document worker {worker_id} starting up",
        extra={"action": "startup", "poll_interval": args.poll_interval},
    )

    # Initialize document processing queue
    queue = DocumentProcessingQueue(use_redis=True)
    redis_queue = get_redis_queue_service()

    # Initialize maintenance interval tracking
    last_maintenance = time.time()
    maintenance_interval = args.maintenance_interval

    # Start the worker loop
    while running:
        try:
            # Check if it's time to run maintenance
            current_time = time.time()
            if current_time - last_maintenance > maintenance_interval:
                maintenance_task()
                last_maintenance = current_time

            # Try to get a job from the queue
            job = redis_queue.dequeue_job()

            if job:
                # Process the document job
                process_document(job, queue)
            else:
                # No jobs available, wait briefly
                time.sleep(args.poll_interval)

        except Exception as e:
            logger.error(
                f"Unexpected error in worker loop: {str(e)}",
                exc_info=True,
                extra={"action": "worker_error"},
            )
            metrics.increment("worker_errors")

            # Sleep briefly to avoid tight error loops
            time.sleep(1)

    logger.info(
        f"Document worker {worker_id} shutting down", extra={"action": "shutdown"}
    )


def create_health_endpoint(port=8080):
    """
    Start a simple HTTP server to expose health and metrics endpoints.

    Args:
        port: Port to listen on
    """
    from http.server import BaseHTTPRequestHandler, HTTPServer
    from threading import Thread

    class HealthHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == "/health":
                # Health check endpoint
                self.send_response(200)
                self.send_header("Content-type", "application/json")
                self.end_headers()

                health_data = {
                    "status": "healthy",
                    "worker_id": worker_id,
                    "uptime_seconds": int(time.time() - startup_time),
                }

                self.wfile.write(json.dumps(health_data).encode())

            elif self.path == "/metrics":
                # Prometheus metrics endpoint
                self.send_response(200)
                self.send_header("Content-type", "text/plain")
                self.end_headers()

                # Get metrics from collector
                metric_data = metrics.export_prometheus()
                self.wfile.write(metric_data.encode())

            else:
                self.send_response(404)
                self.end_headers()

    httpd = HTTPServer(("0.0.0.0", port), HealthHandler)

    def run_server():
        logger.info(f"Starting health endpoint on port {port}")
        httpd.serve_forever()

    # Run the server in a separate thread
    server_thread = Thread(target=run_server, daemon=True)
    server_thread.start()

    return httpd


if __name__ == "__main__":
    # Record startup time
    startup_time = time.time()

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Document processing worker")
    parser.add_argument(
        "--poll-interval", type=float, default=1.0, help="Polling interval in seconds"
    )
    parser.add_argument(
        "--maintenance-interval",
        type=int,
        default=300,
        help="Interval in seconds between maintenance tasks",
    )
    parser.add_argument(
        "--health-port", type=int, default=8080, help="Port for health check endpoint"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level",
    )

    args = parser.parse_args()

    # Set log level from arguments
    logger.setLevel(getattr(logging, args.log_level))

    # Start health endpoint in a separate thread
    health_server = create_health_endpoint(args.health_port)

    try:
        # Start the main worker loop
        worker_loop(args)
    finally:
        # Ensure health server is shut down
        health_server.shutdown()
