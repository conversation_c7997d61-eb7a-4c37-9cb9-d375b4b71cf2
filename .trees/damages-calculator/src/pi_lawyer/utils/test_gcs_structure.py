"""
Test script for demonstrating the GCS folder structure.
"""

import os
import sys

from dotenv import load_dotenv

from pi_lawyer.utils.storage_utils import test_gcs_structure

# Load environment variables
load_dotenv()


def main():
    """Run the GCS structure test."""
    print("GCS Structure Test")
    print("-----------------")

    # Check if GCS environment variables are configured
    if not os.getenv("GCS_BUCKET_NAME"):
        print(
            "Error: GCS_BUCKET_NAME environment variable not set. Please set it before running this test."
        )
        sys.exit(1)

    # See if the user wants to actually upload files
    run_test = len(sys.argv) > 1 and sys.argv[1].lower() == "upload"

    # Get tenant_id and auth_user_id from command line if provided
    tenant_id = (
        sys.argv[2] if len(sys.argv) > 2 else "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11"
    )
    auth_user_id = (
        sys.argv[3] if len(sys.argv) > 3 else "c35cfd89-faad-4621-bc3a-dc1c26ad891c"
    )

    print(f"Using tenant_id: {tenant_id}")
    print(f"Using auth_user_id: {auth_user_id}")
    print(f"Upload mode: {'ON' if run_test else 'OFF'}")

    if not run_test:
        print("\nThis is a DRY RUN. No files will be uploaded.")
        print("To actually upload test files, add 'upload' as a command-line argument:")
        print("  python test_gcs_structure.py upload")

    # Run the test
    results = test_gcs_structure(
        tenant_id=tenant_id, auth_user_id=auth_user_id, run_test=run_test
    )

    print(f"\nTest status: {results['status']}")
    print(results["message"])

    # If we're not actually uploading, show what paths would be created
    if not run_test:
        print("\nFile paths that would be created:")
        print("--------------------------------")

        # Hypothetical paths for demonstration
        print(
            f"1. Case document: tenants/{tenant_id}/case_documents/policies/admin/[DATE]/[UUID]_firm_policy.txt"
        )
        print(
            f"2. Case document: tenants/{tenant_id}/cases/test-case-123/personal_injury/medical/reports/[DATE]/[UUID]_medical_report.pdf"
        )
        print(
            f"3. Client document: tenants/{tenant_id}/clients/test-client-456/intake/general/[DATE]/[UUID]_client_intake.pdf"
        )
        print(
            "4. Public jurisdiction document: public/jurisdictions/texas/laws/statutes/[UUID]_texas_statutes.pdf"
        )
        print(
            "5. Public practice area document: public/practice_areas/personal_injury/references/guidelines/[UUID]_injury_guidelines.pdf"
        )
    else:
        # If we did upload, print the actual paths
        print("\nActual file paths created:")
        print("----------------------")

        # Format and display the results
        for key, value in results.get("results", {}).items():
            if key.endswith("_list"):
                continue

            if isinstance(value, dict) and "gcs_path" in value:
                print(f"{key}: {value['gcs_path']}")


if __name__ == "__main__":
    main()
