"""
Query classifier for legal research agent.

This module provides functions to classify whether a query is related to legal topics
and filter out off-topic or nonsensical queries before retrieval.
"""
import json
import logging
import os
from typing import Tuple

import google.generativeai as genai
import groq
from google.generativeai import GenerativeModel

logger = logging.getLogger(__name__)

# Common legal keywords and phrases for basic filtering
LEGAL_KEYWORDS = [
    "law",
    "legal",
    "court",
    "jurisdiction",
    "statute",
    "regulation",
    "attorney",
    "lawyer",
    "plaintiff",
    "defendant",
    "liability",
    "contract",
    "tort",
    "damages",
    "claim",
    "negligence",
    "lawsuit",
    "litigation",
    "legislation",
    "precedent",
    "appeal",
    "injunction",
    "settlement",
    "filing",
    "motion",
    "ruling",
    "judgment",
    "verdict",
    "testimony",
    "evidence",
    "trial",
    "hearing",
    "deposition",
    "brief",
    "pleading",
    "compensation",
    "argument",
    "rights",
    "case law",
    "malpractice",
    "conviction",
    "sentence",
    "defense",
    "prosecution",
    "injured",
    "injury",
    "sue",
    "clause",
    "agreement",
    "judge",
    "jury",
    "arbitration",
    "mediation",
    "probate",
    "felony",
    "misdemeanor",
    "trust",
    "will",
    "estate",
    "property",
    "divorce",
    "custody",
    "bankruptcy",
    "patent",
    "copyright",
    "trademark",
    "regulatory",
    "compliance",
    "duty",
    "standard of care",
    "statute of limitations",
    "breach",
]


class QueryClassifier:
    """Classifier for determining if a query is related to legal topics."""

    def __init__(self, confidence_threshold: float = 0.7):
        """Initialize the query classifier.

        Args:
            confidence_threshold: Threshold for classification confidence (0.0-1.0)
        """
        self.confidence_threshold = confidence_threshold
        self._init_groq_client()
        self._init_gemini_client()

    def _init_groq_client(self):
        """Initialize the Groq client for fast classification."""
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            logger.warning("GROQ_API_KEY not found in environment variables")
            self.groq_client = None
        else:
            self.groq_client = groq.Groq(api_key=groq_api_key)
            logger.info("Groq client initialized")

    def _init_gemini_client(self):
        """Initialize the Gemini client for more complex classification."""
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not gemini_api_key:
            logger.warning("GEMINI_API_KEY not found in environment variables")
            self.gemini_model = None
        else:
            genai.configure(api_key=gemini_api_key)
            self.gemini_model = GenerativeModel("gemini-2.5-pro-exp-03-25")
            logger.info("Gemini client initialized")

    def has_legal_keywords(self, query: str) -> bool:
        """Check if the query contains common legal keywords.

        Args:
            query: User query to check

        Returns:
            True if query contains legal keywords, False otherwise
        """
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in LEGAL_KEYWORDS)

    async def classify_with_groq(self, query: str) -> Tuple[bool, float]:
        """Classify if a query is legal-related using Groq's Mixtral model.

        Args:
            query: User query to classify

        Returns:
            Tuple of (is_legal, confidence_score)
        """
        if not self.groq_client:
            logger.warning("Groq client not initialized, skipping classification")
            return (True, 1.0)  # Default to True if client not available

        try:
            response = self.groq_client.chat.completions.create(
                messages=[
                    {
                        "role": "user",
                        "content": f"""Classify if this query is a valid legal question.
                    Respond with a JSON object containing:
                    {{"is_legal": true/false, "confidence": 0.0-1.0}}

                    Query: {query}""",
                    }
                ],
                model="mixtral-8x7b-32768",
                temperature=0,
                response_format={"type": "json_object"},
            )

            result = json.loads(response.choices[0].message.content.strip())
            return (result["is_legal"], result["confidence"])

        except Exception as e:
            logger.error(f"Error classifying query with Groq: {e}")
            # Default to True (permissive) on error to avoid blocking valid queries
            return (True, 0.5)

    async def classify_with_gemini(self, query: str) -> Tuple[bool, float, str]:
        """Classify if a query is legal-related using Gemini model.

        Args:
            query: User query to classify

        Returns:
            Tuple of (is_legal, confidence_score, explanation)
        """
        if not self.gemini_model:
            logger.warning("Gemini model not initialized, skipping classification")
            return (True, 1.0, "Classification skipped")  # Default if not available

        try:
            response = await self.gemini_model.generate_content_async(
                f"""Classify if this query is a valid legal question that a law firm's research team should answer.
                Respond with a JSON object containing:
                {{"is_legal": true/false, "confidence": 0.0-1.0, "explanation": "brief reasoning"}}

                The query should be classified as legal only if it:
                1. Relates to law, legal practice, or legal concepts
                2. Could reasonably be answered by legal research
                3. Is not nonsensical, inappropriate, or completely unrelated to law

                Query: {query}""",
                generation_config={"temperature": 0},
            )

            result_text = response.text
            # Extract JSON from response
            try:
                result = json.loads(result_text)
                return (result["is_legal"], result["confidence"], result["explanation"])
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON response from Gemini: {result_text}")
                # Default to True but with low confidence if JSON parsing fails
                return (True, 0.6, "Classification parsing failed")

        except Exception as e:
            logger.error(f"Error classifying query with Gemini: {e}")
            # Default to True (permissive) on error to avoid blocking valid queries
            return (True, 0.5, "Classification error")

    async def is_legal_query(self, query: str) -> Tuple[bool, str]:
        """Determine if a query is a valid legal question using multiple methods.

        Args:
            query: User query to classify

        Returns:
            Tuple of (is_legal, explanation)
        """
        # Method 1: Keyword check (fast pre-filter)
        has_keywords = self.has_legal_keywords(query)

        if not has_keywords:
            return (False, "Query doesn't contain any legal terminology or concepts")

        # Method 2: Model-based classification
        try:
            # First try with fast model
            is_legal_groq, confidence_groq = await self.classify_with_groq(query)

            # If high confidence from Groq, use that result
            if confidence_groq >= 0.8:
                explanation = f"Classification confidence: {confidence_groq:.2f}"
                return (is_legal_groq, explanation)

            # Otherwise, use more powerful model for confirmation
            (
                is_legal_gemini,
                confidence_gemini,
                explanation,
            ) = await self.classify_with_gemini(query)

            # Use average confidence as final score
            final_confidence = (confidence_groq + confidence_gemini) / 2
            is_legal = final_confidence >= self.confidence_threshold and is_legal_gemini

            return (is_legal, explanation)

        except Exception as e:
            logger.error(f"Error in query classification: {e}")
            # If classification fails, fall back to keyword check
            return (has_keywords, "Fallback to keyword-based classification")
