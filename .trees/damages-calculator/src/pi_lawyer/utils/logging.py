"""Logging utilities for the pi_lawyer application."""
import logging
from typing import Optional


def setup_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """Set up a logger with the specified name and level.

    Args:
        name: The name of the logger
        level: The logging level (defaults to DEBUG if None)

    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger(name)

    if level is None:
        level = logging.DEBUG

    logger.setLevel(level)

    # Create console handler if no handlers exist
    if not logger.handlers:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)

        # Create formatter
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        console_handler.setFormatter(formatter)

        # Add handler to logger
        logger.addHandler(console_handler)

    return logger
