"""
Structured logging configuration with JSON formatting.

This module provides utilities for setting up structured logging
with JSON format, correlation IDs, and context-aware logging filters.
"""

import json
import logging
import os
import threading
import traceback
import uuid
from datetime import datetime
from typing import Optional

# Import JSON formatter if available
try:
    from pythonjsonlogger import jsonlogger

    JSON_LOGGER_AVAILABLE = True
except ImportError:
    JSON_LOGGER_AVAILABLE = False

# Thread-local storage for request context
_thread_local = threading.local()


def get_correlation_id() -> str:
    """Get the current correlation ID or generate a new one."""
    if not hasattr(_thread_local, "correlation_id") or not _thread_local.correlation_id:
        _thread_local.correlation_id = str(uuid.uuid4())
    return _thread_local.correlation_id


def set_correlation_id(correlation_id: str) -> None:
    """Set the correlation ID for the current thread."""
    _thread_local.correlation_id = correlation_id


def get_tenant_id() -> Optional[str]:
    """Get the current tenant ID."""
    return getattr(_thread_local, "tenant_id", None)


def set_tenant_id(tenant_id: str) -> None:
    """Set the tenant ID for the current thread."""
    _thread_local.tenant_id = tenant_id


def get_user_id() -> Optional[str]:
    """Get the current user ID."""
    return getattr(_thread_local, "user_id", None)


def set_user_id(user_id: str) -> None:
    """Set the user ID for the current thread."""
    _thread_local.user_id = user_id


def clear_context() -> None:
    """Clear all context variables from the current thread."""
    if hasattr(_thread_local, "correlation_id"):
        delattr(_thread_local, "correlation_id")
    if hasattr(_thread_local, "tenant_id"):
        delattr(_thread_local, "tenant_id")
    if hasattr(_thread_local, "user_id"):
        delattr(_thread_local, "user_id")


class ContextFilter(logging.Filter):
    """Add context information to log records."""

    def __init__(self, service_name: str = None):
        super().__init__()
        self.service_name = service_name or os.getenv("SERVICE_NAME", "pi-lawyer")

    def filter(self, record):
        """Add context fields to the log record."""
        # Add correlation ID
        if not hasattr(record, "correlation_id"):
            record.correlation_id = get_correlation_id()

        # Add tenant ID if available
        if not hasattr(record, "tenant_id"):
            tenant_id = get_tenant_id()
            record.tenant_id = tenant_id if tenant_id else "-"

        # Add user ID if available
        if not hasattr(record, "user_id"):
            user_id = get_user_id()
            record.user_id = user_id if user_id else "-"

        # Add service name
        record.service = self.service_name

        # Add environment
        record.environment = os.getenv("APP_ENV", "development")

        return True


class JsonLogFormatter(
    jsonlogger.JsonFormatter if JSON_LOGGER_AVAILABLE else logging.Formatter
):
    """
    JSON formatter for log records with additional context.
    Falls back to standard formatter if pythonjsonlogger is not available.
    """

    def __init__(
        self, fmt=None, datefmt=None, style="%", json_default=None, json_encoder=None
    ):
        if JSON_LOGGER_AVAILABLE:
            super().__init__(
                fmt=fmt or "%(timestamp)s %(level)s %(name)s %(message)s",
                datefmt=datefmt,
                json_default=json_default,
                json_encoder=json_encoder,
            )
        else:
            # Fallback to standard formatter
            super().__init__(
                fmt=fmt or "%(asctime)s - %(levelname)s - %(name)s - %(message)s",
                datefmt=datefmt,
            )

    def add_fields(self, log_record, record, message_dict):
        """Add custom fields to the log record."""
        if JSON_LOGGER_AVAILABLE:
            super().add_fields(log_record, record, message_dict)

            # Add standard fields
            log_record["timestamp"] = datetime.utcnow().isoformat()
            log_record["level"] = record.levelname

            # Add context fields
            log_record["correlation_id"] = getattr(record, "correlation_id", "-")
            log_record["tenant_id"] = getattr(record, "tenant_id", "-")
            log_record["user_id"] = getattr(record, "user_id", "-")
            log_record["service"] = getattr(record, "service", "-")
            log_record["environment"] = getattr(record, "environment", "development")

            # Add exception info if present
            if record.exc_info:
                log_record["exception"] = traceback.format_exception(*record.exc_info)

    def format(self, record):
        """Format the log record."""
        if JSON_LOGGER_AVAILABLE:
            return super().format(record)
        else:
            # Create a JSON-like format as a fallback
            formatted = super().format(record)

            # Add context as JSON-like string
            context = {
                "correlation_id": getattr(record, "correlation_id", "-"),
                "tenant_id": getattr(record, "tenant_id", "-"),
                "user_id": getattr(record, "user_id", "-"),
                "service": getattr(record, "service", "-"),
                "environment": getattr(record, "environment", "development"),
            }

            # Add context string to the log message
            return f"{formatted} | {json.dumps(context)}"


def configure_logging(service_name=None, log_level=None):
    """
    Configure structured JSON logging for the application.

    Args:
        service_name: Name of the service for logs
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    root_logger = logging.getLogger()

    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Determine log level
    log_level = log_level or os.getenv("LOG_LEVEL", "INFO")
    numeric_level = getattr(logging, log_level, logging.INFO)
    root_logger.setLevel(numeric_level)

    # Create handler (stdout)
    handler = logging.StreamHandler()

    # Create and set formatter
    formatter = JsonLogFormatter()
    handler.setFormatter(formatter)

    # Add context filter
    context_filter = ContextFilter(service_name)
    handler.addFilter(context_filter)

    # Add handler to root logger
    root_logger.addHandler(handler)

    # Log the configuration
    logging.info(
        "Logging configured",
        extra={
            "service": service_name or os.getenv("SERVICE_NAME", "pi-lawyer"),
            "log_level": log_level,
            "json_formatter": JSON_LOGGER_AVAILABLE,
        },
    )


def log_with_context(
    logger, level, message, correlation_id=None, tenant_id=None, user_id=None, **kwargs
):
    """
    Log a message with thread context.

    Args:
        logger: Logger instance
        level: Log level (debug, info, warning, error, critical)
        message: Log message
        correlation_id: Optional correlation ID
        tenant_id: Optional tenant ID
        user_id: Optional user ID
        **kwargs: Additional log fields
    """
    # Set context if provided
    if correlation_id:
        set_correlation_id(correlation_id)
    if tenant_id:
        set_tenant_id(tenant_id)
    if user_id:
        set_user_id(user_id)

    # Get logging method by name
    log_method = getattr(logger, level.lower())

    # Log with extra context
    log_method(message, extra=kwargs)
