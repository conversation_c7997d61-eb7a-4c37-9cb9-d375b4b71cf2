"""
Voyage AI Reranker implementation for improving retrieval quality.
"""
import logging
import os
from typing import List, Optional, Tuple

import voyageai
from langchain_core.documents import Document

logger = logging.getLogger(__name__)


class VoyageAIReranker:
    """
    Reranker implementation using Voyage AI's reranking models.

    This class provides functionality to rerank a list of documents based on
    their relevance to a query, improving retrieval quality beyond vector similarity.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "rerank-2",
        top_k: Optional[int] = None,
    ):
        """Initialize the Voyage AI reranker.

        Args:
            api_key: Voyage AI API key (defaults to VOYAGE_API_KEY env var)
            model: Voyage AI reranking model to use (defaults to voyage-reranker-v1)
            top_k: Number of top documents to return (defaults to all)
        """
        self.api_key = api_key or os.getenv("VOYAGE_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Voyage AI API key is required. Set VOYAGE_API_KEY environment variable or pass api_key."
            )

        self.model = model
        self.top_k = top_k
        self.client = voyageai.Client(api_key=self.api_key)
        logger.info(f"Initialized VoyageAIReranker with model {self.model}")

    def rerank_texts(self, query: str, texts: List[str]) -> List[Tuple[str, float]]:
        """Rerank a list of text documents based on relevance to the query.

        Args:
            query: The query to rerank documents against
            texts: List of text documents to rerank

        Returns:
            List of (document, score) tuples sorted by relevance
        """
        if not texts:
            return []

        try:
            response = self.client.rerank(
                query=query,
                documents=texts,
                model=self.model,
                top_k=self.top_k,
                truncation=True,
            )

            # Return documents with scores, sorted by relevance
            results = [
                (texts[idx], result.relevance_score)
                for idx, result in enumerate(response.results)
            ]
            return results
        except Exception as e:
            logger.error(f"Error reranking documents with Voyage AI: {e}")
            # Return original texts with equal scores as fallback
            return [(text, 1.0) for text in texts]

    def rerank_langchain_documents(
        self, query: str, documents: List[Document]
    ) -> List[Document]:
        """Rerank a list of LangChain documents based on relevance to the query.

        Args:
            query: The query to rerank documents against
            documents: List of LangChain Document objects to rerank

        Returns:
            List of documents sorted by relevance
        """
        if not documents:
            return []

        try:
            # Extract document texts
            texts = [doc.page_content for doc in documents]

            # Rerank using Voyage AI
            response = self.client.rerank(
                query=query,
                documents=texts,
                model=self.model,
                top_k=self.top_k,
                truncation=True,
            )

            # Create a list of (document, score) tuples
            scored_docs = []
            for idx, result in enumerate(response.results):
                document = documents[result.index]
                # Add score as metadata
                if document.metadata is None:
                    document.metadata = {}
                document.metadata["relevance_score"] = result.relevance_score
                scored_docs.append((document, result.relevance_score))

            # Sort by score (descending) and return documents
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            return [doc for doc, _ in scored_docs]

        except Exception as e:
            logger.error(f"Error reranking documents with Voyage AI: {e}")
            # Return original documents in unchanged order as fallback
            return documents
