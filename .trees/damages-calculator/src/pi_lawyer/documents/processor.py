"""Document processing and text extraction pipeline."""
import re
from dataclasses import dataclass
from typing import Any, Dict, List

import fitz  # PyMuPDF
import nltk
import pytesseract
from langchain.text_splitter import RecursiveCharacterTextSplitter
from PIL import Image


@dataclass
class DocumentChunk:
    """Represents a chunk of processed document text."""

    content: str
    metadata: Dict[str, Any]
    page_num: int
    chunk_num: int


class DocumentProcessor:
    """Handles document processing and text extraction."""

    def __init__(self):
        """Initialize document processor."""
        # Download required NLTK data
        try:
            nltk.data.find("tokenizers/punkt")
        except LookupError:
            nltk.download("punkt")

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", " ", ""],
        )

    async def process_pdf(
        self, file_content: bytes, metadata: Dict[str, Any]
    ) -> List[DocumentChunk]:
        """
        Process a PDF file and extract text chunks.

        Args:
            file_content: PDF file content as bytes
            metadata: Document metadata

        Returns:
            List of DocumentChunk objects
        """
        doc = fitz.open(stream=file_content, filetype="pdf")
        chunks = []

        for page_num in range(len(doc)):
            page = doc[page_num]

            # First try normal text extraction
            text = page.get_text()

            # If no text found, try OCR
            if not text.strip():
                pix = page.get_pixmap()
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                text = pytesseract.image_to_string(img)

            # Clean and normalize text
            text = self._clean_text(text)

            # Split into chunks
            if text.strip():
                page_chunks = self.text_splitter.split_text(text)

                for chunk_num, content in enumerate(page_chunks):
                    chunk = DocumentChunk(
                        content=content,
                        metadata={
                            **metadata,
                            "page_num": page_num + 1,
                            "chunk_num": chunk_num + 1,
                            "total_pages": len(doc),
                        },
                        page_num=page_num + 1,
                        chunk_num=chunk_num + 1,
                    )
                    chunks.append(chunk)

        return chunks

    async def process_text(
        self, text: str, metadata: Dict[str, Any]
    ) -> List[DocumentChunk]:
        """
        Process plain text and split into chunks.

        Args:
            text: Plain text content
            metadata: Document metadata

        Returns:
            List of DocumentChunk objects
        """
        # Clean and normalize text
        text = self._clean_text(text)

        # Split into chunks
        chunks = []
        text_chunks = self.text_splitter.split_text(text)

        for chunk_num, content in enumerate(text_chunks):
            chunk = DocumentChunk(
                content=content,
                metadata={
                    **metadata,
                    "chunk_num": chunk_num + 1,
                    "total_chunks": len(text_chunks),
                },
                page_num=1,  # Plain text treated as single page
                chunk_num=chunk_num + 1,
            )
            chunks.append(chunk)

        return chunks

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        # Remove multiple newlines
        text = re.sub(r"\n\s*\n", "\n\n", text)

        # Remove multiple spaces
        text = re.sub(r"\s+", " ", text)

        # Remove special characters but keep basic punctuation
        text = re.sub(r"[^\w\s.,;?!-]", "", text)

        return text.strip()


# Global processor instance
document_processor = DocumentProcessor()
