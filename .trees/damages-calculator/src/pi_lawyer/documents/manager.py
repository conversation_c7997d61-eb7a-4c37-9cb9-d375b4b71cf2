"""Document management system for PI Lawyer."""
import mimetypes
import uuid
from typing import Any, BinaryIO, Dict, List, Optional

import asyncpg

from ..auth.rbac import Role
from .embeddings import embedding_generator
from .processor import document_processor
from .storage import storage_client


class DocumentManager:
    """Manages document operations including storage, processing, and embedding."""

    def __init__(self, db_pool: asyncpg.Pool):
        """Initialize document manager with database pool."""
        self.db_pool = db_pool

    async def _get_user_accessible_cases(
        self, user_id: str, tenant_id: str, role: str
    ) -> List[str]:
        """Get list of case IDs the user has access to."""
        async with self.db_pool.acquire() as conn:
            if role == Role.PARTNER:
                # Partners can access all cases in their tenant
                cases = await conn.fetch(
                    """
                    SELECT id FROM tenants.cases
                    WHERE tenant_id = $1
                """,
                    tenant_id,
                )
                return [str(case["id"]) for case in cases]
            else:
                # Other roles can only access assigned cases
                cases = await conn.fetch(
                    """
                    SELECT case_id FROM tenants.assignments
                    WHERE tenant_id = $1 AND user_id = $2
                """,
                    tenant_id,
                    user_id,
                )
                return [str(case["case_id"]) for case in cases]

    async def upload_document(
        self,
        file: BinaryIO,
        filename: str,
        case_id: str,
        document_type: str,
        tenant_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Upload and process a document.

        Args:
            file: File object to upload
            filename: Original filename
            case_id: Case ID this document belongs to
            document_type: Type of document (e.g., 'medical_record', 'police_report')
            tenant_id: Optional tenant ID for private documents
            metadata: Additional metadata

        Returns:
            Document information including ID and status
        """
        # Determine content type
        content_type, _ = mimetypes.guess_type(filename)
        if not content_type:
            content_type = "application/octet-stream"

        # Upload to GCS
        file_content = file.read()
        gcs_path, public_url = await storage_client.upload_document(
            file, content_type, tenant_id
        )

        # Generate document ID
        document_id = str(uuid.uuid4())

        # Process document based on type
        if content_type == "application/pdf":
            chunks = await document_processor.process_pdf(
                file_content,
                {
                    "document_id": document_id,
                    "case_id": case_id,
                    "document_type": document_type,
                    **(metadata or {}),
                },
            )
        else:
            # Assume text for now
            text = file_content.decode("utf-8")
            chunks = await document_processor.process_text(
                text,
                {
                    "document_id": document_id,
                    "case_id": case_id,
                    "document_type": document_type,
                    **(metadata or {}),
                },
            )

        # Generate embeddings
        namespace = tenant_id if tenant_id else "public"
        vector_ids = await embedding_generator.generate_embeddings(chunks, namespace)

        # Store in database
        async with self.db_pool.acquire() as conn:
            if tenant_id:
                # Store in tenant schema
                await conn.execute(
                    """
                    INSERT INTO tenants.case_documents
                    (id, tenant_id, case_id, title, gcs_path, document_type, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                    document_id,
                    tenant_id,
                    case_id,
                    filename,
                    gcs_path,
                    document_type,
                    metadata or {},
                )

                # Store chunks
                await conn.executemany(
                    """
                    INSERT INTO tenants.document_chunks
                    (id, tenant_id, document_id, content, pinecone_id, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """,
                    [
                        (
                            str(uuid.uuid4()),
                            tenant_id,
                            document_id,
                            chunk.content,
                            vector_id,
                            {"page_num": chunk.page_num},
                        )
                        for chunk, vector_id in zip(chunks, vector_ids, strict=False)
                    ],
                )

                # Log the upload
                await conn.execute(
                    """
                    INSERT INTO tenants.access_logs
                    (id, tenant_id, user_id, resource_type, resource_id, action, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                    str(uuid.uuid4()),
                    tenant_id,
                    metadata.get("uploaded_by"),
                    "document",
                    document_id,
                    "upload",
                    {"case_id": case_id, "document_type": document_type},
                )
            else:
                # Store in public schema
                await conn.execute(
                    """
                    INSERT INTO public.documents
                    (id, title, gcs_path, metadata)
                    VALUES ($1, $2, $3, $4)
                """,
                    document_id,
                    filename,
                    gcs_path,
                    metadata or {},
                )

                # Store chunks
                await conn.executemany(
                    """
                    INSERT INTO public.chunks
                    (id, document_id, content, pinecone_id, metadata)
                    VALUES ($1, $2, $3, $4, $5)
                """,
                    [
                        (
                            str(uuid.uuid4()),
                            document_id,
                            chunk.content,
                            vector_id,
                            {"page_num": chunk.page_num},
                        )
                        for chunk, vector_id in zip(chunks, vector_ids, strict=False)
                    ],
                )

        return {
            "document_id": document_id,
            "case_id": case_id,
            "gcs_path": gcs_path,
            "public_url": public_url,
            "chunk_count": len(chunks),
            "vector_ids": vector_ids,
        }

    async def search_documents(
        self,
        query: str,
        user_id: str,
        tenant_id: str,
        role: str,
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search documents using semantic search.

        Args:
            query: Search query
            user_id: ID of the user performing the search
            tenant_id: Tenant ID
            role: User's role
            top_k: Number of results to return
            filters: Optional search filters

        Returns:
            List of relevant document chunks with metadata
        """
        # Get user's accessible cases
        accessible_case_ids = await self._get_user_accessible_cases(
            user_id, tenant_id, role
        )

        # Create user context for search
        user_context = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "role": role,
            "accessible_case_ids": accessible_case_ids,
        }

        # Perform semantic search
        results = await embedding_generator.search_similar(
            query, tenant_id, top_k, filters, user_context  # Use tenant_id as namespace
        )

        # Enhance results with document metadata
        async with self.db_pool.acquire() as conn:
            for result in results:
                doc_id = result["metadata"]["document_id"]
                doc = await conn.fetchrow(
                    """
                    SELECT title, document_type, case_id, metadata
                    FROM tenants.case_documents
                    WHERE id = $1 AND tenant_id = $2
                    AND case_id = ANY($3::uuid[])
                """,
                    doc_id,
                    tenant_id,
                    accessible_case_ids,
                )

                if doc:
                    result["document"] = dict(doc)

        # Log the search
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO tenants.access_logs
                (id, tenant_id, user_id, resource_type, resource_id, action, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
                str(uuid.uuid4()),
                tenant_id,
                user_id,
                "search",
                None,
                "search",
                {"query": query, "results_count": len(results)},
            )

        return results

    async def delete_document(
        self, document_id: str, user_id: str, tenant_id: str, role: str
    ) -> None:
        """
        Delete a document and all associated data.

        Args:
            document_id: Document ID to delete
            user_id: ID of the user requesting deletion
            tenant_id: Tenant ID
            role: User's role
        """
        async with self.db_pool.acquire() as conn:
            # Check access
            if role != Role.PARTNER:
                doc = await conn.fetchrow(
                    """
                    SELECT case_id FROM tenants.case_documents
                    WHERE id = $1 AND tenant_id = $2
                """,
                    document_id,
                    tenant_id,
                )

                if not doc:
                    return

                # Check if user has access to the case
                has_access = await conn.fetchval(
                    """
                    SELECT EXISTS(
                        SELECT 1 FROM tenants.assignments
                        WHERE tenant_id = $1
                        AND user_id = $2
                        AND case_id = $3
                    )
                """,
                    tenant_id,
                    user_id,
                    doc["case_id"],
                )

                if not has_access:
                    raise ValueError("User does not have access to this document")

            # Get document info
            doc = await conn.fetchrow(
                """
                SELECT gcs_path FROM tenants.case_documents
                WHERE id = $1 AND tenant_id = $2
            """,
                document_id,
                tenant_id,
            )

            if not doc:
                return

            # Get vector IDs
            vectors = await conn.fetch(
                """
                SELECT pinecone_id FROM tenants.document_chunks
                WHERE document_id = $1 AND tenant_id = $2
            """,
                document_id,
                tenant_id,
            )

            # Delete from GCS
            await storage_client.delete_document(doc["gcs_path"])

            # Delete vectors from Pinecone
            vector_ids = [v["pinecone_id"] for v in vectors]
            await embedding_generator.delete_embeddings(vector_ids, tenant_id)

            # Delete from database
            await conn.execute(
                """
                DELETE FROM tenants.document_chunks
                WHERE document_id = $1 AND tenant_id = $2
            """,
                document_id,
                tenant_id,
            )

            await conn.execute(
                """
                DELETE FROM tenants.case_documents
                WHERE id = $1 AND tenant_id = $2
            """,
                document_id,
                tenant_id,
            )

            # Log the deletion
            await conn.execute(
                """
                INSERT INTO tenants.access_logs
                (id, tenant_id, user_id, resource_type, resource_id, action, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
                str(uuid.uuid4()),
                tenant_id,
                user_id,
                "document",
                document_id,
                "delete",
                None,
            )
