"""Vector embedding generation for document chunks."""
import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Dict, List

import pinecone
from openai import AsyncOpenAI

from .processor import DocumentChunk

logger = logging.getLogger(__name__)


class EmbeddingGenerator:
    """Handles vector embedding generation and storage."""

    def __init__(self):
        """Initialize embedding generator."""
        # Initialize OpenAI client
        self.client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Initialize Pinecone with the module-level API
        api_key = os.getenv("PINECONE_API_KEY")
        environment = os.getenv("PINECONE_ENVIRONMENT", "us-east-1")
        pinecone.init(api_key=api_key, environment=environment)

        # Get or create index
        self.index_name = os.getenv("PINECONE_INDEX_NAME", "pi-lawyer")
        existing_indexes = pinecone.list_indexes()

        if self.index_name not in existing_indexes:
            logger.info(f"Creating index {self.index_name}")
            pinecone.create_index(
                name=self.index_name,
                dimension=1536,  # text-embedding-3-small dimension
                metric="cosine",
            )

        self.index = pinecone.Index(self.index_name)

    async def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text using OpenAI."""
        response = await self.client.embeddings.create(
            model="text-embedding-3-small", input=text, encoding_format="float"
        )
        return response.data[0].embedding

    async def _generate_batch_embeddings(
        self, texts: List[str], batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for a batch of texts."""
        all_embeddings = []

        # Process in batches to respect API limits
        for i in range(0, len(texts), batch_size):
            batch = texts[i : i + batch_size]
            response = await self.client.embeddings.create(
                model="text-embedding-3-small", input=batch, encoding_format="float"
            )
            embeddings = [data.embedding for data in response.data]
            all_embeddings.extend(embeddings)

            # Small delay to respect rate limits
            if i + batch_size < len(texts):
                await asyncio.sleep(0.1)

        return all_embeddings

    async def generate_embeddings(
        self, chunks: List[DocumentChunk], namespace: str
    ) -> List[str]:
        """
        Generate embeddings for document chunks and store in Pinecone.

        Args:
            chunks: List of document chunks
            namespace: Pinecone namespace (public or tenant_id)

        Returns:
            List of Pinecone vector IDs
        """
        # Generate embeddings in batches
        texts = [chunk.content for chunk in chunks]
        embeddings = await self._generate_batch_embeddings(texts)

        # Prepare vectors for Pinecone
        vectors = []
        vector_ids = []

        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings, strict=False)):
            vector_id = f"{chunk.metadata.get('document_id')}_{chunk.page_num}_{chunk.chunk_num}"
            vector_ids.append(vector_id)

            # Enhanced metadata focusing on document context
            metadata = {
                # Document metadata
                "document_id": chunk.metadata.get("document_id"),
                "title": chunk.metadata.get("title", ""),
                "content": chunk.content[:1000],  # Store preview in metadata
                # Access control (only store IDs, actual access check via DB)
                "tenant_id": namespace if namespace != "public" else None,
                "case_id": chunk.metadata.get("case_id"),
                # Document attributes
                "document_type": chunk.metadata.get(
                    "document_type"
                ),  # Generic document type
                # Document structure
                "page_num": chunk.page_num,
                "total_pages": chunk.metadata.get("total_pages"),
                "chunk_num": chunk.chunk_num,
                # Timestamps
                "created_at": datetime.utcnow().isoformat(),
                "document_date": chunk.metadata.get("document_date"),
            }

            vectors.append((vector_id, embedding, metadata))

        # Upsert to Pinecone in batches
        batch_size = 100
        for i in range(0, len(vectors), batch_size):
            batch = vectors[i : i + batch_size]
            try:
                logger.info(
                    f"Upserting batch {i//batch_size + 1} of {(len(vectors)-1)//batch_size + 1} to Pinecone"
                )
                self.index.upsert(vectors=batch, namespace=namespace)
                # Small delay between batches
                if i + batch_size < len(vectors):
                    await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Error upserting vectors to Pinecone: {e}")

        return vector_ids

    async def search_similar(
        self,
        query: str,
        namespace: str,
        top_k: int = 5,
        filter: Dict[str, Any] = None,
        user_context: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for similar chunks using the query.

        Args:
            query: Search query
            namespace: Pinecone namespace to search in
            top_k: Number of results to return
            filter: Optional Pinecone filter
            user_context: User context for RBAC filtering

        Returns:
            List of similar chunks with scores
        """
        # Generate query embedding
        query_embedding = await self._generate_embedding(query)

        # Build RBAC-aware filter using case assignments
        rbac_filter = {}
        if user_context:
            rbac_conditions = []

            # Base tenant check
            if namespace != "public":
                rbac_conditions.append({"tenant_id": user_context.get("tenant_id")})

            # Case access (based on assignments table)
            if user_context.get("accessible_case_ids"):
                rbac_conditions.append(
                    {"case_id": {"$in": user_context["accessible_case_ids"]}}
                )

            rbac_filter = {"$or": rbac_conditions}

        # Combine with any existing filters
        if filter:
            final_filter = {"$and": [rbac_filter, filter]} if rbac_filter else filter
        else:
            final_filter = rbac_filter

        # Search in Pinecone
        try:
            logger.info(
                f"Searching Pinecone index {self.index_name} in namespace {namespace}"
            )
            results = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True,
                namespace=namespace,
                filter=final_filter,
            )
        except Exception as e:
            logger.error(f"Error searching Pinecone: {e}")
            return []

        return [
            {"score": match.score, "metadata": match.metadata, "id": match.id}
            for match in results.matches
        ]

    async def delete_embeddings(self, vector_ids: List[str], namespace: str) -> None:
        """Delete embeddings from Pinecone."""
        self.index.delete(ids=vector_ids, namespace=namespace)


# Global embedding generator instance
embedding_generator = EmbeddingGenerator()
