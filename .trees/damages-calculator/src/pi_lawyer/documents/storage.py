"""Google Cloud Storage integration for document storage."""
import os
import uuid
from datetime import datetime
from typing import <PERSON>ary<PERSON>, Optional, Tuple

from google.cloud import storage
from google.cloud.storage import Blob


class GCSStorage:
    """Google Cloud Storage handler for document storage."""

    def __init__(self):
        """Initialize GCS client."""
        self.client = storage.Client()
        self.bucket_name = os.getenv("GCS_BUCKET_NAME")
        self.bucket = self.client.bucket(self.bucket_name)

    def generate_gcs_path(self, tenant_id: Optional[str] = None) -> str:
        """Generate a unique GCS path for a document."""
        date_prefix = datetime.utcnow().strftime("%Y/%m/%d")
        if tenant_id:
            return f"tenants/{tenant_id}/{date_prefix}/{str(uuid.uuid4())}"
        return f"public/{date_prefix}/{str(uuid.uuid4())}"

    async def upload_document(
        self, file: BinaryIO, content_type: str, tenant_id: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Upload a document to GCS.

        Args:
            file: File-like object to upload
            content_type: MIME type of the file
            tenant_id: Optional tenant ID for private documents

        Returns:
            Tuple of (gcs_path, public_url)
        """
        gcs_path = self.generate_gcs_path(tenant_id)
        blob = self.bucket.blob(gcs_path)

        # Upload with content type and metadata
        blob.upload_from_file(
            file,
            content_type=content_type,
            metadata={
                "tenant_id": tenant_id if tenant_id else "public",
                "uploaded_at": datetime.utcnow().isoformat(),
            },
        )

        return gcs_path, blob.public_url if not tenant_id else None

    async def get_document(self, gcs_path: str) -> Blob:
        """Get a document blob from GCS."""
        return self.bucket.blob(gcs_path)

    async def delete_document(self, gcs_path: str) -> None:
        """Delete a document from GCS."""
        blob = self.bucket.blob(gcs_path)
        blob.delete()

    def generate_signed_url(self, gcs_path: str, expiration: int = 3600) -> str:
        """Generate a signed URL for temporary access."""
        blob = self.bucket.blob(gcs_path)
        return blob.generate_signed_url(
            version="v4",
            expiration=datetime.utcnow().timestamp() + expiration,
            method="GET",
        )


# Global storage instance
storage_client = GCSStorage()
