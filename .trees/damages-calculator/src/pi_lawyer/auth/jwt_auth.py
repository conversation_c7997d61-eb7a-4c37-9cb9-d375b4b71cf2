"""JWT authentication module for PI Lawyer."""
import os
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional

import jwt
from fastapi import HTT<PERSON>Exception, Security
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from pydantic import BaseModel


class JWTConfig:
    """JWT configuration."""

    def __init__(self):
        """Initialize JWT configuration with secure secret handling."""
        self.SECRET_KEY = self._get_jwt_secret()
        self.ALGORITHM = "HS256"
        self.ACCESS_TOKEN_EXPIRE_MINUTES = 30

    def _get_jwt_secret(self) -> str:
        """Get JWT secret with secure fallback for development."""
        jwt_secret = os.environ.get("SUPABASE_JWT_SECRET")

        if not jwt_secret:
            app_env = os.environ.get("APP_ENV", "development")
            if app_env == "production":
                raise ValueError(
                    "SUPABASE_JWT_SECRET environment variable is required in production"
                )
            elif app_env == "development":
                # Generate a secure random secret for development
                # This ensures development is secure while not requiring manual setup
                dev_secret = secrets.token_urlsafe(32)
                print(f"WARNING: Using generated JWT secret for development: {dev_secret[:8]}...")
                return dev_secret
            else:
                raise ValueError(
                    f"SUPABASE_JWT_SECRET environment variable is required for {app_env} environment"
                )

        # Validate secret length for security
        if len(jwt_secret) < 32:
            raise ValueError(
                "SUPABASE_JWT_SECRET must be at least 32 characters long for security"
            )

        return jwt_secret


class Token(BaseModel):
    """Token model."""

    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """Token data model."""

    user_id: str
    tenant_id: str
    role: str
    email: str


class JWTAuth:
    """JWT authentication handler."""

    def __init__(self):
        self.security = HTTPBearer()
        self.config = JWTConfig()

    def create_access_token(
        self,
        user_id: str,
        tenant_id: str,
        role: str,
        email: str,
        expires_delta: Optional[timedelta] = None,
    ) -> str:
        """Create a new JWT token."""
        to_encode = {
            "sub": user_id,
            "tenant_id": tenant_id,
            "role": role,
            "email": email,
        }

        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=self.config.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode, self.config.SECRET_KEY, algorithm=self.config.ALGORITHM
        )
        return encoded_jwt

    def decode_token(self, token: str) -> Dict[str, Any]:
        """Decode and validate JWT token."""
        try:
            payload = jwt.decode(
                token, self.config.SECRET_KEY, algorithms=[self.config.ALGORITHM]
            )
            if payload["exp"] < datetime.utcnow().timestamp():
                raise HTTPException(status_code=401, detail="Token has expired")
            return payload
        except jwt.JWTError:
            raise HTTPException(
                status_code=401, detail="Could not validate credentials"
            )

    async def get_current_user(
        self, credentials: HTTPAuthorizationCredentials = Security(HTTPBearer())
    ) -> TokenData:
        """Get current user from JWT token."""
        token_data = self.decode_token(credentials.credentials)
        return TokenData(
            user_id=token_data["sub"],
            tenant_id=token_data["tenant_id"],
            role=token_data["role"],
            email=token_data["email"],
        )


jwt_auth = JWTAuth()
