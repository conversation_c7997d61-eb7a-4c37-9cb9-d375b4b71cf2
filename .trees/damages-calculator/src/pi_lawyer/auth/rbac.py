"""Role-Based Access Control (RBAC) middleware for PI Lawyer."""
from enum import Enum
from functools import wraps

from fastapi import <PERSON>TT<PERSON>Exception
from psycopg2.extras import RealDictCursor

from .jwt_auth import TokenData, jwt_auth


class Role(str, Enum):
    """User roles."""

    PARTNER = "partner"
    ATTORNEY = "attorney"
    PARALEGAL = "paralegal"
    STAFF = "staff"


class Permission(str, Enum):
    """Resource permissions."""

    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"


# Role-based permission matrix
ROLE_PERMISSIONS = {
    Role.PARTNER: [
        Permission.READ,
        Permission.WRITE,
        Permission.DELETE,
        Permission.ADMIN,
    ],
    Role.ATTORNEY: [Permission.READ, Permission.WRITE],
    Role.PARALEGAL: [Permission.READ, Permission.WRITE],
    Role.STAFF: [Permission.READ],
}


class RBACMiddleware:
    """RBAC middleware for handling access control."""

    def __init__(self, db_pool):
        """Initialize RBAC middleware with database connection pool."""
        self.db_pool = db_pool

    async def check_case_access(
        self, user: TokenData, case_id: str, required_permission: Permission
    ) -> bool:
        """Check if user has required permission for a case."""
        if user.role == Role.PARTNER:
            return True

        with self.db_pool.getconn() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Check assignments table for explicit access
                cur.execute(
                    """
                    SELECT role FROM tenants.assignments
                    WHERE tenant_id = %s
                    AND case_id = %s
                    AND user_id = %s
                """,
                    (user.tenant_id, case_id, user.user_id),
                )

                assignment = cur.fetchone()

                if not assignment:
                    return False

                assigned_role = Role(assignment["role"])
                return required_permission in ROLE_PERMISSIONS[assigned_role]

    async def check_document_access(
        self, user: TokenData, document_id: str, required_permission: Permission
    ) -> bool:
        """Check if user has required permission for a document."""
        if user.role == Role.PARTNER:
            return True

        with self.db_pool.getconn() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # First get the case_id for the document
                cur.execute(
                    """
                    SELECT case_id FROM tenants.case_documents
                    WHERE tenant_id = %s AND id = %s
                """,
                    (user.tenant_id, document_id),
                )

                doc = cur.fetchone()
                if not doc:
                    return False

                # Then check case access
                return await self.check_case_access(
                    user, doc["case_id"], required_permission
                )

    def requires_permission(
        self,
        required_permission: Permission,
        resource_type: str = None,
        resource_id_param: str = None,
    ):
        """Decorator for endpoints requiring specific permissions."""

        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                user = await jwt_auth.get_current_user()

                # For partner role, always allow access
                if user.role == Role.PARTNER:
                    return await func(*args, **kwargs)

                # For specific resource access checks
                if resource_type and resource_id_param:
                    resource_id = kwargs.get(resource_id_param)
                    if not resource_id:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Missing resource ID parameter: {resource_id_param}",
                        )

                    has_access = False
                    if resource_type == "case":
                        has_access = await self.check_case_access(
                            user, resource_id, required_permission
                        )
                    elif resource_type == "document":
                        has_access = await self.check_document_access(
                            user, resource_id, required_permission
                        )

                    if not has_access:
                        raise HTTPException(
                            status_code=403,
                            detail="Insufficient permissions for this resource",
                        )

                # For general role-based permission checks
                elif required_permission not in ROLE_PERMISSIONS[Role(user.role)]:
                    raise HTTPException(
                        status_code=403,
                        detail="Insufficient permissions for this action",
                    )

                return await func(*args, **kwargs)

            return wrapper

        return decorator


# Initialize RBAC middleware with DB pool
# This will be initialized in the main application
rbac = None  # type: Optional[RBACMiddleware]


def init_rbac(db_pool):
    """Initialize RBAC middleware with database connection pool."""
    global rbac
    rbac = RBACMiddleware(db_pool)
