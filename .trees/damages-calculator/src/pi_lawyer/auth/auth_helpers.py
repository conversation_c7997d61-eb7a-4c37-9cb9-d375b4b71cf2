from fastapi import Depends
from pydantic import BaseModel

from .jwt_auth import TokenData, jwt_auth


class AuthUser(BaseModel):
    """User info returned after authentication."""

    id: str
    email: str
    role: str
    tenantId: str
    metadata: dict = {}


async def requireAuth(user: TokenData = Depends(jwt_auth.get_current_user)) -> AuthUser:
    """Dependency to require authentication and return AuthUser."""
    return AuthUser(
        id=user.user_id,
        email=user.email,
        role=user.role,
        tenantId=user.tenant_id,
        metadata={},
    )
