"""Authentication middleware for handling user context."""
from functools import wraps
from typing import Callable, Optional

from fastapi import Depends, HTTPException, Request, status

from pi_lawyer.config import get_config
from pi_lawyer.db.supabase_client import SupabaseClient

from .auth_helpers import AuthUser, requireAuth


def get_token_from_request(request: Request) -> Optional[str]:
    """Extract JWT token from request headers."""
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return None
    return auth_header.split(" ")[1]


def require_auth(roles: Optional[list[str]] = None):
    """Decorator to require authentication and optionally specific roles.

    Args:
        roles: Optional list of required roles. If None, any authenticated user is allowed.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request = kwargs.get("request")
            if not request:
                raise HTTPException(status_code=500, detail="No request object found")

            token = get_token_from_request(request)
            if not token:
                raise HTTPException(status_code=401, detail="Authentication required")

            try:
                # Get Supabase client
                supabase = SupabaseClient(
                    url=get_config()["supabase"]["url"],
                    key=get_config()["supabase"]["key"],
                )

                # Get user context
                user_context = await supabase.get_user_context(token)

                # Check role if required
                if roles and user_context["role"] not in roles:
                    raise HTTPException(
                        status_code=403, detail=f"Required role(s): {', '.join(roles)}"
                    )

                # Add user context to request state
                request.state.user_context = user_context

                return await func(*args, **kwargs)

            except ValueError as e:
                raise HTTPException(status_code=401, detail=str(e))

        return wrapper

    return decorator


def require_case_access(func: Callable) -> Callable:
    """Decorator to verify user has access to the requested case."""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        request = kwargs.get("request")
        case_id = kwargs.get("case_id")

        if not request or not case_id:
            raise HTTPException(status_code=500, detail="Missing request or case_id")

        user_context = getattr(request.state, "user_context", None)
        if not user_context:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Partners can access all cases
        if user_context["role"] == "partner":
            return await func(*args, **kwargs)

        # Check if user is assigned to case
        if case_id not in user_context["assigned_case_ids"]:
            raise HTTPException(
                status_code=403, detail="You do not have access to this case"
            )

        return await func(*args, **kwargs)

    return wrapper


# Add withAuth decorator for route-level authentication
def withAuth(roles: Optional[list[str]] = None):
    """Decorator to require authentication and optionally specific roles."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, auth_user: AuthUser = Depends(requireAuth), **kwargs):
            if roles and auth_user.role not in roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient role permissions",
                )
            return await func(*args, auth_user=auth_user, **kwargs)

        return wrapper

    return decorator
