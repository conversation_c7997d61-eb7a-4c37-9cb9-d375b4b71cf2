"""Tenant resolution middleware for PI Lawyer."""

from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware

from .jwt_auth import TokenData, jwt_auth


class TenantMiddleware(BaseHTTPMiddleware):
    """Middleware for resolving tenant context from request."""

    async def dispatch(self, request: Request, call_next):
        """Process request to extract and validate tenant information."""
        # Skip tenant check for public routes
        if request.url.path.startswith("/public"):
            return await call_next(request)

        # Get tenant from header
        tenant_id = request.headers.get("X-Tenant-ID")
        if not tenant_id:
            raise HTTPException(
                status_code=400, detail="X-Tenant-ID header is required"
            )

        # Get current user from JWT
        try:
            auth = request.headers.get("Authorization")
            if not auth or not auth.startswith("Bearer "):
                raise HTTPException(
                    status_code=401, detail="Invalid authentication credentials"
                )

            token = auth.split(" ")[1]
            token_data = jwt_auth.decode_token(token)

            # Validate tenant ID matches token
            if token_data["tenant_id"] != tenant_id:
                raise HTTPException(status_code=403, detail="Tenant ID mismatch")

            # Add tenant and user context to request state
            request.state.tenant_id = tenant_id
            request.state.user = TokenData(
                user_id=token_data["sub"],
                tenant_id=token_data["tenant_id"],
                role=token_data["role"],
                email=token_data["email"],
            )

        except Exception as e:
            raise HTTPException(status_code=401, detail=str(e))

        response = await call_next(request)
        return response


async def get_tenant_id(request: Request) -> str:
    """Get tenant ID from request state."""
    return request.state.tenant_id


async def get_current_user(request: Request) -> TokenData:
    """Get current user from request state."""
    return request.state.user
