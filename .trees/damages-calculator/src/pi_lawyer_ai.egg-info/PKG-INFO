Metadata-Version: 2.4
Name: pi-lawyer-ai
Version: 0.1.0
Summary: PI Lawyer AI
Author-email: <PERSON> <j<PERSON><PERSON><PERSON><PERSON>@gmail.com>
License: Proprietary
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: diff-cover==9.2.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: coverage>=7.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Dynamic: requires-python

# AiLex: Comprehensive Multi-Practice Legal AI Platform

[![OWASP ZAP Scan](https://github.com/Jpkay/pi_lawyer_ai/actions/workflows/zap.yml/badge.svg)](https://github.com/Jpkay/pi_lawyer_ai/actions/workflows/zap.yml)
[![Security Score](https://img.shields.io/badge/Security%20Score-8.5%2F10-green)](docs/security/README.md)
[![Production Ready](https://img.shields.io/badge/Production-Ready-brightgreen)](#production-deployment-status)

## 🚀 Production Deployment Status

**✅ PRODUCTION READY** - All critical security requirements met (Auto-deployment working correctly)

### 🎉 Release v0.3.0 - Complete uv Adoption & Security Hardening
- **Complete uv adoption**: 10-100x faster Python dependency installation (Docker + CI)
- **Security baseline enforcement**: Active protection against NEW High/Critical findings
- **Optimized CI/CD**: Path-based filtering, concurrency controls, cost optimization

- **Security Score**: 8.5/10 (Excellent)
- **Critical Issues**: 0 (Production approved)
- **Authentication**: Enterprise-grade JWT with RBAC
- **Database Security**: pgAudit integration with comprehensive audit trails
- **Environment**: Production variables configured in Vercel

## 🔒 Security Implementation Overview

### ✅ **COMPLETED - Production Security Features**
All critical security vulnerabilities have been resolved:

1. **Client-Side JWT Parsing Elimination** - Secure server-side validation
2. **Service Role Key Security** - Environment variable protection
3. **JWT Secret Management** - Secure generation and validation
4. **Database Security Infrastructure** - pgAudit, security schema, device fingerprinting
5. **Authentication Security Framework** - RBAC, token tracking, suspicious activity detection
6. **Security Validation Framework** - Comprehensive config validation and security scoring

### 🔧 **NICE TO HAVE - Enhanced Security Features** (Optional)
These features enhance security but don't block production:

- **Rate Limiting Implementation Review** - Verify configuration across all endpoints
- **Input Validation Security Review** - Comprehensive audit of API endpoints
- **Session Management Enhancement** - Advanced session security features
- **Security Monitoring Dashboard** - Complete UI implementation for admin monitoring

### 🚀 **EXTRA - Advanced Security Features** (Future Phases)
Advanced features for future implementation:

- **Advanced Anomaly Detection** - ML-based authentication pattern analysis
- **Real-time Security Alerts** - External notification integration
- **Geo-location Validation** - Location-based authentication
- **Compliance Framework Implementation** - SOC2, GDPR, CCPA
- **AI Security & Privacy** - Prompt injection protection

## 📋 Table of Contents

1. [Route Categories & Protection Strategy](#1-route-categories--protection-strategy)
2. [Authentication Layers](#2-authentication-layers)
3. [Implementation Details](#3-implementation-details)
4. [Deadline Insights System](#4-deadline-insights-system)
5. [AI Agent Architecture](#5-ai-agent-architecture)
4. [Security Features](#4-security-features)
5. [Database Schema](#5-database-schema)
6. [API Protection](#6-api-protection)
7. [Client Components](#7-client-components)
8. [Supabase Integration](#8-supabase-integration)
9. [Environment Setup](#9-environment-setup)
10. [Testing Strategy](#10-testing-strategy)
11. [System Monitoring & Observability](#system-monitoring--observability)
12. [Troubleshooting](#11-troubleshooting)
13. [Documentation](#12-documentation)
14. [Contributing](#13-contributing)

## 1. Route Categories & Protection Strategy

### A. Public Routes (No Authentication Required)
- **Routes:**
  - `/` (landing page)
  - `/login`
  - `/register`
  - `/privacy-policy`, `/terms`, etc.
- **Protection:** None needed
- **Implementation:** Exclude from middleware

### B. Client Portal Routes (Client Authentication)
- **Routes:**
  - `/client-portal/*`
  - `/submit-matter`
- **Protection:** Middleware + Client Role Check
- **Implementation:** Middleware + Client Layout

### C. Staff Routes (Staff Authentication)
- **Routes:**
  - `/dashboard/*`
  - `/matters/*`
  - `/documents/*`
  - `/clients/*`
- **Protection:** Middleware + Staff Role Check
- **Implementation:** Middleware + Authenticated Layout

### D. Admin-Only Routes (Partner Authentication)
- **Routes:**
  - `/admin/*`
  - `/settings/*`
  - `/billing/*`
  - `/users/*`
- **Protection:** Middleware + Partner Role Check
- **Implementation:** Middleware + Role-specific checks

## 2. Authentication Layers

### Middleware Protection
- Checks user session
- Validates user role
- Redirects unauthorized access

### API Route Protection
- Uses `withAuth` wrapper
- Validates user roles
- Prevents unauthorized API access

### Database Row Level Security (RLS)
- Filters data by `tenant_id`
- Enforces role-based access
- Provides an additional security layer

## 3. User Roles

Supported roles:
- `partner`: Full administrative access
- `attorney`: Matter management, client interactions
- `paralegal`: Support tasks, document management
- `staff`: Limited administrative access
- `client`: Personal matter portal access

## 4. Security Best Practices

- Minimal JWT claims
- Multiple authentication layers
- Tenant-based data isolation
- Service role for admin operations
- Comprehensive error handling

## 5. Dynamic Terminology System (Matters vs Cases)

The application uses a dual-field matter model with dynamic terminology to provide contextually appropriate language for different types of legal work:

### Core Concept
- **All work is stored as "matters"** in the database (`tenants.matters` table)
- **Dynamic terminology** displays "Case" or "Matter" based on the type of work
- **Two orthogonal fields** define each matter:
  - `practice_area`: What the engagement is about (Personal Injury, Family Law, Criminal Defense, etc.)
  - `work_type`: How the work is handled (litigation, transactional, advisory, intellectual_property)

### Terminology Rules
- **"Case"**: Displayed for litigation work (`work_type = 'litigation'`)
- **"Matter"**: Displayed for non-litigation work (`work_type != 'litigation'`)

### Implementation Pattern
```typescript
import { getMatterDisplayLabel } from '@/lib/utils/matter-display'

// Get appropriate terminology
const displayLabel = getMatterDisplayLabel(matter.workType)
// Returns "Case" for litigation, "Matter" for non-litigation

// Use in UI
<h1>Create New {displayLabel}</h1>
```

### Practice Areas & Work Types
| Practice Area | Typical Work Type | Display Label |
|---------------|-------------------|---------------|
| Personal Injury & Medical Malpractice | litigation | Case |
| Criminal Defense | litigation | Case |
| Family Law | litigation | Case |
| Real Estate (Residential/Landlord-Tenant) | transactional | Matter |
| Estate Planning & Probate | advisory | Matter |
| Immigration Law | advisory | Matter |
| Bankruptcy | litigation | Case |

For comprehensive documentation, see [`docs/matter-vs-case-refactor/`](./docs/matter-vs-case-refactor/).

## 6. Database Schema Usage

### Tenant Data vs. Public Data

- **Tenant Data**: All tenant-specific tables (e.g., `matters`, `documents`, `clients`, `deadlines`, `assignments`, etc.) reside in the `tenants` schema.
  **Always use:**
  ```python
  .schema('tenants').from_('table_name')
  ```
  This ensures data isolation and prevents cross-tenant access.

- **Public Data**: Shared resources (e.g., `legal_templates`, reference tables) reside in the `public` schema (the default).
  **Always use:**
  ```python
  .from_('table_name')
  ```
  No explicit schema is needed for public data.

### Code Review & Contribution Guidelines

- All new queries for tenant data **must** specify `.schema('tenants')`.
- Code reviewers should reject PRs that do not follow this convention for tenant tables.
- Public/shared data should **not** specify the tenants schema.

### Example

```python
# Correct for tenant data
client.schema('tenants').from_('matters').select('*').eq('id', matter_id).execute()

# Correct for public data
client.from_('legal_templates').select('*').execute()
```

### Why is this Important?

- Prevents bugs where data is read/written to the wrong schema.
- Ensures Row Level Security (RLS) and tenant isolation are always enforced.
- Makes migrations and multi-environment deployments safer and more predictable.

## 6. Database Trigger: `sync_auth_users`

This trigger is crucial for maintaining consistency between the Supabase `auth.users` table and the application's `tenants.users` table.

**Purpose:**
- Automatically creates or updates a corresponding record in `tenants.users` whenever a user is created or updated in `auth.users` (e.g., during sign-up or login).
- Extracts `tenant_id` and `role` from `auth.users.raw_user_meta_data` and populates the respective columns in `tenants.users`.
- Updates `tenants.users.last_login` timestamp upon user login.

**Key Implementation Details & Troubleshooting Notes:**
- **`SECURITY DEFINER`:** The associated function `public.sync_auth_user()` runs with `SECURITY DEFINER`. This is essential because the trigger needs to modify the `tenants.users` table, potentially bypassing the Row Level Security (RLS) policies that would apply to the invoking user (the one logging in). Running as the function owner ensures it has the necessary permissions. *Initial troubleshooting showed RLS errors when this function ran with default `SECURITY INVOKER` privileges.*
- **Lookup Logic:** The function **must** look up existing records in `tenants.users` using `WHERE auth_user_id = NEW.id`. It cannot assume the primary key (`id`) of `auth.users` matches the primary key (`id`) of `tenants.users`. *Initial troubleshooting showed "duplicate key" errors on the `tenants.users.email` constraint because the function incorrectly tried to INSERT a user that already existed but was linked via `auth_user_id`.*
- **Linking:** The `tenants.users` table uses an `auth_user_id` column to link back to the corresponding `auth.users.id`.

Understanding this trigger is vital for debugging login issues or problems related to user data synchronization between the authentication system and the application's tenant data.

## 7. Authentication Architecture

### Authentication Components
| Component | File | Responsibility |
|-----------|------|----------------|
| **API Authentication** | `/src/lib/auth-helpers.ts` | Central mechanism for API route protection |
| **Security Context** | `/src/lib/security/context.tsx` | Device trust management and fingerprinting |
| **Supabase Client** | `/src/lib/supabase/client.ts` | Supabase initialization and session management |
| **Security Forensics** | `/src/lib/security/forensics.ts` | Security event logging and monitoring |
| **JWT Debugging** | `/src/lib/debug/jwtrole.ts` | Development tools for JWT issues |
| **Route Protection** | `/src/middleware.ts` | Route-level authentication and access control |

### Key Authentication Functions
- `requireAuth(allowedRoles?)`: Validate user session and role permissions
- `withAuth(handler, allowedRoles)`: Wrap API route handlers for authentication
- `createServiceClient()`: Create admin-level Supabase client for server operations

### Usage Example
```typescript
// In an API route
import { withAuth } from '@/lib/auth-helpers'
import { NextResponse } from 'next/server'

export const GET = withAuth(async (req, user) => {
  // User is already authenticated and role-checked
  // Access user.id, user.role, user.tenantId

  return NextResponse.json({ message: "Authenticated!" })
}, ['partner', 'attorney']) // Only these roles can access
```

## 8. Supabase SSR & Cookie Handling Strategy

### Authentication Client Creation
- **User Routes**: `createServerClientForUser(req)`
  - Uses Supabase Anon Key
  - Respects Row Level Security (RLS)
  - Synchronous cookie access via `req.cookies`

- **Service Routes**: `createServiceClient(req)`
  - Uses Supabase Service Role Key
  - Bypasses RLS for admin/backend operations
  - Synchronous cookie access via `req.cookies`

### Cookie Handling Approach
- **Synchronous Access**: Use `req.cookies.get(name)?.value`
- **Async Modifications**: Use `cookies().set()` and `cookies().delete()`
- **Consistent Across Routes**: Unified strategy for user and service routes

### Authentication Wrappers
- **`withAuth`**:
  - Protects user-facing routes
  - Validates user roles
  - Uses RLS-enabled client

- **`withServiceRole`**:
  - Protects admin/backend routes
  - Bypasses user authentication
  - Uses service role client

### Key Benefits
- Resolves Next.js and Supabase SSR cookie compatibility
- Provides secure, consistent authentication
- Supports granular access control
- Maintains separation between user and admin operations

### Security Considerations
- Always pass `NextRequest` to client creation methods
- Use service role clients sparingly and with caution
- Implement additional role-based checks as needed

## 9. Environment Setup

### Required Environment Variables
- `NEXT_PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_KEY`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### Local Development
1. Copy `.env.example` to `.env.local`
2. Fill in required environment variables
3. Run `npm install`
4. Start development server: `npm run dev`

## 10. Deployment Considerations

- Always use environment-specific configurations
- Rotate service account keys regularly
- Monitor authentication logs
- Implement additional monitoring for suspicious activities

## 11. Troubleshooting

### Common Authentication Issues
- Verify JWT claims
- Check role assignments
- Validate tenant configurations

### Debugging
- Use `createServiceClient()` for admin investigations
- Leverage Supabase logs
- Implement comprehensive logging

## 12. Documentation

### 🔒 Security Documentation
- [**Security Implementation Status**](/docs/SECURITY_IMPLEMENTATION_STATUS.md) ✅ **PRODUCTION READY**
- [Security Architecture](/docs/security/README.md)
- [Authentication Guide](/docs/authentication.md)
- [JWT Secret Management](/docs/security/jwt-secret-management.md)
- [Production Deployment Checklist](/PRODUCTION_DEPLOYMENT_CHECKLIST.md)
- [Security Implementation Report](/SECURITY-IMPLEMENTATION-REPORT.md)

### 🏗️ System Architecture
- [Authentication Architecture](/docs/authentication-architecture.md)
- [Document Processing Pipeline](/docs/document-processing.md)
- [CopilotKit Integration Guide](/docs/copilotkit-integration.md)
- [GCS Storage Architecture](/docs/gcs-storage-architecture.md)
- [Agent Development Guidelines](/docs/agent-development-guidelines.md)

### 🚀 Deployment & Operations
- [**MCP Rules Engine Production Readiness Guide**](/docs/PRODUCTION_READINESS.md) ✅
- [Deployment Guide](/DEPLOYMENT_GUIDE.md)
- [Development Guide](/DEVELOPMENT.md)
- [API Key Verification Report](/docs/API_KEY_VERIFICATION.md) ✅

### 📊 System Features
- [Deadline Extraction and Retrieval](./deadlines_extraction_readme.md)
- [Insights System Documentation](/INSIGHTS_SYSTEM_DOCUMENTATION.md)
- [Subscription System Audit](/docs/README-SUBS-AUDIT.md)

### 🌍 Compliance & Data Residency
- [**Data Residency Controls**](/docs/DATA_RESIDENCY_CONTROLS.md) ✅ **PRODUCTION READY**
- [Data Residency Deployment Guide](/docs/DATA_RESIDENCY_DEPLOYMENT_GUIDE.md)
- [Jurisdiction Monorepo Compatibility](/docs/JURISDICTION_MONOREPO_COMPATIBILITY.md)

## 13. Contributing

**Schema Enforcement Policy:**
- All Supabase queries for tenant data must use `.schema('tenants')`.
- This is a hard requirement for all code merged into main.
- Automated tests and code review will enforce this policy.

Please read our security guidelines before contributing to the authentication system.

## 14. Testing Authentication

### Authentication Route Testing

We've created a comprehensive test authentication route at `/api/test-auth` to verify different authentication scenarios:

#### Endpoints
- `GET /api/test-auth`: Accessible to all authenticated roles
  - Verifies basic authentication
  - Returns user information

- `POST /api/test-auth`: Partner-only endpoint
  - Tests role-based access control
  - Only accessible to users with 'partner' role

- `PUT /api/test-auth`: Staff roles endpoint
  - Checks access for staff-level roles
  - Accessible to partners, attorneys, paralegals, and staff

- `DELETE /api/test-auth`: Client-specific endpoint
  - Validates client-role access restrictions

#### Testing Procedure
1. Log in with different user roles
2. Send requests to each endpoint
3. Verify expected behavior:
   - Authorized users receive successful responses
   - Unauthorized users receive 401 Unauthorized errors

#### Example Test Scenarios
```bash
# Partner user
curl -X GET /api/test-auth     # Should succeed
curl -X POST /api/test-auth    # Should succeed
curl -X PUT /api/test-auth     # Should succeed
curl -X DELETE /api/test-auth  # Should fail

# Client user
curl -X GET /api/test-auth     # Should succeed
curl -X POST /api/test-auth    # Should fail
curl -X PUT /api/test-auth     # Should fail
curl -X DELETE /api/test-auth  # Should succeed
```

### Debugging Authentication
- Use browser network tools to inspect response headers
- Check server logs for detailed authentication errors
- Verify JWT claims and role assignments

## 15. Testing

For detailed testing information and guidance, please see the [TESTING.md](./TESTING.md) file.

## 16. Agents Overview

The AiLex system utilizes a sophisticated **LangGraph-based multi-agent architecture** with 8 fully implemented agents organized into two categories, supporting **7 comprehensive practice areas**:

**Supported Practice Areas:**
- **Personal Injury & Medical Malpractice** - Auto accidents, slip & fall, medical negligence
- **Criminal Defense** - Criminal charges, DUI, white collar crime
- **Family Law** - Divorce, custody, adoption, domestic relations
- **Estate Planning & Probate** - Wills, trusts, probate administration
- **Immigration Law** - Visa applications, citizenship, deportation defense
- **Real Estate** - Residential transactions, landlord-tenant disputes
- **Bankruptcy** - Chapter 7, Chapter 11, debt restructuring

### 🚀 **Interactive Agents** (`backend/agents/interactive/`)
*Quick response agents (< 5 seconds) for real-time user interactions*

- **Research Agent** ✅ - Legal research and case law search with hybrid retrieval pipeline
- **Intake Agent** ✅ - Multi-practice client intake (all 7 practice areas supported)
- **Calendar CRUD Agent** ✅ - Calendar and scheduling management with OAuth integration
- **Task CRUD Agent** ✅ - Task and deadline management with priority handling
- **Master Router** ✅ - Intelligent request routing and agent orchestration

### 🔍 **Insights Agents** (`backend/agents/insights/`)
*Long-running analysis agents for comprehensive data processing*

- **Document Agent** ✅ - Legal document generation with dual-template architecture
- **Deadline Insights Agent** ✅ - Deadline analysis, conflict detection, and risk assessment
- **Supervisor Agent** ✅ - Multi-agent coordination and workflow orchestration

### 🎯 **Matter Client Agent** (`backend/agents/matter_client/`)
*Specialized agent for matter-specific operations and client management*

### 🏗️ **Architecture Highlights**

- **Unified LangGraph Implementation** - All agents follow consistent patterns and interfaces
- **Tenant Isolation** - Complete multi-tenant security and data separation
- **Configuration Management** - Centralized agent configuration with superadmin controls
- **Comprehensive Testing** - 90%+ test coverage with unit, integration, and performance tests
- **Production Ready** - All agents deployed and operational in production environment

**For comprehensive documentation of all agents, their responsibilities, and implementation details, please refer to the [Agents Architecture Documentation](./docs/agents-architecture.md).**

### 4. Calendar Deadline Agent
- **Purpose**: Specifically focused on managing deadlines associated with calendar events.
- **Configuration**:
  - **Services**: Managed through `calendar-deadline-service.ts`, which handles the logic for deadlines.

### Analysis of Agent Organization
- **Consistency**: Each agent follows a consistent naming and configuration pattern, which is crucial for maintainability.
- **Separation of Concerns**: Each agent has a defined role and responsibility, simplifying the codebase.
- **Communication**: Agents communicate through defined API routes, ensuring seamless interaction between frontend components and backend services.

### Recommendations for Improvement
1. **Centralized Agent Management**: Consider creating a centralized directory or service for managing all agents.
2. **Testing and Validation**: Implement unit tests for each agent to ensure they handle various scenarios correctly.
3. **Documentation**: Enhance documentation for each agent, detailing their purpose, configuration, and usage patterns.
4. **Monitoring and Logging**: Set up monitoring for each agent to track performance and issues.

## 17. Rate Limiting and Resource Controls

The application implements a comprehensive rate limiting and resource control system to manage document uploads effectively. This ensures that tenants can only upload documents within their specified quotas and limits.

### Key Features:
- **Tenant Quotas**: Each tenant has specific limits on daily uploads, monthly uploads, maximum document size, and concurrent processing.
- **Rate Limit Middleware**: Applied to the document upload endpoint to check tenant limits before processing uploads.
- **Real-time Tracking**: Utilizes Redis for real-time counters to enforce limits immediately.
- **Admin Interface**: Accessible at `/admin/tenant-quotas`, where administrators can view and modify tenant quotas and monitor resource usage.

### Admin Interface for Quota Management
The admin interface for managing tenant quotas is available at `/admin/tenant-quotas`. This page allows administrators to:
- View current tenant quotas and resource usage.
- Modify resource limits for each tenant based on their subscription plans.
- Monitor overall system resource consumption through analytics.

### Usage Example
To check if a tenant can upload a document, the `RateLimitService` is used, which checks the tenant's current usage against their quotas and returns whether the upload is allowed or not.

## Advanced Features

### Proactive Activity Insights
For detailed information about our intelligent activity tracking and insights feature, please refer to [Proactive Activity Insights Documentation](/docs/proactive-activity-insights.md).

### System Monitoring & Observability
The application includes a comprehensive monitoring and observability system that provides:
- **Real-time Webhook Monitoring**: Track webhook delivery success rates, failures, and performance
- **System Health Dashboard**: Monitor API health, database connectivity, and Redis cache status
- **Automated Alerting**: Proactive alerts for system issues and performance degradation
- **Performance Metrics**: Comprehensive metrics collection with Prometheus integration
- **Distributed Tracing**: Request flow visibility across microservices with OpenTelemetry

#### Monitoring Features
- **Webhook Retry Queue**: Exponential backoff retry mechanism with dead letter queue
- **Health Check APIs**: Real-time system health validation and alert generation
- **Superadmin Dashboard**: Integrated monitoring interface at `/superadmin/monitoring`
- **Database Integration**: Surgical webhook monitoring table (`webhook_retry_queue`)
- **Alert Thresholds**: Configurable warning and critical alert levels

For detailed monitoring documentation, see:
- [Webhook Monitoring System](/docs/webhook-monitoring.md)
- [System Observability Guide](/docs/system-monitoring.md)

### Subscription Management System
The application includes a comprehensive subscription management system that supports:
- Flexible subscription plans with different tiers (Starter, Professional, Enterprise)
- Add-on modules for additional practice areas, states, and features
- 14-day free trials with extension capabilities
- Usage tracking and quota enforcement
- Notification system for trial expiration and quota limits

For detailed documentation on the subscription management system, see:
- [Subscription Management Documentation](/frontend/docs/SUBSCRIPTION_MANAGEMENT.md)

## Staging Environment

This project includes a dedicated staging environment backed by its own Supabase project, completely isolated from production.

- **Staging Project Ref**: `btwaueeckvylrlrnbvgt`
- **Link CLI**:
  ```bash
  supabase link --project-ref btwaueeckvylrlrnbvgt
  ```
- **Push migrations**:
  ```bash
  supabase db push --include-all
  ```
- **Environment Variables**: Use a separate `.env.staging` file with the same variables as production, pointing to staging:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - `SUPABASE_SERVICE_KEY`
  - (other env vars as needed)
- **Isolation**: Staging uses its own schemas (`public`, `tenants`, `security`) and will not affect production data.

## Troubleshooting: Auth.User Triggers

### Issue: Permission Denied for tenants Schema
- Upon user signup, the `sync_auth_user` trigger failed with the error:
  ```
  permission denied for schema tenants
  ```
- This occurred because the internal Supabase role (`supabase_auth_admin`) lacked access to the `tenants` schema.

### Solution:
1. Grant necessary permissions:
   ```sql
   GRANT USAGE ON SCHEMA tenants TO supabase_auth_admin;
   GRANT SELECT, INSERT, UPDATE ON TABLE tenants.users TO supabase_auth_admin;
   ```
2. Updated the `sync_auth_user` trigger function to support the 3-step flow:
   - No action on `INSERT` if `tenant_id` is `NULL`.
   - On `UPDATE` when `tenant_id` is set, insert or update `tenants.users`.
   - Robust null checks and safe UUID casting when extracting metadata.
3. Re-enabled other triggers (`on_auth_user_created`, `on_user_change`, `on_user_role_change`) after validation.

This ensures new users can sign up without errors and are synced to `tenants.users` once their `tenant_id` is available.

## Code Quality and Automated Checks

To maintain code consistency, prevent errors, and ensure a smoother development process, this project utilizes a multi-layered approach for code quality checks:

1.  **IDE Integration (Real-time Feedback):**
    *   **Tools:** ESLint, Prettier (via VS Code extensions or similar).
    *   **Function:** Provides immediate feedback on TypeScript errors, linting issues, and formatting problems directly within your code editor as you type or save files.

2.  **Manual Project-Wide Checks (NPM Scripts):**
    *   Located in `frontend/package.json`.
    *   `npm run lint`: Runs ESLint across the entire frontend project and attempts to automatically fix identified issues (`--fix`).
    *   `npm run type-check`: Runs the TypeScript compiler (`tsc --noEmit`) to check for type errors across the entire frontend project without generating JavaScript output. Useful for a full validation before major changes or commits.

3.  **Automated Pre-Commit Checks (Husky + lint-staged):**
    *   **Tools:** `husky`, `lint-staged`.
    *   **Function:** Before any `git commit` is finalized, this automatically runs `eslint --fix` *only* on the files that have been staged (`git add ...`).
    *   **Outcome:** If ESLint finds errors that cannot be fixed automatically, the commit process is **blocked**, forcing errors to be corrected before they enter the version history. This enforces incremental fixing.

4.  **Continuous Integration Checks (GitHub Actions):**
    *   **Configuration:** `.github/workflows/ci.yml`.
    *   **Function:** Automatically triggers on pushes or pull requests to the `main` and `develop` branches.
    *   **Process:** Runs on GitHub's servers, performs a clean setup, installs dependencies (`npm ci`), and executes `npm run lint` and `npm run type-check` for the frontend project.
    *   **Outcome:** Acts as a final validation step. If any checks fail, the workflow run is marked as failed in the GitHub Actions tab, providing a clear signal that the pushed code has issues.

## MCP Rules Engine Production Deployment

✅ **COMPLETED**: Automated Vercel to API Gateway cut-over successfully deployed.

### Production API Gateway
- **URL**: `https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev`
- **Health Check**: `GET /health` (public, no authentication)
- **MCP Endpoint**: `POST /mcp/run` (requires `x-api-key` header)
- **Status**: Active and processing legal deadline calculations

### Key Components
- **API Gateway**: Google Cloud API Gateway with proper backend authentication
- **Backend Service**: Cloud Run service `mcp-prod` in `texas-laws-personalinjury` project
- **Authentication**: API key-based with keys stored in Secret Manager
- **Monitoring**: Error rate and latency alerts configured

### Environment Variables
```bash
MCP_RULES_BASE=https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev
FEATURE_MCP_RULES_ENGINE=true
```

## 4. Deadline Insights System

### 🎯 **Overview**
The Deadline Insights System provides AI-powered deadline management with proactive notifications, intelligent recommendations, and automated insights generation.

### ✅ **Key Features**
- **Automated Deadline Analysis**: Periodic insights generation every 4 hours
- **Morning Briefings**: Daily personalized summaries at configurable times
- **Conflict Detection**: Identifies scheduling conflicts between deadlines
- **User Return Insights**: Personalized updates when users return after breaks
- **Multi-Channel Notifications**: Email, SMS, and in-app notifications
- **Intelligent Recommendations**: AI-generated deadline management suggestions

### 🏗️ **Architecture Components**

#### Backend Services
- **Celery Background Jobs**: Periodic insights generation and notifications
- **FastAPI Endpoints**: REST APIs for insights data and configuration
- **Notification Services**: Email (Resend) and SMS (Twilio) integration
- **Database Schema**: Dedicated tables for insights and configuration

#### Frontend Components
- **DeadlineInsightsPanel**: Main dashboard insights display
- **MorningBriefing**: Daily briefing component with personalized greetings
- **NotificationCenter**: Real-time notification management
- **Settings Interface**: User preference configuration

#### API Endpoints
```
GET /deadline-insights/summary          # Dashboard summary data
GET /deadline-insights/detailed         # Comprehensive insights
GET /deadline-insights/critical         # Critical deadlines
GET /deadline-insights/conflicts        # Deadline conflicts
GET /deadline-insights/morning-briefing # Daily briefing
POST /deadline-insights/trigger         # Manual insights generation

POST /jobs/user-return-insights         # User return insights
POST /jobs/deadline-alert               # Send deadline alerts
GET /jobs/status/{job_id}               # Job status tracking

GET /tenant/briefing-config/            # Tenant configuration
PUT /tenant/briefing-config/            # Update configuration
```

### 🔧 **Configuration**

#### Required Environment Variables
```bash
# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key
RESEND_FROM_EMAIL=<EMAIL>

# SMS Service (Twilio) - Optional
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

# Redis for Celery
REDIS_URL=redis://localhost:6379/0
```

#### Tenant Configuration Options
- **Morning Briefing Time**: Customizable time and timezone
- **Insight Frequency**: 1-24 hours between insights generation
- **User Return Threshold**: Hours away before triggering return insights
- **Notification Preferences**: Channel preferences and priority filters

### 📊 **Database Schema**
```sql
-- Main insights storage
CREATE TABLE tenants.deadline_insights (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    analysis_type VARCHAR(50),
    insights JSONB NOT NULL,
    generated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
);

-- Tenant configuration
CREATE TABLE tenants.briefing_config (
    tenant_id UUID PRIMARY KEY,
    morning_briefing JSONB,
    insight_frequency_hours INTEGER,
    auto_insights_enabled BOOLEAN,
    user_return_threshold_hours INTEGER
);
```

## 📊 Coverage & Gates

### **Coverage Reporting**

The project implements comprehensive test coverage reporting with gentle gates to prevent regressions while maintaining development velocity.

#### **Frontend Coverage (Vitest + V8)**
- **Provider**: V8 coverage provider via Vitest
- **Reports**: Text summary, LCOV, and HTML reports
- **Location**: `frontend/coverage/`
- **Thresholds**: 55% total coverage, 70% diff coverage

```bash
# Run frontend tests with coverage
cd frontend
pnpm test:coverage

# Check coverage gates
pnpm test:coverage:check
```

#### **Python Coverage (coverage.py + diff-cover)**
- **Provider**: coverage.py with pytest integration
- **Reports**: XML, HTML, and terminal reports
- **Location**: `backend/coverage.xml`, `backend/htmlcov/`
- **Thresholds**: 55% total coverage, 70% diff coverage

```bash
# Run Python tests with coverage
cd backend
uv run pytest --cov --cov-report=html --cov-report=xml

# Check coverage gates
python ../scripts/check-python-coverage.py
```

### **Coverage Gates**

Coverage gates are **path-aware** and only enforce thresholds when relevant code changes:

- **Frontend gate**: Only runs when `frontend/` files change
- **Python gate**: Only runs when `backend/` or `src/` files change
- **Diff coverage**: Only checks coverage on changed lines (70% threshold)
- **Total coverage**: Prevents overall regression (55% threshold)

#### **Gate Configuration**

Thresholds are configurable via environment variables:

```bash
# Frontend thresholds
FRONTEND_COVERAGE_TOTAL=55    # Total coverage threshold
FRONTEND_COVERAGE_DIFF=70     # Diff coverage threshold

# Python thresholds
PYTHON_COVERAGE_TOTAL=55      # Total coverage threshold
PYTHON_COVERAGE_DIFF=70       # Diff coverage threshold
```

#### **CI Integration**

Coverage gates are integrated into the CI pipeline with cost controls:

- **Selective execution**: Only runs when relevant paths change
- **Fast feedback**: Coverage checks complete in 2-3 minutes
- **Artifact upload**: HTML reports uploaded only on failure or stripe push
- **Non-blocking integration tests**: Integration coverage remains informational

#### **Improving Coverage**

To improve coverage when gates fail:

1. **Focus on changed files**: Diff coverage only checks your changes
2. **Prioritize critical paths**: Business logic and error handling
3. **Use detailed reports**: HTML reports show exact uncovered lines
4. **Incremental improvement**: 70% diff coverage allows gradual improvement

```bash
# Generate detailed coverage reports
cd frontend && pnpm test:coverage  # Creates frontend/coverage/index.html
cd backend && uv run pytest --cov --cov-report=html  # Creates backend/htmlcov/index.html
```

### 🚀 **Deployment Status**
- **Backend**: ✅ Implemented and tested
- **Frontend**: ✅ Dashboard integration complete
- **API Endpoints**: ✅ All endpoints implemented
- **Background Jobs**: ✅ Celery tasks configured
- **Database**: ⚠️ Migration pending (run `create_deadline_insights_tables.sql`)
- **Notifications**: ⚠️ Requires service configuration (Resend/Twilio)

### 📚 **Documentation**
- **Detailed Documentation**: [docs/DEADLINE_INSIGHTS_SYSTEM.md](docs/DEADLINE_INSIGHTS_SYSTEM.md)
- **API Reference**: See endpoint documentation above
- **Configuration Guide**: Environment variables and tenant settings

### 🔮 **Future Enhancements**
- **Advanced AI Insights**: Machine learning for deadline prediction
- **Mobile Push Notifications**: Native mobile app notifications
- **Analytics Dashboard**: Detailed insights analytics and reporting
- **Enhanced Calendar Features**: Advanced scheduling automation and AI-powered meeting optimization

For detailed deployment and troubleshooting information, see [API_GATEWAY_PROD.md](docs/API_GATEWAY_PROD.md).
# Trigger CI
# Trigger new CI run
# Test after account fix
