/**
 * Type definitions for speakeasy
 */

declare module 'speakeasy' {
  export interface GenerateSecretOptions {
    name?: string;
    length?: number;
    symbols?: boolean;
    otpauth_url?: boolean;
    issuer?: string;
  }

  export interface Secret {
    ascii: string;
    hex: string;
    base32: string;
    otpauth_url?: string;
  }

  export interface TotpVerifyOptions {
    secret: string;
    encoding?: 'ascii' | 'hex' | 'base32';
    token: string;
    window?: number;
    step?: number;
    time?: number;
    timestamp?: number;
    digits?: number;
    counter?: number;
    algorithm?: 'sha1' | 'sha256' | 'sha512';
  }

  export function generateSecret(options?: GenerateSecretOptions): Secret;

  export namespace totp {
    function generate(options: TotpVerifyOptions): string;
    function verify(options: TotpVerifyOptions): boolean;
  }

  export namespace hotp {
    function generate(options: TotpVerifyOptions): string;
    function verify(options: TotpVerifyOptions): boolean;
  }
}
