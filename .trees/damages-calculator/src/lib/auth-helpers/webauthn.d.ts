/**
 * Type definitions for WebAuthn
 */

declare module '@simplewebauthn/server' {
  export interface RegistrationOptionsParams {
    rpName: string;
    rpID: string;
    userID: string;
    userName: string;
    userDisplayName?: string;
    timeout?: number;
    attestationType?: 'none' | 'indirect' | 'direct';
    excludeCredentials?: {
      id: string;
      type: 'public-key';
      transports?: ('usb' | 'nfc' | 'ble' | 'internal')[];
    }[];
    authenticatorSelection?: {
      authenticatorAttachment?: 'platform' | 'cross-platform';
      requireResidentKey?: boolean;
      residentKey?: 'discouraged' | 'preferred' | 'required';
      userVerification?: 'discouraged' | 'preferred' | 'required';
    };
    supportedAlgorithmIDs?: number[];
  }

  export interface RegistrationOptions {
    challenge: string;
    rp: {
      name: string;
      id: string;
    };
    user: {
      id: string;
      name: string;
      displayName: string;
    };
    pubKeyCredParams: {
      type: 'public-key';
      alg: number;
    }[];
    timeout?: number;
    excludeCredentials?: {
      id: string;
      type: 'public-key';
      transports?: ('usb' | 'nfc' | 'ble' | 'internal')[];
    }[];
    authenticatorSelection?: {
      authenticatorAttachment?: 'platform' | 'cross-platform';
      requireResidentKey?: boolean;
      residentKey?: 'discouraged' | 'preferred' | 'required';
      userVerification?: 'discouraged' | 'preferred' | 'required';
    };
    attestation?: 'none' | 'indirect' | 'direct';
    extensions?: {
      credProps?: boolean;
    };
  }

  export interface VerificationResult {
    verified: boolean;
    registrationInfo?: {
      fmt: string;
      counter: number;
      aaguid: string;
      credentialID: string;
      credentialPublicKey: string;
      credentialType: string;
      attestationObject: string;
      userVerified: boolean;
    };
    authenticationInfo?: {
      credentialID: string;
      newCounter: number;
      userVerified: boolean;
    };
  }

  export function generateRegistrationOptions(
    options: RegistrationOptionsParams
  ): Promise<RegistrationOptions>;

  export function verifyRegistrationResponse(options: any): Promise<VerificationResult>;

  export function generateAuthenticationOptions(options: any): Promise<any>;

  export function verifyAuthenticationResponse(options: any): Promise<VerificationResult>;
}
