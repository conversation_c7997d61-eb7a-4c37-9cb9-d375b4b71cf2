/**
 * Performance monitoring types
 */

/**
 * Performance metrics for API requests and responses
 */
export interface PerformanceMetrics {
  // Request identification
  requestId: string;
  
  // Timing information
  startTime: number;
  endTime?: number;
  duration?: number;
  
  // Request metadata
  url?: string;
  method?: string;
  route?: string;
  agent?: string;
  
  // Response information
  status?: number;
  statusText?: string;
  
  // Error tracking
  error?: boolean;
  errorType?: string;
  errorMessage?: string;
  
  // Streaming metrics
  streaming?: boolean;
  chunkCount?: number;
  firstChunkTime?: number;
  lastChunkTime?: number;
  
  // Retry information
  retry?: boolean;
  retryCount?: number;
  retryDelay?: number;
  
  // Fallback information
  fallback?: boolean;
  fallbackSource?: string;
  
  // Cache information
  cached?: boolean;
  cacheHit?: boolean;
  cacheTtl?: number;
  
  // Context information
  context?: Record<string, any>;
  
  // User information (anonymized)
  userHash?: string;
  tenantHash?: string;
  
  // Resource usage
  tokenCount?: number;
  promptTokens?: number;
  completionTokens?: number;
  
  // Model information
  model?: string;
  modelVersion?: string;
  
  // Custom metrics
  custom?: Record<string, any>;
}

/**
 * Interface for performance monitor
 */
export interface PerformanceMonitor {
  // Start tracking a request
  startRequest(requestId: string, initialMetrics?: Partial<PerformanceMetrics>): void;
  
  // Update metrics for a request
  updateMetrics(requestId: string, metrics: Partial<PerformanceMetrics>): void;
  
  // End tracking a request
  endRequest(requestId: string, finalMetrics?: Partial<PerformanceMetrics>): PerformanceMetrics;
  
  // Get metrics for a request
  getMetrics(requestId: string): PerformanceMetrics | undefined;
  
  // Get all metrics
  getAllMetrics(): Record<string, PerformanceMetrics>;
  
  // Clear metrics
  clearMetrics(olderThan?: number): void;
}

/**
 * Options for creating a performance monitor
 */
export interface PerformanceMonitorOptions {
  // Maximum number of requests to track
  maxRequests?: number;
  
  // Time to keep metrics in memory (ms)
  ttl?: number;
  
  // Whether to automatically clean up old metrics
  autoCleanup?: boolean;
  
  // Cleanup interval (ms)
  cleanupInterval?: number;
  
  // Whether to log metrics
  logging?: boolean;
  
  // Log level
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  
  // Whether to report metrics to an external service
  reporting?: boolean;
  
  // Reporting endpoint
  reportingEndpoint?: string;
  
  // Reporting interval (ms)
  reportingInterval?: number;
}

/**
 * Retry policy for API requests
 */
export interface RetryPolicy {
  // Whether to retry the request
  retry: boolean;
  
  // Maximum number of retries
  maxRetries?: number;
  
  // Base delay between retries (ms)
  baseDelay?: number;
  
  // Maximum delay between retries (ms)
  maxDelay?: number;
  
  // Whether to use exponential backoff
  exponential?: boolean;
  
  // Jitter factor (0-1)
  jitter?: number;
  
  // Whether to retry on specific error types
  retryOnErrorTypes?: string[];
  
  // Whether to retry on specific status codes
  retryOnStatusCodes?: number[];
}
