/**
 * Error types and classes for the application
 */

/**
 * Enum for error types
 */
export enum ErrorType {
  // General errors
  UNKNOWN = 'unknown',
  INTERNAL_SERVER_ERROR = 'internal_server_error',
  INVALID_REQUEST = 'invalid_request',
  
  // Authentication errors
  UNAUTHORIZED = 'unauthorized',
  FORBIDDEN = 'forbidden',
  ACCESS_DENIED = 'access_denied',
  
  // Network errors
  NETWORK_ERROR = 'network_error',
  NETWORK_OFFLINE = 'network_offline',
  TIMEOUT = 'timeout',
  
  // Resource errors
  NOT_FOUND = 'not_found',
  RESOURCE_EXHAUSTED = 'resource_exhausted',
  
  // API errors
  API_ERROR = 'api_error',
  RATE_LIMITED = 'rate_limited',
  
  // Data errors
  VALIDATION_ERROR = 'validation_error',
  DATA_CORRUPTION = 'data_corruption',
  
  // LLM errors
  LLM_ERROR = 'llm_error',
  CONTENT_FILTER = 'content_filter',
  
  // Tenant errors
  TENANT_NOT_FOUND = 'tenant_not_found',
  TENANT_SUSPENDED = 'tenant_suspended',
  
  // Subscription errors
  SUBSCRIPTION_ERROR = 'subscription_error',
  QUOTA_EXCEEDED = 'quota_exceeded',
  
  // Security errors
  SECURITY_ERROR = 'security_error',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity'
}

/**
 * Error codes for more specific error categorization
 */
export enum AGErrorCodes {
  // General errors
  UNKNOWN_ERROR = 'unknown_error',
  INTERNAL_ERROR = 'internal_error',
  
  // API errors
  API_ERROR = 'api_error',
  API_TIMEOUT = 'api_timeout',
  API_RATE_LIMIT = 'api_rate_limit',
  
  // Authentication errors
  AUTH_ERROR = 'auth_error',
  TOKEN_EXPIRED = 'token_expired',
  INVALID_TOKEN = 'invalid_token',
  
  // Permission errors
  PERMISSION_DENIED = 'permission_denied',
  
  // Resource errors
  RESOURCE_NOT_FOUND = 'resource_not_found',
  
  // Network errors
  NETWORK_ERROR = 'network_error',
  
  // Data errors
  VALIDATION_ERROR = 'validation_error',
  
  // LLM errors
  LLM_ERROR = 'llm_error',
  CONTENT_FILTER = 'content_filter',
  
  // Tenant errors
  TENANT_ERROR = 'tenant_error',
  
  // Subscription errors
  SUBSCRIPTION_ERROR = 'subscription_error',
  QUOTA_EXCEEDED = 'quota_exceeded'
}

/**
 * Enum for error severity levels
 */
export enum ErrorSeverity {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * Interface for AG error type
 */
export interface AGError extends Error {
  type: ErrorType;
  code?: string;
  severity: ErrorSeverity;
  status?: number;
  retryable: boolean;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Implementation of the AGError interface
 */
export class AGErrorImpl extends Error implements AGError {
  type: ErrorType;
  code?: string;
  severity: ErrorSeverity;
  status: number;
  retryable: boolean;
  timestamp: number;
  metadata?: Record<string, any>;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    options: {
      code?: string;
      severity?: ErrorSeverity;
      status?: number;
      retryable?: boolean;
      metadata?: Record<string, any>;
    } = {}
  ) {
    super(message);
    this.name = 'AGError';
    this.type = type;
    this.code = options.code;
    this.severity = options.severity || ErrorSeverity.ERROR;
    this.status = options.status || 500;
    this.retryable = options.retryable !== undefined ? options.retryable : this.isRetryableType(type);
    this.timestamp = Date.now();
    this.metadata = options.metadata;
    
    // Ensure the prototype chain is properly maintained
    Object.setPrototypeOf(this, AGErrorImpl.prototype);
  }

  /**
   * Determine if an error type is retryable by default
   */
  private isRetryableType(type: ErrorType): boolean {
    const retryableTypes = [
      ErrorType.NETWORK_ERROR,
      ErrorType.NETWORK_OFFLINE,
      ErrorType.TIMEOUT,
      ErrorType.RATE_LIMITED,
      ErrorType.INTERNAL_SERVER_ERROR
    ];
    
    return retryableTypes.includes(type);
  }

  /**
   * Convert to a plain object for serialization
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      code: this.code,
      severity: this.severity,
      status: this.status,
      retryable: this.retryable,
      timestamp: this.timestamp,
      metadata: this.metadata
    };
  }

  /**
   * Create an AGError from an unknown error
   */
  static fromError(error: unknown): AGError {
    if (error instanceof AGErrorImpl) {
      return error;
    }
    
    if (error instanceof Error) {
      return new AGErrorImpl(error.message, ErrorType.UNKNOWN, {
        metadata: {
          originalError: {
            name: error.name,
            stack: error.stack
          }
        }
      });
    }
    
    return new AGErrorImpl(
      typeof error === 'string' ? error : 'Unknown error occurred',
      ErrorType.UNKNOWN,
      {
        metadata: { originalError: error }
      }
    );
  }
}

// Export AGErrorImpl as AGError for backward compatibility
export const AGError = AGErrorImpl;
