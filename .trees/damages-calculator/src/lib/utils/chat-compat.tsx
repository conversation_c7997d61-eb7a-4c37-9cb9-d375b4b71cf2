import React, { useState, useEffect } from 'react';
import { CopilotChat as OriginalCopilotChat } from '@copilotkit/react-ui';
import { useCopilotChat } from '@copilotkit/react-core';
import { isAGUIEnabled, getAGUIVersion, isAGUIFeatureEnabled } from '@/lib/features/ag-ui';
import { generateAgentThreadId, generateSessionThreadId } from '@/lib/utils/thread-id';
import { useAuth } from '@/lib/auth/useAuth';
import { Session } from '@supabase/supabase-js';
import { WithErrorBoundary } from '@/components/copilot/error-boundary';

// Common props for both legacy and AG-UI chat implementations
export interface CommonChatProps {
  className?: string;
  displayName?: string;
  placeholder?: string;
  suggestions?: string[];
  context?: Record<string, any>;
  initialMessage?: string;
  onError?: (error: Error) => void;
  agent?: string;
  children?: React.ReactNode;
}

// Wrapper component that handles compatibility between legacy and AG-UI
export function CompatCopilotChat(props: CommonChatProps) {
  const { session } = useAuth();
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Get user and org IDs with fallbacks for when not authenticated
  const userId = session?.user?.id || 'anonymous';
  // For organization ID, we need to access user metadata since organization isn't directly on Session
  const orgId = session?.user?.user_metadata?.organization_id || 'default';

  // Generate a deterministic thread ID for tenant isolation if AG-UI is enabled
  const threadId = isAGUIEnabled()
    ? generateAgentThreadId(
        orgId,
        userId,
        props.agent || 'default' // Use the agent name as context differentiator
      )
    : undefined;

  // Handle errors
  const handleError = (err: Error) => {
    console.error('CopilotChat error:', err);
    setError(err);
    setIsLoading(false);
    if (props.onError) {
      props.onError(err);
    }
  };

  // Handle loading state
  useEffect(() => {
    // Reset error when props change
    setError(null);
  }, [props.agent, props.context]);

  // Create AG-UI specific props
  const agUIProps = isAGUIEnabled() ? {
    threadId: threadId,
    version: getAGUIVersion(),
    streaming: isAGUIFeatureEnabled('streaming'),
    // Add any other AG-UI specific props here
  } : {};

  // Apply compatibility props
  const compatProps = {
    ...props,
    ...agUIProps,
    onError: handleError,
  };

  // Show error state if there's an error
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 bg-red-50 text-red-800 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Error</h3>
        <p className="text-sm mb-4">{error.message}</p>
        <button
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          onClick={() => setError(null)}
        >
          Retry
        </button>
      </div>
    );
  }

  // Wrap in error boundary for additional safety
  return (
    <WithErrorBoundary fallback={<div>Something went wrong with the chat component</div>} onError={handleError}>
      <OriginalCopilotChat {...compatProps as any} />
    </WithErrorBoundary>
  );
}

// Hook that abstracts usage of the useCopilotChat hook for AG-UI compatibility
export function useCompatCopilotChat(options: any = {}) {
  const { session } = useAuth();
  const [error, setError] = useState<Error | null>(null);

  // Get user and org IDs with fallbacks for when not authenticated
  const userId = session?.user?.id || 'anonymous';
  // For organization ID, we need to access user metadata since organization isn't directly on Session
  const orgId = session?.user?.user_metadata?.organization_id || 'default';

  // Generate a deterministic thread ID if AG-UI is enabled
  const threadId = isAGUIEnabled()
    ? generateAgentThreadId(
        orgId,
        userId,
        options.agent || 'default'
      )
    : undefined;

  // Handle errors
  const handleError = (err: Error) => {
    console.error('CopilotChat hook error:', err);
    setError(err);
    if (options.onError) {
      options.onError(err);
    }
  };

  // Reset error when options change
  useEffect(() => {
    setError(null);
  }, [options.agent, options.context]);

  // Create the appropriate options for the hook
  const compatOptions = {
    ...options,
    ...(isAGUIEnabled() ? {
      threadId,
      version: getAGUIVersion(),
      streaming: isAGUIFeatureEnabled('streaming'),
    } : {}),
    onError: handleError,
  };

  // Use the CopilotKit hook with compat options
  const hookResult = useCopilotChat(compatOptions);

  // Add error to the result
  return {
    ...hookResult,
    error,
  };
}
