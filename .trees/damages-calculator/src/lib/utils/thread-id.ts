import { createHash } from 'crypto';

/**
 * Generate a deterministic thread ID for a user session
 *
 * @param userId - User ID or anonymous identifier
 * @param sessionId - Session ID or other unique identifier
 * @returns A deterministic thread ID
 */
export function generateSessionThreadId(userId: string, sessionId: string): string {
  // Create a deterministic thread ID by hashing the user ID and session ID
  const hash = createHash('sha256');
  hash.update(`${userId}:${sessionId}`);
  return hash.digest('hex').substring(0, 32);
}

/**
 * Generate a deterministic thread ID for an agent
 *
 * @param tenantId - Tenant ID (organization)
 * @param userId - User ID
 * @param agentId - Agent ID or name
 * @returns A deterministic thread ID
 */
export function generateAgentThreadId(tenantId: string, userId: string, agentId: string): string {
  // Create a deterministic thread ID by hashing the tenant ID, user ID, and agent ID
  const hash = createHash('sha256');
  hash.update(`${tenantId}:${userId}:${agentId}`);
  return hash.digest('hex').substring(0, 32);
}

/**
 * Generate a deterministic thread ID for a document
 *
 * @param tenantId - Tenant ID (organization)
 * @param documentId - Document ID
 * @returns A deterministic thread ID
 */
export function generateDocumentThreadId(tenantId: string, documentId: string): string {
  // Create a deterministic thread ID by hashing the tenant ID and document ID
  const hash = createHash('sha256');
  hash.update(`${tenantId}:document:${documentId}`);
  return hash.digest('hex').substring(0, 32);
}

/**
 * Generate a deterministic thread ID for a client
 *
 * @param tenantId - Tenant ID (organization)
 * @param clientId - Client ID
 * @returns A deterministic thread ID
 */
export function generateClientThreadId(tenantId: string, clientId: string): string {
  // Create a deterministic thread ID by hashing the tenant ID and client ID
  const hash = createHash('sha256');
  hash.update(`${tenantId}:client:${clientId}`);
  return hash.digest('hex').substring(0, 32);
}

/**
 * Generate a deterministic thread ID
 * This is a legacy function that is kept for backward compatibility
 *
 * @param tenantId - Tenant ID (organization)
 * @param userId - User ID
 * @param agentId - Optional agent ID
 * @returns A deterministic thread ID
 * @deprecated Use generateAgentThreadId instead
 */
export function generateThreadId(tenantId: string, userId: string, agentId?: string): string {
  // Create a deterministic thread ID by hashing the tenant ID, user ID, and optional agent ID
  const hash = createHash('sha256');
  if (agentId) {
    hash.update(`${tenantId}:${userId}:${agentId}`);
  } else {
    hash.update(`${tenantId}:${userId}`);
  }
  return hash.digest('hex').substring(0, 32);
}
