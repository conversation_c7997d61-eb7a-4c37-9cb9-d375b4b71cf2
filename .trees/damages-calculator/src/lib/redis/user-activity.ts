import { getRedisClient } from './redis-client';
import type Redis from 'ioredis';

// Constants
const USER_ACTIVITY_PREFIX = 'user:activity:';
const USER_LAST_SEEN_PREFIX = 'user:last_seen:';
const USER_SESSION_PREFIX = 'user:session:';
const DEFAULT_TTL = 60 * 60 * 24 * 7; // 7 days

/**
 * Record user activity
 * 
 * @param userId - User ID
 * @param action - Action performed
 * @param metadata - Additional metadata
 * @returns True if successful, false otherwise
 */
export async function recordUserActivity(
  userId: string,
  action: string,
  metadata: Record<string, any> = {}
): Promise<boolean> {
  const redis = getRedisClient();
  
  if (!redis) {
    console.warn('Redis client not available, skipping activity recording');
    return false;
  }
  
  try {
    const timestamp = Date.now();
    const activityKey = `${USER_ACTIVITY_PREFIX}${userId}`;
    const lastSeenKey = `${USER_LAST_SEEN_PREFIX}${userId}`;
    
    // Record the activity
    const activity = {
      action,
      timestamp,
      ...metadata
    };
    
    // Add to the activity list (limited to 100 items)
    await redis.lpush(activityKey, JSON.stringify(activity));
    await redis.ltrim(activityKey, 0, 99);
    
    // Set TTL on the activity list
    await redis.expire(activityKey, DEFAULT_TTL);
    
    // Update last seen
    await redis.set(lastSeenKey, timestamp.toString());
    await redis.expire(lastSeenKey, DEFAULT_TTL);
    
    return true;
  } catch (error) {
    console.error('Failed to record user activity:', error);
    return false;
  }
}

/**
 * Get user activity
 * 
 * @param userId - User ID
 * @param limit - Maximum number of activities to return
 * @returns Array of user activities
 */
export async function getUserActivity(
  userId: string,
  limit = 20
): Promise<Array<Record<string, any>>> {
  const redis = getRedisClient();
  
  if (!redis) {
    console.warn('Redis client not available, returning empty activity list');
    return [];
  }
  
  try {
    const activityKey = `${USER_ACTIVITY_PREFIX}${userId}`;
    
    // Get activities
    const activities = await redis.lrange(activityKey, 0, limit - 1);
    
    // Parse JSON
    return activities.map((activity) => JSON.parse(activity));
  } catch (error) {
    console.error('Failed to get user activity:', error);
    return [];
  }
}

/**
 * Get user last seen timestamp
 * 
 * @param userId - User ID
 * @returns Last seen timestamp or null if not found
 */
export async function getUserLastSeen(userId: string): Promise<number | null> {
  const redis = getRedisClient();
  
  if (!redis) {
    console.warn('Redis client not available, returning null for last seen');
    return null;
  }
  
  try {
    const lastSeenKey = `${USER_LAST_SEEN_PREFIX}${userId}`;
    
    // Get last seen
    const lastSeen = await redis.get(lastSeenKey);
    
    return lastSeen ? parseInt(lastSeen, 10) : null;
  } catch (error) {
    console.error('Failed to get user last seen:', error);
    return null;
  }
}

/**
 * Get active users
 * 
 * @param timeframe - Timeframe in milliseconds (default: 24 hours)
 * @returns Array of active user IDs
 */
export async function getActiveUsers(
  timeframe = 1000 * 60 * 60 * 24
): Promise<string[]> {
  const redis = getRedisClient();
  
  if (!redis) {
    console.warn('Redis client not available, returning empty active users list');
    return [];
  }
  
  try {
    // Get all last seen keys
    const keys = await redis.keys(`${USER_LAST_SEEN_PREFIX}*`);
    const now = Date.now();
    const activeUsers: string[] = [];
    
    // Check each key
    const values = await redis.mget(keys);
    
    keys.forEach((key: string, index: number) => {
      const lastSeen = parseInt(values[index] || '0', 10);
      
      if (now - lastSeen <= timeframe) {
        // Extract user ID from key
        const userId = key.replace(USER_LAST_SEEN_PREFIX, '');
        activeUsers.push(userId);
      }
    });
    
    return activeUsers;
  } catch (error) {
    console.error('Failed to get active users:', error);
    return [];
  }
}
