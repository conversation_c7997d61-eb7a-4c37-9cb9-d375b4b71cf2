import Redis from 'ioredis';

// Redis client singleton
let redisClient: Redis | null = null;

/**
 * Get the Redis client instance
 * Creates a new client if one doesn't exist
 * 
 * @returns Redis client instance
 */
export function getRedisClient(): Redis | null {
  if (!redisClient) {
    const redisUrl = process.env.REDIS_URL;
    
    if (!redisUrl) {
      console.warn('REDIS_URL not set, Redis functionality will be disabled');
      return null;
    }
    
    try {
      redisClient = new Redis(redisUrl);
      
      // Set up error handling
      redisClient.on('error', (err) => {
        console.error('Redis connection error:', err);
      });
      
      // Set up reconnection handling
      redisClient.on('reconnecting', () => {
        console.log('Redis reconnecting...');
      });
      
      redisClient.on('connect', () => {
        console.log('Redis connected');
      });
    } catch (error) {
      console.error('Failed to initialize Redis client:', error);
      return null;
    }
  }
  
  return redisClient;
}

/**
 * Close the Redis client connection
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
}

/**
 * Check if Redis is available
 * 
 * @returns True if Redis is available, false otherwise
 */
export async function isRedisAvailable(): Promise<boolean> {
  const client = getRedisClient();
  
  if (!client) {
    return false;
  }
  
  try {
    // Try to ping Redis
    const result = await client.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis availability check failed:', error);
    return false;
  }
}
