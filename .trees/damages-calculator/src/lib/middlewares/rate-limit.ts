import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '../redis/redis-client';
import { ErrorType, AGErrorImpl } from '../error-handling/error-types';

// Constants
const RATE_LIMIT_PREFIX = 'rate-limit:';
const DEFAULT_LIMIT = 100; // requests
const DEFAULT_WINDOW = 60; // seconds
const DEFAULT_BLOCK_DURATION = 300; // seconds

/**
 * Rate limiting middleware for Next.js API routes
 * 
 * @param req - Next.js request
 * @param options - Rate limiting options
 * @returns Next.js response or undefined to continue
 */
export async function rateLimitMiddleware(
  req: NextRequest,
  options: {
    limit?: number;
    window?: number;
    blockDuration?: number;
    identifier?: (req: NextRequest) => string;
    keyPrefix?: string;
    skipSuccessIncrement?: boolean;
  } = {}
): Promise<NextResponse | undefined> {
  const {
    limit = DEFAULT_LIMIT,
    window = DEFAULT_WINDOW,
    blockDuration = DEFAULT_BLOCK_DURATION,
    identifier = defaultIdentifier,
    keyPrefix = RATE_LIMIT_PREFIX,
    skipSuccessIncrement = false
  } = options;
  
  const redis = getRedisClient();
  
  if (!redis) {
    // Redis not available, skip rate limiting
    console.warn('Redis not available, skipping rate limiting');
    return undefined;
  }
  
  // Get identifier for this request
  const id = identifier(req);
  const key = `${keyPrefix}${id}`;
  const blockKey = `${key}:blocked`;
  
  try {
    // Check if the client is blocked
    const isBlocked = await redis.get(blockKey);
    
    if (isBlocked) {
      const ttl = await redis.ttl(blockKey);
      
      return NextResponse.json(
        {
          error: 'Too many requests',
          message: `Rate limit exceeded. Try again in ${ttl} seconds.`,
          retryAfter: ttl
        },
        {
          status: 429,
          headers: {
            'Retry-After': String(ttl),
            'X-RateLimit-Limit': String(limit),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': String(Math.floor(Date.now() / 1000) + ttl)
          }
        }
      );
    }
    
    // Get current count
    const count = await redis.incr(key);
    
    // Set expiration if this is the first request in the window
    if (count === 1) {
      await redis.expire(key, window);
    }
    
    // Get TTL for the current window
    const ttl = await redis.ttl(key);
    
    // Set headers for rate limit info
    const remaining = Math.max(0, limit - count);
    const headers = {
      'X-RateLimit-Limit': String(limit),
      'X-RateLimit-Remaining': String(remaining),
      'X-RateLimit-Reset': String(Math.floor(Date.now() / 1000) + ttl)
    };
    
    // Check if rate limit exceeded
    if (count > limit) {
      // Block the client for the specified duration
      await redis.set(blockKey, '1', 'EX', blockDuration);
      
      return NextResponse.json(
        {
          error: 'Too many requests',
          message: `Rate limit exceeded. Try again in ${blockDuration} seconds.`,
          retryAfter: blockDuration
        },
        {
          status: 429,
          headers: {
            ...headers,
            'Retry-After': String(blockDuration)
          }
        }
      );
    }
    
    // Continue to the API route
    // The response will be modified to include rate limit headers
    return undefined;
  } catch (error) {
    console.error('Rate limiting error:', error);
    
    // Continue to the API route if rate limiting fails
    return undefined;
  }
}

/**
 * Default identifier function that uses IP address
 * 
 * @param req - Next.js request
 * @returns Identifier string
 */
function defaultIdentifier(req: NextRequest): string {
  // Get IP address
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
  
  // Get path
  const path = req.nextUrl.pathname;
  
  return `${ip}:${path}`;
}

/**
 * Higher-order function that wraps an API route handler with rate limiting
 * 
 * @param handler - API route handler
 * @param options - Rate limiting options
 * @returns Wrapped API route handler
 */
export function withRateLimit(
  handler: (req: NextRequest) => Promise<NextResponse> | NextResponse,
  options: {
    limit?: number;
    window?: number;
    blockDuration?: number;
    identifier?: (req: NextRequest) => string;
    keyPrefix?: string;
    skipSuccessIncrement?: boolean;
  } = {}
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Apply rate limiting
      const rateLimitResponse = await rateLimitMiddleware(req, options);
      
      if (rateLimitResponse) {
        return rateLimitResponse;
      }
      
      // Call the original handler
      const response = await handler(req);
      
      // Add rate limit headers to the response
      // This would require modifying the response headers
      
      return response;
    } catch (error) {
      console.error('API route error:', error);
      
      if (error instanceof AGErrorImpl) {
        return NextResponse.json(
          {
            error: error.type,
            message: error.message
          },
          { status: error.status || 500 }
        );
      }
      
      return NextResponse.json(
        {
          error: ErrorType.INTERNAL_SERVER_ERROR,
          message: 'An unexpected error occurred'
        },
        { status: 500 }
      );
    }
  };
}
