/**
 * Type definitions for Supabase provider
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from './database.types';

export interface SupabaseContext {
  supabase: SupabaseClient<Database>;
  session: any;
}

export function useSupabase(): SupabaseContext;

export interface SupabaseProviderProps {
  children: React.ReactNode;
}

export function SupabaseProvider(props: SupabaseProviderProps): JSX.Element;
