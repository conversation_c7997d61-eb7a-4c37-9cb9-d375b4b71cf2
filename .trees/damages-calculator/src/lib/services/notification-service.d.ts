/**
 * Type definitions for NotificationService
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

declare global {
  class NotificationService {
    constructor(supabase: SupabaseClient<Database>);

    sendEmail(options: {
      to: string;
      subject: string;
      body: string;
      template?: string;
      data?: Record<string, any>;
    }): Promise<boolean>;

    sendSMS(options: {
      to: string;
      message: string;
    }): Promise<boolean>;

    createInAppNotification(options: {
      userId: string;
      tenantId?: string;
      title: string;
      message: string;
      type?: string;
      link?: string;
      data?: Record<string, any>;
    }): Promise<any>;

    markAsRead(notificationId: string): Promise<boolean>;

    getUnreadCount(userId: string): Promise<number>;

    getNotifications(userId: string, options?: {
      limit?: number;
      offset?: number;
      includeRead?: boolean;
    }): Promise<any[]>;
  }

  class NotificationSchedulerService {
    constructor(supabase: SupabaseClient<Database>);

    scheduleNotification(options: {
      userId: string;
      tenantId?: string;
      title: string;
      message: string;
      type?: string;
      channels?: ('email' | 'sms' | 'in_app')[];
      scheduledFor: Date | string;
      data?: Record<string, any>;
    }): Promise<any>;

    cancelScheduledNotification(notificationId: string): Promise<boolean>;

    getScheduledNotifications(userId: string): Promise<any[]>;
  }
}
