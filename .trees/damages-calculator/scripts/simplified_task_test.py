#!/usr/bin/env python3
"""
Simplified test script for task embeddings without dependencies.
"""

import asyncio
import os
import sys
import json
from datetime import datetime, timedelta
import logging
from pinecone import Pinecone, ServerlessSpec
from langchain_community.embeddings import OpenAIEmbeddings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv

load_dotenv()


# Define a simplified PineconeClient for testing
class PineconeClient:
    def __init__(self, api_key, environment, index_name, tenant_id=None):
        self.api_key = api_key
        self.environment = environment
        self.index_name = index_name
        self.tenant_id = tenant_id

        # Initialize Pinecone
        self.pc = Pinecone(api_key=api_key)

        # Get or create index
        existing_indexes = self.pc.list_indexes().names()
        if index_name not in existing_indexes:
            logger.info(f"Creating index {index_name}")
            self.pc.create_index(
                name=index_name,
                dimension=1536,  # text-embedding-3-small dimension
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-1"),
            )

        self.index = self.pc.Index(index_name)

    def _get_namespace(self, namespace_type="tasks"):
        if namespace_type == "tasks" and self.tenant_id:
            return f"tasks_{self.tenant_id}"
        else:
            return "tasks_default"

    async def store_task(self, task_id, vector, metadata):
        namespace = self._get_namespace()
        logger.info(f"Storing task {task_id} in namespace {namespace}")

        # Convert to sync for simplicity in this test
        self.index.upsert(vectors=[(task_id, vector, metadata)], namespace=namespace)

        return True

    async def search_tasks(self, query_vector, top_k=5, filter=None, user_context=None):
        namespace = self._get_namespace()
        logger.info(f"Searching in namespace {namespace} with filter: {filter}")

        # Apply security filters based on user context
        rbac_filter = {}
        if user_context:
            rbac_conditions = []

            # Filter by tenant
            if self.tenant_id and user_context.get("tenant_id") == self.tenant_id:
                rbac_conditions.append({"tenant_id": self.tenant_id})

            # Filter by user role and task assignment
            if user_context.get("role") == "admin":
                # Admins can see all tasks in their tenant
                pass
            elif user_context.get("user_id"):
                # Users can see tasks they created or are assigned to
                rbac_conditions.append(
                    {
                        "$or": [
                            {"created_by": user_context.get("user_id")},
                            {"assigned_to": user_context.get("user_id")},
                        ]
                    }
                )

            # Filter by related cases the user has access to
            if user_context.get("accessible_case_ids"):
                rbac_conditions.append(
                    {"related_case": {"$in": user_context["accessible_case_ids"]}}
                )

            if rbac_conditions:
                rbac_filter = {"$and": rbac_conditions}

        # Combine with any existing filters
        final_filter = None
        if filter and rbac_filter:
            final_filter = {"$and": [filter, rbac_filter]}
        elif filter:
            final_filter = filter
        elif rbac_filter:
            final_filter = rbac_filter

        # Search in Pinecone
        results = self.index.query(
            vector=query_vector,
            top_k=top_k,
            filter=final_filter,
            namespace=namespace,
            include_metadata=True,
        )

        return results.matches

    async def delete_task(self, task_id):
        namespace = self._get_namespace()
        logger.info(f"Deleting task {task_id} from namespace {namespace}")

        self.index.delete(ids=[task_id], namespace=namespace)

        return True


# Define TaskEmbeddingService for testing
class TaskEmbeddingService:
    def __init__(self, openai_api_key=None, pinecone_client=None):
        self.embedder = OpenAIEmbeddings(
            api_key=openai_api_key or os.getenv("OPENAI_API_KEY"),
            model="text-embedding-3-small",
        )

        if pinecone_client:
            self.pinecone_client = pinecone_client
        else:
            self.pinecone_client = PineconeClient(
                api_key=os.getenv("PINECONE_API_KEY"),
                environment=os.getenv("PINECONE_ENV"),
                index_name=os.getenv("PINECONE_INDEX_NAME", "pi-lawyer"),
                tenant_id=None,
            )

    async def generate_task_embedding(self, task_data, tenant_id):
        try:
            logger.info(
                f"Generating embedding for task {task_data.get('id')} for tenant {tenant_id}"
            )

            # Set tenant ID for this operation
            self.pinecone_client.tenant_id = tenant_id

            # Create rich context from task data
            task_text = self._create_task_text(task_data)

            # Generate embedding
            embedding = await self.embedder.aembed_query(task_text)

            # Create metadata with security attributes
            metadata = self._create_task_metadata(task_data, tenant_id)

            # Store in appropriate namespace with security metadata
            await self.pinecone_client.store_task(
                task_id=str(task_data["id"]), vector=embedding, metadata=metadata
            )

            logger.info(f"Successfully stored embedding for task {task_data.get('id')}")
            return {"status": "success", "task_id": task_data["id"]}

        except Exception as e:
            logger.error(f"Error generating task embedding: {str(e)}")
            return {"status": "error", "error": str(e), "task_id": task_data.get("id")}

    def _create_task_text(self, task_data):
        parts = []

        parts.append(f"Title: {task_data.get('title', '')}")

        if task_data.get("description"):
            parts.append(f"Description: {task_data.get('description', '')}")

        if task_data.get("status"):
            parts.append(f"Status: {task_data.get('status', '')}")

        if task_data.get("priority"):
            parts.append(f"Priority: {task_data.get('priority', '')}")

        if task_data.get("due_date"):
            parts.append(f"Due Date: {task_data.get('due_date', '')}")

        if task_data.get("assigned_to"):
            parts.append(f"Assigned To: {task_data.get('assigned_to', '')}")

        if task_data.get("related_case"):
            parts.append(f"Related Case: {task_data.get('related_case', '')}")

        return "\n".join(parts)

    def _create_task_metadata(self, task_data, tenant_id):
        metadata = {
            "tenant_id": tenant_id,
            "created_by": str(task_data.get("created_by", "")),
            "assigned_to": str(task_data.get("assigned_to", "")),
            "related_case": str(task_data.get("related_case", "")),
            "task_id": str(task_data.get("id", "")),
            "title": task_data.get("title", ""),
            "status": task_data.get("status", ""),
            "priority": task_data.get("priority", ""),
            "task_type": task_data.get("task_type", ""),
            "created_at": task_data.get("created_at", datetime.utcnow().isoformat()),
            "updated_at": task_data.get("updated_at", datetime.utcnow().isoformat()),
            "due_date": task_data.get("due_date", ""),
        }

        if task_data.get("tags") and isinstance(task_data["tags"], list):
            metadata["tags"] = task_data["tags"]

        return metadata

    async def delete_task_embedding(self, task_id, tenant_id):
        try:
            self.pinecone_client.tenant_id = tenant_id
            await self.pinecone_client.delete_task(task_id=str(task_id))
            return {"status": "success", "task_id": task_id}

        except Exception as e:
            logger.error(f"Error deleting task embedding: {str(e)}")
            return {"status": "error", "error": str(e), "task_id": task_id}

    async def search_similar_tasks(
        self, query, tenant_id, user_context, top_k=5, filters=None
    ):
        try:
            self.pinecone_client.tenant_id = tenant_id
            query_embedding = await self.embedder.aembed_query(query)

            results = await self.pinecone_client.search_tasks(
                query_vector=query_embedding,
                top_k=top_k,
                filter=filters,
                user_context=user_context,
            )

            formatted_results = []
            for match in results:
                formatted_results.append(
                    {
                        "task_id": match.metadata.get("task_id"),
                        "title": match.metadata.get("title"),
                        "status": match.metadata.get("status"),
                        "priority": match.metadata.get("priority"),
                        "assigned_to": match.metadata.get("assigned_to"),
                        "due_date": match.metadata.get("due_date"),
                        "score": match.score,
                    }
                )

            return formatted_results

        except Exception as e:
            logger.error(f"Error searching similar tasks: {str(e)}")
            return []


async def test_task_embedding_pipeline():
    """Test the full task embedding pipeline."""
    logger.info("Starting task embedding test")

    # Create a PineconeClient
    pinecone_client = PineconeClient(
        api_key=os.getenv("PINECONE_API_KEY"),
        environment=os.getenv("PINECONE_ENV"),
        index_name=os.getenv("PINECONE_INDEX_NAME"),
        tenant_id="test_tenant_123",  # Using a test tenant ID
    )

    # Create a TaskEmbeddingService
    embedding_service = TaskEmbeddingService(
        openai_api_key=os.getenv("OPENAI_API_KEY"), pinecone_client=pinecone_client
    )

    # Create a list of mock tasks for testing
    tenant_id = "test_tenant_123"
    mock_tasks = [
        {
            "id": "task_001",
            "title": "Review medical records for Smith case",
            "description": "Go through all medical records to identify relevant information for the personal injury claim. Look for evidence of injuries, treatments, and long-term effects.",
            "status": "open",
            "priority": "high",
            "created_by": "user123",
            "assigned_to": "user456",
            "related_case": "case789",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "due_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
            "task_type": "research",
            "tags": ["medical", "evidence", "urgent"],
        },
        {
            "id": "task_002",
            "title": "Draft demand letter to insurance company",
            "description": "Create a detailed demand letter to ABC Insurance for the Johnson case. Include all medical expenses, lost wages, and pain and suffering calculations.",
            "status": "in_progress",
            "priority": "medium",
            "created_by": "user123",
            "assigned_to": "user456",
            "related_case": "case123",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "due_date": (datetime.utcnow() + timedelta(days=5)).isoformat(),
            "task_type": "document",
            "tags": ["insurance", "demand", "negotiation"],
        },
        {
            "id": "task_003",
            "title": "Schedule expert witness meeting",
            "description": "Arrange a meeting with Dr. Williams to discuss testimony for the Rodriguez case. Need to review medical opinions on long-term disability.",
            "status": "open",
            "priority": "low",
            "created_by": "user789",
            "assigned_to": "user123",
            "related_case": "case456",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "due_date": (datetime.utcnow() + timedelta(days=14)).isoformat(),
            "task_type": "meeting",
            "tags": ["expert", "witness", "testimony"],
        },
    ]

    # Step 1: Generate embeddings for all mock tasks
    logger.info("Generating embeddings for mock tasks...")
    for task in mock_tasks:
        logger.info(f"Processing task: {task['id']} - {task['title']}")
        result = await embedding_service.generate_task_embedding(task, tenant_id)
        logger.info(f"Result: {result}")

    # Wait a moment for Pinecone to index
    logger.info("Waiting for Pinecone to index...")
    await asyncio.sleep(2)

    # Step 2: Test searching for tasks with various queries
    logger.info("\nTesting task searches...")

    # Create user context for searches
    user_context = {
        "tenant_id": tenant_id,
        "user_id": "user456",  # This user is assigned to tasks 001 and 002
        "role": "user",
        "accessible_case_ids": [
            "case123",
            "case789",
        ],  # This user has access to these cases
    }

    # Test different search queries
    search_queries = [
        "medical records",
        "draft a letter to insurance",
        "expert witness",
        "urgent tasks",
        "Johnson case",
    ]

    for query in search_queries:
        logger.info(f"\nSearching for: '{query}'")
        results = await embedding_service.search_similar_tasks(
            query=query, tenant_id=tenant_id, user_context=user_context, top_k=3
        )

        logger.info(f"Found {len(results)} results:")
        for i, result in enumerate(results, 1):
            logger.info(f"{i}. {result['title']} (Score: {result['score']:.4f})")
            logger.info(
                f"   Status: {result['status']}, Priority: {result['priority']}"
            )

    # Step 3: Test search with admin role (should see all tasks)
    logger.info("\nTesting search with admin role...")
    admin_context = {
        "tenant_id": tenant_id,
        "user_id": "admin_user",
        "role": "admin",
        "accessible_case_ids": [],  # Admin can see all cases
    }

    results = await embedding_service.search_similar_tasks(
        query="all tasks", tenant_id=tenant_id, user_context=admin_context, top_k=10
    )

    logger.info(f"Admin search found {len(results)} results:")
    for i, result in enumerate(results, 1):
        logger.info(f"{i}. {result['title']} (Score: {result['score']:.4f})")

    # Step 4: Test deleting a task embedding
    logger.info("\nTesting task embedding deletion...")
    delete_result = await embedding_service.delete_task_embedding("task_001", tenant_id)
    logger.info(f"Deletion result: {delete_result}")

    # Verify deletion
    logger.info("Searching again after deletion...")
    results = await embedding_service.search_similar_tasks(
        query="medical records",
        tenant_id=tenant_id,
        user_context=admin_context,
        top_k=3,
    )

    logger.info(f"Search after deletion found {len(results)} results:")
    for i, result in enumerate(results, 1):
        logger.info(f"{i}. {result['title']} (Score: {result['score']:.4f})")

    # Clean up - delete remaining test tasks
    logger.info("\nCleaning up test data...")
    for task in mock_tasks[1:]:  # Skip task_001 as it's already deleted
        await embedding_service.delete_task_embedding(task["id"], tenant_id)

    logger.info("Test completed successfully!")


if __name__ == "__main__":
    asyncio.run(test_task_embedding_pipeline())
