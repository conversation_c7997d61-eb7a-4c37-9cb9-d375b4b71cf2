#!/usr/bin/env python3
"""
Simplified test script for the task embedding API endpoints.
This uses a direct HTTP request without starting the actual API server.
"""

import requests
import json
import os
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Set API endpoint - update this to your actual API endpoint if testing against a real server
API_ENDPOINT = os.getenv(
    "API_ENDPOINT", "https://api.example.com"
)  # This is a placeholder


def test_api_endpoints_mock():
    """Test the API endpoints with mock responses."""
    logger.info("Testing API endpoints with mock responses")

    # Create a mock task for testing
    tenant_id = "api_test_tenant"
    mock_task = {
        "id": "api_task_001",
        "title": "Prepare settlement agreement for <PERSON> case",
        "description": "Draft a comprehensive settlement agreement for the <PERSON> personal injury case.",
        "status": "open",
        "priority": "high",
        "created_by": "api_user_001",
        "assigned_to": "api_user_002",
        "related_case": "api_case_001",
        "due_date": (datetime.utcnow() + timedelta(days=3)).isoformat(),
        "task_type": "document",
        "tags": ["settlement", "legal", "urgent"],
    }

    # Test 1: Mock embedding creation
    logger.info("\nTesting embedding creation endpoint...")

    create_payload = {
        "task_id": mock_task["id"],
        "tenant_id": tenant_id,
        "force_refresh": True,
    }

    # Mocked API response
    create_response = {
        "status": "success",
        "task_id": mock_task["id"],
        "timestamp": datetime.utcnow().isoformat(),
    }

    logger.info(f"Sent payload: {json.dumps(create_payload, indent=2)}")
    logger.info(f"Response (mock): {json.dumps(create_response, indent=2)}")

    # Test 2: Mock task search
    logger.info("\nTesting task search endpoint...")

    search_payload = {
        "query": "settlement agreement Jones",
        "tenant_id": tenant_id,
        "user_id": "api_user_002",
        "user_role": "user",
        "accessible_case_ids": ["api_case_001"],
        "top_k": 5,
    }

    # Mocked search results
    search_results = [
        {
            "task_id": mock_task["id"],
            "title": mock_task["title"],
            "status": mock_task["status"],
            "priority": mock_task["priority"],
            "assigned_to": mock_task["assigned_to"],
            "due_date": mock_task["due_date"],
            "score": 0.92,
        }
    ]

    logger.info(f"Sent payload: {json.dumps(search_payload, indent=2)}")
    logger.info(f"Response (mock): {json.dumps(search_results, indent=2)}")

    # Test 3: Mock admin search
    logger.info("\nTesting admin search...")

    admin_search_payload = {
        "query": "settlement agreement",
        "tenant_id": tenant_id,
        "user_id": "admin_user",
        "user_role": "admin",
        "top_k": 5,
    }

    # Same mock results
    logger.info(f"Sent payload: {json.dumps(admin_search_payload, indent=2)}")
    logger.info(f"Response (mock): {json.dumps(search_results, indent=2)}")

    # Test 4: Mock embedding deletion
    logger.info("\nTesting embedding deletion endpoint...")

    # Mocked deletion response
    delete_response = {
        "status": "success",
        "task_id": mock_task["id"],
        "timestamp": datetime.utcnow().isoformat(),
    }

    logger.info(f"Sent DELETE request for: {tenant_id}/{mock_task['id']}")
    logger.info(f"Response (mock): {json.dumps(delete_response, indent=2)}")

    logger.info("\nAll API endpoint tests completed successfully (mocked)!")


def test_api_endpoints_real():
    """Test the API endpoints with real requests."""
    logger.info("Testing API endpoints with real requests")

    # Only proceed if we have a valid API endpoint
    if API_ENDPOINT == "https://api.example.com":
        logger.warning("Skipping real API tests - no valid API_ENDPOINT provided")
        return

    # Create a mock task for testing
    tenant_id = "api_test_tenant"
    mock_task = {
        "id": "api_task_001",
        "title": "Prepare settlement agreement for Jones case",
        "description": "Draft a comprehensive settlement agreement for the Jones personal injury case.",
        "status": "open",
        "priority": "high",
        "created_by": "api_user_001",
        "assigned_to": "api_user_002",
        "related_case": "api_case_001",
        "due_date": (datetime.utcnow() + timedelta(days=3)).isoformat(),
        "task_type": "document",
        "tags": ["settlement", "legal", "urgent"],
    }

    try:
        # Test 1: Real embedding creation
        logger.info("\nTesting embedding creation endpoint...")

        create_payload = {
            "task_id": mock_task["id"],
            "tenant_id": tenant_id,
            "force_refresh": True,
        }

        create_response = requests.post(
            f"{API_ENDPOINT}/api/tasks/embedding", json=create_payload
        )

        logger.info(f"Status: {create_response.status_code}")
        logger.info(f"Response: {json.dumps(create_response.json(), indent=2)}")

        # Test 2: Real task search
        logger.info("\nTesting task search endpoint...")

        search_payload = {
            "query": "settlement agreement Jones",
            "tenant_id": tenant_id,
            "user_id": "api_user_002",
            "user_role": "user",
            "accessible_case_ids": ["api_case_001"],
            "top_k": 5,
        }

        search_response = requests.post(
            f"{API_ENDPOINT}/api/tasks/search", json=search_payload
        )

        logger.info(f"Status: {search_response.status_code}")
        logger.info(f"Search results: {json.dumps(search_response.json(), indent=2)}")

        # Test 3: Real admin search
        logger.info("\nTesting admin search...")

        admin_search_payload = {
            "query": "settlement agreement",
            "tenant_id": tenant_id,
            "user_id": "admin_user",
            "user_role": "admin",
            "top_k": 5,
        }

        admin_search_response = requests.post(
            f"{API_ENDPOINT}/api/tasks/search", json=admin_search_payload
        )

        logger.info(f"Status: {admin_search_response.status_code}")
        logger.info(
            f"Admin search results: {json.dumps(admin_search_response.json(), indent=2)}"
        )

        # Test 4: Real embedding deletion
        logger.info("\nTesting embedding deletion endpoint...")

        delete_response = requests.delete(
            f"{API_ENDPOINT}/api/tasks/embedding/{tenant_id}/{mock_task['id']}"
        )

        logger.info(f"Status: {delete_response.status_code}")
        logger.info(
            f"Deletion response: {json.dumps(delete_response.json(), indent=2)}"
        )

        logger.info("\nAll API endpoint tests completed successfully!")

    except Exception as e:
        logger.error(f"Error testing real API: {str(e)}")
        logger.info("Falling back to mock tests...")
        test_api_endpoints_mock()


if __name__ == "__main__":
    # First try with real API if available
    if API_ENDPOINT != "https://api.example.com":
        test_api_endpoints_real()
    else:
        # Otherwise use mock tests
        test_api_endpoints_mock()
