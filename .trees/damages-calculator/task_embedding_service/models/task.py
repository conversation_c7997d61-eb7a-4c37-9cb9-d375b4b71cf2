"""
Pydantic models for Task Embedding Service
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime


class TaskBase(BaseModel):
    """Base model with common task fields."""

    title: str
    description: Optional[str] = None
    status: str
    priority: str
    created_by: str
    assigned_to: Optional[str] = None
    related_case: Optional[str] = None
    task_type: Optional[str] = None
    due_date: Optional[datetime] = None
    tags: Optional[List[str]] = None


class TaskEmbeddingRequest(BaseModel):
    """Request model for task embedding generation."""

    task_id: str = Field(..., description="ID of the task")
    tenant_id: str = Field(..., description="Tenant ID for multi-tenant isolation")
    force_refresh: bool = Field(
        False, description="Force refresh the embedding even if it exists"
    )


class TaskSearchRequest(BaseModel):
    """Request model for semantic task search."""

    query: str = Field(..., description="Search query text")
    tenant_id: str = Field(..., description="Tenant ID for multi-tenant isolation")
    user_id: str = Field(..., description="User ID for access control")
    user_role: str = Field("user", description="User role for access control")
    accessible_case_ids: Optional[List[str]] = Field(
        None, description="List of case IDs the user has access to"
    )
    top_k: int = Field(5, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(
        None, description="Additional filters to apply"
    )


class TaskEmbeddingResponse(BaseModel):
    """Response model for task embedding operations."""

    status: str = Field(..., description="Operation status (success/error)")
    task_id: str = Field(..., description="ID of the task")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    timestamp: str = Field(..., description="Timestamp of the operation")


class TaskSearchResult(BaseModel):
    """Model for a single task search result."""

    task_id: str
    title: str
    status: str
    priority: str
    assigned_to: Optional[str] = None
    due_date: Optional[str] = None
    score: float
