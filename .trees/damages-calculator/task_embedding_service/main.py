"""
Main entry point for the Task Embedding Service.
"""
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("task_embedding_service.log"),
    ],
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Task Embedding Service",
    description="API for generating and managing task embeddings",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include API routes
from .api.routes import router

app.include_router(router)


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "Task Embedding Service",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "/api/tasks/embedding",
            "/api/tasks/search",
            "/api/tasks/embedding/{tenant_id}/{task_id}",
            "/health",
        ],
    }


def main():
    """Run the uvicorn server."""
    import uvicorn

    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "task_embedding_service.main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
    )


if __name__ == "__main__":
    main()
