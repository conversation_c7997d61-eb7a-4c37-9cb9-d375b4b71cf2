"""
API routes for task embedding service
"""
from fastapi import APIRouter, Request, Depends, HTTPException, Body
import logging
from datetime import datetime
import json
import httpx
import os

from ..models.task import (
    TaskEmbeddingRequest,
    TaskSearchRequest,
    TaskEmbeddingResponse,
    TaskSearchResult,
)
from ..services.embedding import embedding_service

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create API router
router = APIRouter(tags=["tasks"])


@router.post("/api/tasks/embedding", response_model=TaskEmbeddingResponse)
async def create_task_embedding(request: Request):
    """Generate and store an embedding for a task."""
    try:
        # Parse request body
        body = await request.json()
        logger.info(f"Received task embedding request for task {body.get('task_id')}")

        # Extract task data from request
        task_id = body.get("task_id")
        tenant_id = body.get("tenant_id")

        if not task_id or not tenant_id:
            raise HTTPException(
                status_code=400,
                detail="Missing required fields: task_id and tenant_id are required",
            )

        # Get task data from database
        # In a real implementation, you would fetch this from Supabase or your DB
        task_data = await _get_task_from_database(task_id, tenant_id)

        if not task_data:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Generate and store embedding
        result = await embedding_service.generate_task_embedding(
            task_data=task_data, tenant_id=tenant_id
        )

        # Return response
        return {
            "status": result.get("status", "error"),
            "task_id": task_id,
            "error": result.get("error"),
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException as he:
        # Rethrow HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error processing task embedding request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/api/tasks/search", response_model=list[TaskSearchResult])
async def search_similar_tasks(request: TaskSearchRequest = Body(...)):
    """Search for tasks based on semantic similarity."""
    try:
        logger.info(f"Received task search request: {request.query}")

        # Create user context for RBAC filtering
        user_context = {
            "tenant_id": request.tenant_id,
            "user_id": request.user_id,
            "role": request.user_role,
            "accessible_case_ids": request.accessible_case_ids or [],
        }

        # Perform search with RBAC filtering
        results = await embedding_service.search_similar_tasks(
            query=request.query,
            tenant_id=request.tenant_id,
            user_context=user_context,
            top_k=request.top_k,
            filters=request.filters,
        )

        return results

    except Exception as e:
        logger.error(f"Error processing task search request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(
    "/api/tasks/embedding/{tenant_id}/{task_id}", response_model=TaskEmbeddingResponse
)
async def delete_task_embedding(tenant_id: str, task_id: str):
    """Delete a task embedding from the index."""
    try:
        logger.info(f"Received task embedding deletion request for task {task_id}")

        # Delete embedding
        result = await embedding_service.delete_task_embedding(
            task_id=task_id, tenant_id=tenant_id
        )

        # Return response
        return {
            "status": result.get("status", "error"),
            "task_id": task_id,
            "error": result.get("error"),
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error processing task embedding deletion request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "task-embedding-service",
        "timestamp": datetime.utcnow().isoformat(),
    }


async def _get_task_from_database(task_id: str, tenant_id: str) -> dict:
    """Fetch task data from Supabase.

    In a production environment, this would connect to your Supabase database.
    For now, it uses a simplified approach to fetch data.
    """
    # If SUPABASE_URL and SUPABASE_KEY are set, use Supabase
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

    if supabase_url and supabase_key:
        try:
            # Use httpx to make a direct API call to Supabase
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{supabase_url}/rest/v1/tasks",
                    params={
                        "id": f"eq.{task_id}",
                        "tenant_id": f"eq.{tenant_id}",
                        "select": "*",
                    },
                    headers={
                        "apikey": supabase_key,
                        "Authorization": f"Bearer {supabase_key}",
                    },
                )

                if response.status_code == 200:
                    data = response.json()
                    if data and len(data) > 0:
                        return data[0]

                logger.warning(f"Task not found in Supabase or error: {response.text}")

        except Exception as e:
            logger.error(f"Error fetching task from Supabase: {str(e)}")

    # Fallback to mock data for testing
    logger.info("Using mock task data for testing")
    return {
        "id": task_id,
        "title": f"Mock Task {task_id}",
        "description": "This is a mock task for testing the embedding service",
        "status": "open",
        "priority": "medium",
        "created_by": "system",
        "assigned_to": "user123",
        "related_case": "case456",
        "tenant_id": tenant_id,
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat(),
        "due_date": (
            datetime.utcnow().replace(day=datetime.utcnow().day + 7)
        ).isoformat(),
    }
