"""
Pinecone client for vector search capabilities.
"""
from typing import Dict, Any, List, Optional, Literal
import logging
from pinecone import Pinecone, ServerlessSpec

logger = logging.getLogger(__name__)


class PineconeClient:
    """Client for interacting with Pinecone vector database."""

    def __init__(self, api_key: str, index_name: str, tenant_id: Optional[str] = None):
        """Initialize the Pinecone client.

        Args:
            api_key: Pinecone API key
            index_name: Name of the Pinecone index to use
            tenant_id: Optional tenant ID for multi-tenant operations
        """
        self.api_key = api_key
        self.index_name = index_name
        self.tenant_id = tenant_id

        # Initialize Pinecone
        self.pc = Pinecone(api_key=api_key)

        # Get or create index
        existing_indexes = self.pc.list_indexes().names()
        if index_name not in existing_indexes:
            logger.info(f"Creating index {index_name}")
            self.pc.create_index(
                name=index_name,
                dimension=1536,  # text-embedding-3-small dimension
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-1"),
            )

        self.index = self.pc.Index(index_name)

    def _get_namespace(
        self, namespace_type: Literal["legal", "tenant", "tasks"] = "tasks"
    ) -> str:
        """Get the appropriate namespace based on context.

        Args:
            namespace_type: The type of namespace to use (legal, tenant, or tasks)

        Returns:
            Namespace string
        """
        if namespace_type == "legal":
            return "legal"
        elif namespace_type == "tasks" and self.tenant_id:
            return f"tasks_{self.tenant_id}"
        elif namespace_type == "tenant" and self.tenant_id:
            return f"tenant_{self.tenant_id}"
        else:
            raise ValueError("Tenant ID required for non-legal operations")

    async def store_task(
        self, task_id: str, vector: List[float], metadata: Dict[str, Any]
    ) -> None:
        """Store a task vector with metadata.

        Args:
            task_id: Unique identifier for the task
            vector: Task embedding vector
            metadata: Additional task metadata including security attributes
        """
        namespace = self._get_namespace(namespace_type="tasks")
        logger.info(f"Storing task {task_id} in namespace {namespace}")

        # Upsert to Pinecone
        self.index.upsert(vectors=[(task_id, vector, metadata)], namespace=namespace)

    async def search_tasks(
        self,
        query_vector: List[float],
        top_k: int = 5,
        filter: Dict[str, Any] = None,
        user_context: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """Search tasks using vector similarity with security filters.

        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filter: Optional base metadata filters
            user_context: User context for RBAC filtering (user_id, role, etc.)

        Returns:
            List of similar tasks with scores
        """
        namespace = self._get_namespace(namespace_type="tasks")
        logger.info(f"Searching in namespace {namespace} with filter: {filter}")

        # Apply security filters based on user context
        rbac_filter = {}
        if user_context:
            rbac_conditions = []

            # Filter by tenant
            if self.tenant_id and user_context.get("tenant_id") == self.tenant_id:
                rbac_conditions.append({"tenant_id": self.tenant_id})

            # Filter by user role and task assignment
            if user_context.get("role") == "admin":
                # Admins can see all tasks in their tenant
                pass
            elif user_context.get("user_id"):
                # Users can see tasks they created or are assigned to
                rbac_conditions.append(
                    {
                        "$or": [
                            {"created_by": user_context.get("user_id")},
                            {"assigned_to": user_context.get("user_id")},
                        ]
                    }
                )

            # Filter by related cases the user has access to
            if user_context.get("accessible_case_ids"):
                rbac_conditions.append(
                    {"related_case": {"$in": user_context["accessible_case_ids"]}}
                )

            if rbac_conditions:
                rbac_filter = {"$and": rbac_conditions}

        # Combine with any existing filters
        final_filter = None
        if filter and rbac_filter:
            final_filter = {"$and": [filter, rbac_filter]}
        elif filter:
            final_filter = filter
        elif rbac_filter:
            final_filter = rbac_filter

        # Search in Pinecone
        results = self.index.query(
            vector=query_vector,
            top_k=top_k,
            filter=final_filter,
            namespace=namespace,
            include_metadata=True,
        )

        return results.matches

    async def delete_task(self, task_id: str) -> None:
        """Delete a task from the index.

        Args:
            task_id: ID of the task to delete
        """
        namespace = self._get_namespace(namespace_type="tasks")
        logger.info(f"Deleting task {task_id} from namespace {namespace}")

        self.index.delete(ids=[task_id], namespace=namespace)
