"""
Isolated tests for the CopilotKit endpoint with AG-UI protocol support.

This module contains tests for the CopilotKit endpoint implementation
that are completely isolated from the existing codebase.
"""

import json
import os
import asyncio
import pytest
from fastapi import FastAPI, APIRouter, Request, HTTPException, Depends, Response, BackgroundTasks
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse, StreamingResponse
from unittest.mock import patch, MagicMock

# Create a router for testing
router = APIRouter(
    prefix="/copilotkit",
    tags=["copilotkit"],
    responses={404: {"description": "Not found"}},
)


# Verify endpoint key middleware
async def verify_endpoint_key(request: Request) -> None:
    """
    Verify the CopilotKit endpoint key for secure access.
    
    Args:
        request: The FastAPI request object
        
    Raises:
        HTTPException: If the endpoint key is invalid
    """
    # Get the endpoint secret from environment variables
    endpoint_secret = os.environ.get("CPK_ENDPOINT_SECRET", "")

    # If no secret is set, disable auth in development
    if not endpoint_secret:
        return

    # Check the header for the endpoint key
    auth_header = request.headers.get("X-CPK-Endpoint-Key")
    if not auth_header or auth_header != endpoint_secret:
        raise HTTPException(
            status_code=403,
            detail="Invalid endpoint key"
        )


async def handle_legacy_request(body: dict) -> JSONResponse:
    """Handle legacy GraphQL format requests."""
    # Extract GraphQL variables
    variables = body.get("variables", {})
    data = variables.get("data", {})
    
    # Get agent name from variables or fall back to default
    agent_name = data.get("agent", "supervisor_agent")
    thread_id = data.get("threadId", "test-thread")
    
    return JSONResponse(
        content={
            "data": {
                "generateCopilotResponse": {
                    "messages": [
                        {
                            "content": [
                                "This endpoint now supports the AG-UI protocol. Please update your client."
                            ],
                            "role": "assistant",
                        }
                    ],
                    "done": True,
                    "threadId": thread_id,
                }
            }
        }
    )


async def handle_agui_request(body: dict, background_tasks: BackgroundTasks):
    """Handle AG-UI format requests."""
    # Extract request parameters
    agent_name = body.get("agent", "supervisor_agent")
    thread_id = body.get("threadId", "test-thread")
    stream = body.get("stream", True)
    
    # For streaming responses
    if stream:
        return StreamingResponse(
            generate_streaming_response(),
            media_type="text/event-stream",
        )
    
    # For non-streaming responses
    return JSONResponse(
        content={
            "messages": [
                {
                    "role": "assistant",
                    "content": "Test response"
                }
            ],
            "done": True,
            "threadId": thread_id,
        }
    )


async def generate_streaming_response():
    """Generate a streaming response."""
    yield f"data: {json.dumps({'type': 'start', 'threadId': 'test-thread'})}\n\n"
    yield f"data: {json.dumps({'type': 'content', 'content': 'Test response'})}\n\n"
    yield f"data: {json.dumps({'type': 'done', 'threadId': 'test-thread'})}\n\n"


@router.post("", response_model=None)
async def handle_copilotkit_request(
    request: Request,
    background_tasks: BackgroundTasks,
    _: None = Depends(verify_endpoint_key),
):
    """
    Main endpoint for CopilotKit requests with AG-UI protocol support.
    """
    try:
        # Parse request body
        body = await request.json()
        
        # Extract GraphQL operation if present (legacy format)
        operation_name = body.get("operationName")

        # Handle different request structures
        if operation_name == "generateCopilotResponse":
            # Legacy GraphQL format
            return await handle_legacy_request(body)
        else:
            # AG-UI format
            return await handle_agui_request(body, background_tasks)

    except json.JSONDecodeError:
        return JSONResponse(
            status_code=400,
            content={"error": "Invalid JSON in request body"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


@router.options("")
async def options_handler():
    """Handle CORS preflight requests."""
    return {}


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}


# Create a test app
app = FastAPI()
app.include_router(router)

# Create a test client
client = TestClient(app)


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/copilotkit/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_options_handler():
    """Test the CORS preflight handler."""
    response = client.options("/copilotkit")
    assert response.status_code == 200


def test_endpoint_key_verification_success():
    """Test that the endpoint key verification succeeds with a valid key."""
    # Set environment variable for testing
    os.environ["CPK_ENDPOINT_SECRET"] = "test-secret"
    
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "test-secret"},
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code != 403  # Not forbidden
    
    # Clean up
    os.environ.pop("CPK_ENDPOINT_SECRET", None)


def test_endpoint_key_verification_failure():
    """Test that the endpoint key verification fails with an invalid key."""
    # Set environment variable for testing
    os.environ["CPK_ENDPOINT_SECRET"] = "test-secret"
    
    response = client.post(
        "/copilotkit",
        headers={"X-CPK-Endpoint-Key": "invalid-secret"},
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code == 403
    assert "Invalid endpoint key" in response.json()["detail"]
    
    # Clean up
    os.environ.pop("CPK_ENDPOINT_SECRET", None)


def test_endpoint_key_verification_disabled():
    """Test that the endpoint key verification is disabled when no secret is set."""
    # Ensure environment variable is not set
    os.environ.pop("CPK_ENDPOINT_SECRET", None)
    
    response = client.post(
        "/copilotkit",
        json={"agent": "test-agent", "messages": []},
    )
    assert response.status_code != 403  # Not forbidden


def test_handle_legacy_request():
    """Test handling a legacy GraphQL request."""
    response = client.post(
        "/copilotkit",
        json={
            "operationName": "generateCopilotResponse",
            "variables": {"data": {"agent": "test-agent", "messages": []}},
        },
    )
    
    assert response.status_code == 200
    assert "generateCopilotResponse" in response.json()["data"]


def test_handle_agui_request_non_streaming():
    """Test handling an AG-UI request with non-streaming response."""
    response = client.post(
        "/copilotkit",
        json={
            "agent": "test-agent",
            "messages": [{"role": "user", "content": "Test message"}],
            "stream": False,
        },
    )
    
    assert response.status_code == 200
    assert response.json()["messages"][0]["role"] == "assistant"
    assert response.json()["messages"][0]["content"] == "Test response"


def test_handle_agui_request_streaming():
    """Test handling an AG-UI request with streaming response."""
    response = client.post(
        "/copilotkit",
        json={
            "agent": "test-agent",
            "messages": [{"role": "user", "content": "Test message"}],
            "stream": True,
        },
    )
    
    assert response.status_code == 200
    assert "text/event-stream" in response.headers["content-type"]
    
    # Check the streaming response content
    content = response.content.decode("utf-8")
    assert "data: " in content
    assert "test-thread" in content


def test_invalid_json():
    """Test handling an invalid JSON request."""
    response = client.post(
        "/copilotkit",
        content="invalid json",
    )
    assert response.status_code == 400
    assert "Invalid JSON" in response.json()["error"]
