#!/usr/bin/env python3
"""
Comprehensive Test Suite for All 8 Agents
This script tests all 8 agents to verify they work and are AG-UI compliant.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

class AllAgentsTestSuite:
    """Comprehensive test suite for all 8 agents."""
    
    def __init__(self):
        self.test_results = {}
        
    def test_agent_basic_functionality(self, agent_name: str, agent_class, agent_instance) -> dict:
        """Test basic agent functionality."""
        results = {
            "import": False,
            "instantiation": False,
            "graph_creation": False,
            "graph_type": None,
            "error": None
        }
        
        try:
            # Import and instantiation already done
            results["import"] = True
            results["instantiation"] = True
            
            # Test graph creation
            graph = agent_instance.create_graph()
            results["graph_creation"] = True
            results["graph_type"] = str(type(graph))
            
            print(f"✅ {agent_name}: All basic tests passed")
            return results
            
        except Exception as e:
            results["error"] = str(e)
            print(f"❌ {agent_name}: Basic test failed - {e}")
            return results
    
    def test_all_agents(self):
        """Test all 8 agents."""
        print("🚀 Testing All 8 Agents")
        print("=" * 60)
        
        # Define all 8 agents to test
        agents_to_test = [
            # Working agents (from previous tests)
            ("IntakeAgent", "backend.agents.interactive.intake.agent", "IntakeAgent"),
            ("DocumentAgent", "backend.agents.insights.document.agent", "DocumentAgent"),
            ("DeadlineInsightsAgent", "backend.agents.insights.deadline.agent", "DeadlineInsightsAgent"),
            
            # Recently fixed agents
            ("ResearchAgent", "backend.agents.interactive.research.agent", "ResearchAgent"),
            ("CalendarCrudAgent", "backend.agents.interactive.calendar_crud.agent", "CalendarCrudAgent"),
            ("SupervisorAgent", "backend.agents.insights.supervisor.agent", "SupervisorAgent"),
            ("TaskCrudAgent", "backend.agents.interactive.task_crud.agent", "TaskCrudAgent"),
            ("MatterClientAgent", "backend.agents.matter_client.agent", "MatterClientAgent"),
        ]
        
        successful_agents = []
        failed_agents = []
        
        for agent_name, module_path, class_name in agents_to_test:
            print(f"\n🧪 Testing {agent_name}:")
            print("-" * 40)
            
            try:
                # Dynamic import
                module = __import__(module_path, fromlist=[class_name])
                agent_class = getattr(module, class_name)
                
                # Create instance
                agent_instance = agent_class()
                print(f"   ✅ Import and instantiation successful")
                
                # Test basic functionality
                results = self.test_agent_basic_functionality(agent_name, agent_class, agent_instance)
                self.test_results[agent_name] = results
                
                if results["graph_creation"]:
                    successful_agents.append(agent_name)
                else:
                    failed_agents.append(agent_name)
                    
            except Exception as e:
                print(f"   ❌ Failed to test {agent_name}: {e}")
                self.test_results[agent_name] = {
                    "import": False,
                    "instantiation": False,
                    "graph_creation": False,
                    "error": str(e)
                }
                failed_agents.append(agent_name)
        
        # Generate summary report
        self.generate_summary_report(successful_agents, failed_agents)
        
        return len(successful_agents), len(failed_agents)
    
    def generate_summary_report(self, successful_agents: list, failed_agents: list):
        """Generate a comprehensive summary report."""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE AGENT TEST SUMMARY")
        print("=" * 60)
        
        total_agents = len(successful_agents) + len(failed_agents)
        success_rate = (len(successful_agents) / total_agents * 100) if total_agents > 0 else 0
        
        print(f"📈 Overall Results:")
        print(f"   Total Agents Tested: {total_agents}")
        print(f"   ✅ Successful: {len(successful_agents)}")
        print(f"   ❌ Failed: {len(failed_agents)}")
        print(f"   📊 Success Rate: {success_rate:.1f}%")
        
        if successful_agents:
            print(f"\n🎉 Successful Agents ({len(successful_agents)}):")
            for i, agent in enumerate(successful_agents, 1):
                graph_type = self.test_results[agent].get("graph_type", "Unknown")
                print(f"   {i:2d}. {agent:20} | Graph: {graph_type}")
        
        if failed_agents:
            print(f"\n⚠️ Failed Agents ({len(failed_agents)}):")
            for i, agent in enumerate(failed_agents, 1):
                error = self.test_results[agent].get("error", "Unknown error")
                print(f"   {i:2d}. {agent:20} | Error: {error[:50]}...")
        
        # Detailed breakdown
        print(f"\n📋 Detailed Test Results:")
        print(f"{'Agent':<20} | {'Import':<6} | {'Instance':<8} | {'Graph':<5} | {'Status'}")
        print("-" * 70)
        
        for agent_name, results in self.test_results.items():
            import_status = "✅" if results["import"] else "❌"
            instance_status = "✅" if results["instantiation"] else "❌"
            graph_status = "✅" if results["graph_creation"] else "❌"
            overall_status = "PASS" if results["graph_creation"] else "FAIL"
            
            print(f"{agent_name:<20} | {import_status:<6} | {instance_status:<8} | {graph_status:<5} | {overall_status}")
        
        # Final assessment
        if len(successful_agents) == 8:
            print(f"\n🎉 ALL 8 AGENTS ARE WORKING!")
            print(f"✅ Ready for CopilotKit registration and AG-UI compliance testing")
        elif len(successful_agents) >= 6:
            print(f"\n🟡 Most agents are working ({len(successful_agents)}/8)")
            print(f"⚠️ Need to fix {len(failed_agents)} remaining agents")
        else:
            print(f"\n🔴 Significant issues found ({len(successful_agents)}/8 working)")
            print(f"❌ Need to address multiple agent failures")

def main():
    """Run the comprehensive agent test suite."""
    print("🚀 Comprehensive Agent Test Suite")
    print("Testing all 8 agents for basic functionality and graph creation")
    print("=" * 80)
    
    tester = AllAgentsTestSuite()
    successful_count, failed_count = tester.test_all_agents()
    
    # Return success if all agents work
    return successful_count == 8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
