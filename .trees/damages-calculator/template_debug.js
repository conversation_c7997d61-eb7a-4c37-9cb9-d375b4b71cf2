// Copy and paste this code into your browser console when viewing the templates page
// It will log detailed information about the templates currently displayed in the UI

console.log('Template Debugging Tool');

// Function to analyze templates in the UI
function analyzeTemplates() {
  // Find elements that likely contain template data
  const templateElements = document.querySelectorAll('[class*="template"], [class*="card"], [class*="list-item"]');

  if (templateElements.length === 0) {
    console.log('No template elements found in the UI. Try running this on the templates page.');
    return;
  }

  console.log(`Found ${templateElements.length} potential template elements in the UI`);

  // Try to extract template information
  const templateNames = Array.from(document.querySelectorAll('h3, h4, .title, [class*="title"], [class*="name"]'))
    .filter(el => el.textContent.trim().length > 0)
    .map(el => el.textContent.trim());

  console.log('Template names found in the UI:');
  templateNames.forEach((name, i) => {
    console.log(`${i+1}. ${name}`);
  });

  // Look for React application state
  console.log('\nAttempting to find React component state...');

  // Find template data in React dev tools
  console.log(`
To view complete template data:
1. Open React DevTools
2. Find the templates component (likely named "TemplatesPage" or similar)
3. Look for a state variable named "templates" or "filteredTemplates"
4. Inspect the values there
`);
}

// Get info about fetch requests
function checkNetworkRequests() {
  console.log('\nChecking recent network requests:');

  // Show timing of fetch requests
  const fetchData = performance.getEntriesByType('resource')
    .filter(entry => entry.name.includes('supabase') || entry.name.includes('templates'))
    .map(entry => ({
      url: entry.name,
      duration: Math.round(entry.duration),
      startTime: new Date(performance.timeOrigin + entry.startTime).toISOString(),
      type: entry.initiatorType
    }));

  console.table(fetchData);

  console.log(`
To see the exact template data being fetched:
1. Open Network tab in DevTools
2. Filter for 'legal_templates' or your API endpoint
3. Check the Response tab to see what data is returned
`);
}

// Run analysis
analyzeTemplates();
checkNetworkRequests();
