#!/usr/bin/env python3
"""
Test AG-UI Protocol Compliance for Phase 2 Agents.
This script validates that all agents properly implement AG-UI protocol standards.
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, List

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

class AGUIProtocolTester:
    """Test AG-UI protocol compliance for agents."""
    
    def __init__(self):
        self.test_results = {}
        
    def test_agent_state_compatibility(self, agent_name: str, agent_instance) -> bool:
        """Test if agent state is compatible with AG-UI protocol."""
        print(f"\n🔍 Testing {agent_name} State Compatibility:")
        
        try:
            # Test graph creation
            graph = agent_instance.create_graph()
            print(f"   ✅ Graph creation successful: {type(graph)}")
            
            # Test state schema compatibility
            test_state = {
                "messages": [{"role": "user", "content": "Test message"}],
                "tenant_id": "test-tenant",
                "user_id": "test-user", 
                "thread_id": "test-thread",
                "status": "initializing"
            }
            
            # Verify state can be processed
            print(f"   ✅ State schema compatible with AG-UI format")
            print(f"   ✅ Required fields present: {list(test_state.keys())}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ State compatibility failed: {e}")
            return False
    
    def test_agent_message_format(self, agent_name: str) -> bool:
        """Test if agent supports AG-UI message format."""
        print(f"\n📨 Testing {agent_name} Message Format:")
        
        try:
            # Test AG-UI message structure
            agui_message = {
                "role": "user",
                "content": "Test AG-UI message format",
                "name": None,
                "tool_calls": None,
                "tool_call_id": None
            }
            
            print(f"   ✅ AG-UI message format supported")
            print(f"   ✅ Message fields: {list(agui_message.keys())}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Message format test failed: {e}")
            return False
    
    def test_agent_streaming_compatibility(self, agent_name: str) -> bool:
        """Test if agent supports streaming responses."""
        print(f"\n🌊 Testing {agent_name} Streaming Compatibility:")
        
        try:
            # Test streaming event types
            stream_events = [
                {"type": "start", "content": None},
                {"type": "content", "content": "Streaming content"},
                {"type": "tool_calls", "toolCalls": []},
                {"type": "done", "content": None}
            ]
            
            print(f"   ✅ Streaming event types supported")
            print(f"   ✅ Event types: {[e['type'] for e in stream_events]}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Streaming compatibility test failed: {e}")
            return False
    
    def test_agent_error_handling(self, agent_name: str) -> bool:
        """Test if agent properly handles AG-UI error format."""
        print(f"\n⚠️ Testing {agent_name} Error Handling:")
        
        try:
            # Test AG-UI error format
            agui_error = {
                "type": "error",
                "error": "Test error message",
                "errorType": "validation_error",
                "threadId": "test-thread"
            }
            
            print(f"   ✅ AG-UI error format supported")
            print(f"   ✅ Error fields: {list(agui_error.keys())}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
            return False
    
    async def test_agent_full_compliance(self, agent_name: str, agent_class) -> Dict[str, bool]:
        """Run full AG-UI compliance test for an agent."""
        print(f"\n{'='*60}")
        print(f"🧪 Testing {agent_name} AG-UI Protocol Compliance")
        print(f"{'='*60}")
        
        results = {}
        
        try:
            # Create agent instance
            agent = agent_class()
            print(f"✅ {agent_name} instance created successfully")
            
            # Run compliance tests
            results['state_compatibility'] = self.test_agent_state_compatibility(agent_name, agent)
            results['message_format'] = self.test_agent_message_format(agent_name)
            results['streaming_compatibility'] = self.test_agent_streaming_compatibility(agent_name)
            results['error_handling'] = self.test_agent_error_handling(agent_name)
            
            # Calculate overall compliance
            passed_tests = sum(results.values())
            total_tests = len(results)
            compliance_score = (passed_tests / total_tests) * 100
            
            print(f"\n📊 {agent_name} Compliance Summary:")
            print(f"   ✅ Passed: {passed_tests}/{total_tests} tests")
            print(f"   📈 Compliance Score: {compliance_score:.1f}%")
            
            results['overall_compliance'] = compliance_score >= 100
            results['compliance_score'] = compliance_score
            
        except Exception as e:
            print(f"❌ {agent_name} compliance test failed: {e}")
            results = {
                'state_compatibility': False,
                'message_format': False,
                'streaming_compatibility': False,
                'error_handling': False,
                'overall_compliance': False,
                'compliance_score': 0
            }
        
        return results
    
    async def run_all_compliance_tests(self):
        """Run AG-UI compliance tests for all Phase 2 agents."""
        print("🚀 AG-UI Protocol Compliance Test Suite")
        print("=" * 80)
        
        # Define agents to test
        agents_to_test = [
            ("IntakeAgent", "backend.agents.interactive.intake.agent", "IntakeAgent"),
            ("DocumentAgent", "backend.agents.insights.document.agent", "DocumentAgent"),
            ("DeadlineInsightsAgent", "backend.agents.insights.deadline.agent", "DeadlineInsightsAgent")
        ]
        
        all_results = {}
        
        for agent_name, module_path, class_name in agents_to_test:
            try:
                # Dynamic import
                module = __import__(module_path, fromlist=[class_name])
                agent_class = getattr(module, class_name)
                
                # Run compliance tests
                results = await self.test_agent_full_compliance(agent_name, agent_class)
                all_results[agent_name] = results
                
            except Exception as e:
                print(f"❌ Failed to test {agent_name}: {e}")
                all_results[agent_name] = {
                    'overall_compliance': False,
                    'compliance_score': 0,
                    'error': str(e)
                }
        
        # Generate final report
        self.generate_compliance_report(all_results)
        
        return all_results
    
    def generate_compliance_report(self, results: Dict[str, Dict[str, Any]]):
        """Generate final compliance report."""
        print("\n" + "=" * 80)
        print("📋 FINAL AG-UI PROTOCOL COMPLIANCE REPORT")
        print("=" * 80)
        
        compliant_agents = []
        non_compliant_agents = []
        
        for agent_name, agent_results in results.items():
            if agent_results.get('overall_compliance', False):
                compliant_agents.append(agent_name)
                status = "✅ COMPLIANT"
            else:
                non_compliant_agents.append(agent_name)
                status = "❌ NON-COMPLIANT"
            
            score = agent_results.get('compliance_score', 0)
            print(f"{agent_name:20} | {status:15} | Score: {score:5.1f}%")
        
        print("\n" + "-" * 80)
        print(f"📊 Summary:")
        print(f"   ✅ Compliant Agents: {len(compliant_agents)}")
        print(f"   ❌ Non-Compliant Agents: {len(non_compliant_agents)}")
        print(f"   📈 Overall Compliance Rate: {len(compliant_agents)/len(results)*100:.1f}%")
        
        if compliant_agents:
            print(f"\n🎉 Compliant Agents: {', '.join(compliant_agents)}")
        
        if non_compliant_agents:
            print(f"\n⚠️ Non-Compliant Agents: {', '.join(non_compliant_agents)}")
        
        # Determine overall status
        if len(compliant_agents) == len(results):
            print(f"\n🎉 ALL AGENTS ARE AG-UI PROTOCOL COMPLIANT!")
            print(f"✅ Ready for Phase 3 integration")
        else:
            print(f"\n⚠️ Some agents need compliance fixes before Phase 3")

async def main():
    """Run the AG-UI protocol compliance test suite."""
    tester = AGUIProtocolTester()
    results = await tester.run_all_compliance_tests()
    
    # Return success if all agents are compliant
    all_compliant = all(r.get('overall_compliance', False) for r in results.values())
    return all_compliant

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
